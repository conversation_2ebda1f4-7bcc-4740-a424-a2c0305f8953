package com.adins.esignhubjob.dataaccess.api;

import java.util.Date;

import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrMessageDeliveryReport;

public interface MessageDeliveryReportDao {
    void insertMessageDeliveryReport(TrMessageDeliveryReport report);
    void insertMessageDeliveryReportNewTrx(TrMessageDeliveryReport report);

    TrMessageDeliveryReport getMessageDeliveryReport(String vendorTrxNo, Date reportTime);
    TrMessageDeliveryReport getMessageDeliveryReportNewTrx(String vendorTrxNo, Date reportTime);

    TrMessageDeliveryReport getMessageDeliveryReportByTrxNo(String trxNo);
    TrMessageDeliveryReport getLatestWhatsAppMessageDeliveryReport(MsTenant tenant, String phone, String deliveryStatus);
}
