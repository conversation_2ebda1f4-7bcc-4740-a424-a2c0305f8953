package com.adins.esignhubjob.dataaccess.impl;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.VerifyDocumentKomdigiDao;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigi;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigiDetailSign;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigiDetailStamp;
import com.adins.util.Tools;
@Component
@Transactional
public class VerifyDocumentKomdigiDaoHbn extends BaseDaoHbn implements VerifyDocumentKomdigiDao {
    private static final String PARAM_STATUS = "status";
    private static final String PARAM_DATE = "date";

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertVerifyDocumentKomdigiNewTrx(TrVerifyDocumentKomdigi verifyDocumentKomdigi) {
        verifyDocumentKomdigi.setUsrCrt(Tools.maskData(verifyDocumentKomdigi.getUsrCrt()));
        this.managerDAO.insert(verifyDocumentKomdigi);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertVerifyDocumentKomdigiDetailSignNewTrx(TrVerifyDocumentKomdigiDetailSign verifyDocumentKomdigiDetailSign) {
        verifyDocumentKomdigiDetailSign.setUsrCrt(Tools.maskData(verifyDocumentKomdigiDetailSign.getUsrCrt()));
        this.managerDAO.insert(verifyDocumentKomdigiDetailSign);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertVerifyDocumentKomdigiDetailStampNewTrx(TrVerifyDocumentKomdigiDetailStamp verifyDocumentKomdigiDetailStamp) {
        verifyDocumentKomdigiDetailStamp.setUsrCrt(Tools.maskData(verifyDocumentKomdigiDetailStamp.getUsrCrt()));
        this.managerDAO.insert(verifyDocumentKomdigiDetailStamp);
    }

    @Override
    @Transactional(readOnly = true)
    public int countVerifyDocumentKomdigiByStatusAndDate(String status, Date date) {
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_STATUS, status);
        params.put(PARAM_DATE, date);
        String query = "SELECT COUNT(v) FROM TrVerifyDocumentKomdigi v " +
                "WHERE v.verifyStatus = :status " +
                "AND v.verifyDate = :date";
        Long count = (Long) this.managerDAO.selectOne(query, params);
        return count != null ? count.intValue() : 0;
    }
    
    @Override
    @Transactional(readOnly = true)
    public int countTotalVerifyDocumentKomdigiByDate(Date date) {
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_DATE, date);
        String query = "SELECT COUNT(v) FROM TrVerifyDocumentKomdigi v " +
                "WHERE v.verifyDate = :date";
        Long count = (Long) this.managerDAO.selectOne(query, params);
        return count != null ? count.intValue() : 0;
    }
}