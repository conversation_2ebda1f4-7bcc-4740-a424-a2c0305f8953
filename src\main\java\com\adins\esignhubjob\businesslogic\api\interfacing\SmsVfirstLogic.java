package com.adins.esignhubjob.businesslogic.api.interfacing;

import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.webservice.vfirst.SendSmsVfirstResponse;
import com.aliyun.fc.runtime.Context;

public interface SmsVfirstLogic {
    SendSmsVfirstResponse sendSms(MsTenant tenant, String phone, String message, Context context);
    SendSmsVfirstResponse sendSms(MsTenant tenant, String phone, String message, SigningProcessAuditTrailBean auditTrailBean, Context context);
}
