package com.adins.esignhubjob.model.webservice.halosis;

import com.google.gson.annotations.SerializedName;

public class HalosisLoginResponse {
    private String message;
	@SerializedName("refresh_token") private String refreshToken;
	@SerializedName("token_expired_at") private String tokenExpiredAt;
	@SerializedName("login_at") private String loginAt;
	
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getRefreshToken() {
		return refreshToken;
	}
	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}
	public String getTokenExpiredAt() {
		return tokenExpiredAt;
	}
	public void setTokenExpiredAt(String tokenExpiredAt) {
		this.tokenExpiredAt = tokenExpiredAt;
	}
	public String getLoginAt() {
		return loginAt;
	}
	public void setLoginAt(String loginAt) {
		this.loginAt = loginAt;
	}
}
