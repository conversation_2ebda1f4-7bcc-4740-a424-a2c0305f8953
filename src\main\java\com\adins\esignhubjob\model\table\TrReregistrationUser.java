package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "tr_reregistration_user")
public class TrReregistrationUser extends ActivatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	
	public static final String EMAIL_HBM = "email";
	public static final String PHONE_HBM = "phone";
	public static final String ID_NO_HBM = "idNo";
	
	private long idReregistratiounUser;
	private String email;
	private String phone;
	private String idNo;
	private MsVendor msVendor;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_reregistration_user", unique = true, nullable = false)
	public long getIdReregistratiounUser() {
		return idReregistratiounUser;
	}
	public void setIdReregistratiounUser(long idReregistratiounUser) {
		this.idReregistratiounUser = idReregistratiounUser;
	}
	
	@Column(name = "email", nullable = false, length = 80)
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	
	@Column(name = "phone", nullable = false, length = 20)
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	@Column(name = "id_no", nullable = false, length = 50)
	public String getIdNo() {
		return idNo;
	}
	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return msVendor;
	}
	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}
}
