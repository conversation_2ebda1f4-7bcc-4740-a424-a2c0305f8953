package com.adins.esignhubjob.dataaccess.api;

import java.util.List;

import com.adins.esignhubjob.model.table.MsBalancevendoroftenant;
import com.adins.esignhubjob.model.table.MsVendor;

public interface VendorDao {
    void insertVendor(MsVendor vendor);
    void deleteVendor(MsVendor vendor);
    MsVendor getVendorByCode(String code);
    MsVendor getVendorByCodeNewTrx(String code);
	List<MsBalancevendoroftenant> getListBalanceByVendorTenant(String tenantCode, String vendorCode);
}
