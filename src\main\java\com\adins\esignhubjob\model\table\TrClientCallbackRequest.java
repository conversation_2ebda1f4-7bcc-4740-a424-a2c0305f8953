package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_client_callback_request")
public class TrClientCallbackRequest extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	
	private long idClientCallbackRequest;
	private Short requestStatus;
	private Date requestDate;
	private MsTenant msTenant;
	private MsLov lovCallbackType;
	private AmMsuser amMsuser;
	private TrDocumentD trDocumentD;
	private String callbackRequest;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_client_callback_request", unique = true, nullable = false)
	public long getIdClientCallbackRequest() {
		return idClientCallbackRequest;
	}
	
	public void setIdClientCallbackRequest(long idClientCallbackRequest) {
		this.idClientCallbackRequest = idClientCallbackRequest;
	}
	
	@Column(name = "request_status", nullable = false)
	public Short getRequestStatus() {
		return requestStatus;
	}
	
	public void setRequestStatus(Short requestStatus) {
		this.requestStatus = requestStatus;
	}
	
	@Column(name = "request_date", nullable = false)
	public Date getRequestDate() {
		return requestDate;
	}
	
	public void setRequestDate(Date requestDate) {
		this.requestDate = requestDate;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return msTenant;
	}
	
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_callback_type")
	public MsLov getLovCallbackType() {
		return lovCallbackType;
	}
	
	public void setLovCallbackType(MsLov lovCallbackType) {
		this.lovCallbackType = lovCallbackType;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
	public AmMsuser getAmMsuser() {
		return amMsuser;
	}
	
	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return trDocumentD;
	}
	
	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}
	
	@Column(name = "callback_request", length = 200)
	public String getCallbackRequest() {
		return callbackRequest;
	}
	
	public void setCallbackRequest(String callbackRequest) {
		this.callbackRequest = callbackRequest;
	}
}
