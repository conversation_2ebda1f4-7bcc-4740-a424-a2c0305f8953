package com.adins.esignhubjob.model.table.custom;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.StringUtils;

@MappedSuperclass
public abstract class CreatableAndUpdatableEntity {
    protected String usrCrt;
	protected Date dtmCrt;
	protected String usrUpd;
	protected Date dtmUpd;

	@Column(name = "usr_crt", nullable = false, length = 36)
	public String getUsrCrt() {
		return this.usrCrt;
	}

	public void setUsrCrt(String usrCrt) {
		this.usrCrt = StringUtils.left(usrCrt, 36);
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "dtm_crt", nullable = false, length = 29)
	public Date getDtmCrt() {
		return this.dtmCrt;
	}

	public void setDtmCrt(Date dtmCrt) {
		this.dtmCrt = dtmCrt;
	}

	@Column(name = "usr_upd", length = 36)
	public String getUsrUpd() {
		return this.usrUpd;
	}

	public void setUsrUpd(String usrUpd) {
		this.usrUpd = StringUtils.left(usrUpd, 36);
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "dtm_upd", length = 29)
	public Date getDtmUpd() {
		return this.dtmUpd;
	}

	public void setDtmUpd(Date dtmUpd) {
		this.dtmUpd = dtmUpd;
	}
}
