package com.adins.esignhubjob.businesslogic.api.interfacing;

import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppRequestBean;
import com.aliyun.fc.runtime.Context;

public interface WhatsAppJatisLogic {
    void sendMessageAndCutBalance(JatisWhatsAppRequestBean request, Context context);
    void sendMessageAndCutBalance(JatisWhatsAppRequestBean request, SigningProcessAuditTrailBean auditTrailBean, Context context);
}
