# AD-INS API AS A SERVICE RELEASE NOTES

## v1.1.7-SNAPSHOT
2022-11-10 Rework : Setting timeout pada env variable dan kirim ref no ke api ocr, Penambahan FC Verifikasi Biometrik Dukcapil

## v1.1.6-SNAPSHOT
2022-10-12 Penambahan FC ID Forgery + OCR KTP

## v1.1.5-SNAPSHOT
2022-10-12 Penambahan FC Dukcapil Biometrik Dummy

## v1.1.5-SNAPSHOT
2022-10-12 Penambahan FC ID Forgery

## v1.1.4-SNAPSHOT
2022-10-07 Penambahan FC CDE

## v1.1.3-SNAPSHOT
2022-09-30 Penambahan Validasi Samsat

## v1.1.2-SNAPSHOT
2022-09-19 Penambahan FC RobotikSlik

## v1.1.1-SNAPSHOT
2022-08-09 Penambahan FC Liveness, Face Compare, dan Liveness + FaceCompare

## v1.1.0-SNAPSHOT
2022-07-19 Penambahan fitur untuk penyimpanan success atau false dan penambahan parameter tambahan

## v0.0.17-SNAPSHOT
2022-06-15 Logging duration call ke API upstream
2022-06-15 Trusts all certs saat call ke API Check threshold (issue PKIX saat hit api billing system)

## v0.0.16-SNAPSHOT
2022-06-03 (Pemotongan saldo selama response code 200 walaupun failed dan set timezone saat buka koneksi karena timezonenya tidak sesuai)

## v0.0.15-SNAPSHOT
2022-05-10 (Perubahan nilai timeout terkait API Rek Koran yang butuh waktu lama)

## v0.0.14-SNAPSHOT
2022-04-18 (Fix pemotongan saldo BPKB salah compare)

## v0.0.13-SNAPSHOT
2022-04-18 (Fix BPKB skip reading null pages)

## v0.0.12-SNAPSHOT
2022-04-13

* Add fc OCR BPKB (Combine 2 Page in 1 API)

## v0.0.11-SNAPSHOT
2022-03-07

* Add fc OCR KK

## v0.0.10-SNAPSHOT
2022-02-26

* Add fc OCR STNK

## v0.0.9-SNAPSHOT
2022-02-03

* Update fc OCR NPWP adjust request param field "file" to "img"

## v0.0.8-SNAPSHOT
2022-02-02

* Update fc OCR RK Mandiri PDF e-Banking, fc OCR RK BCA PDF e-Banking adjust response field "num_of_pages:" to "num_of_pages"

## v0.0.7-SNAPSHOT
2022-01-31

* Add fc OCR BPKB Hal2
* Add fc OCR BPKB Hal3
* Add fc OCR RK Mandiri PDF e-Banking
* Add fc OCR RK BCA PDF e-Banking
* Add fc OCR NPWP

## v0.0.6-SNAPSHOT
2022-01-25

* Ocr ktp change parameter key img1 to img

## v0.0.5-SNAPSHOT
2022-01-15

* Add fc PhoneActiveCheck
* Add fc PhoneActiveQuery
* Fix Get current balance still hardcoded with KTP