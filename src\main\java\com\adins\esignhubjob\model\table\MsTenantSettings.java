package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_tenant_settings")
public class MsTenantSettings extends CreatableAndUpdatableEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    private long idMsTenantSetting;
	private MsTenant msTenant;
	private MsLov lovSettingType;
	private String settingValue;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_tenant_setting", unique = true, nullable = false)
	public long getIdMsTenantSetting() {
		return idMsTenantSetting;
	}
	
	public void setIdMsTenantSetting(long idMsTenantSetting) {
		this.idMsTenantSetting = idMsTenantSetting;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return msTenant;
	}
	
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_setting_type", nullable = false)
	public MsLov getLovSettingType() {
		return lovSettingType;
	}
	
	public void setLovSettingType(MsLov lovSettingType) {
		this.lovSettingType = lovSettingType;
	}
	
	@Column(name = "setting_value", length = 128)
	public String getSettingValue() {
		return settingValue;
	}
	
	public void setSettingValue(String settingValue) {
		this.settingValue = settingValue;
	}
}
