package com.adins.esignhubjob.businesslogic.api;

import com.adins.constants.enums.NotificationSendingPoint;
import com.adins.constants.enums.NotificationType;
import com.adins.esignhubjob.model.table.MsTenant;

public interface TenantLogic {
    /**
	 * @param tenant
	 * @param sendingPoint
	 * @param emailService "1", "0", or null. Any other string will be considered as "0".
	 */
    NotificationType getNotificationType(MsTenant tenant, NotificationSendingPoint sendingPoint, String emailService);
}
