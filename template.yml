ROSTemplateFormatVersion: '2015-09-01'
Transform: 'Aliyun::Serverless-2018-04-03'
Resources:
  esign:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: eSignHub Job
    ReconOtpDigisign:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: 'com.adins.esignhubjob.job.ReconOtpDigisign::handleRequest'
        Runtime: java8
        Timeout: 60
        CodeUri: ./
        MemorySize: 128
        EnvironmentVariables:
          DIGI_RECON_URL: https://reconapi.tandatanganku.com/reconsile.html
          LOG_RECON_INSERT: 1
          RECON_DATE_FORMAT: dd-MM-yyyy HH:mm:ss
          ENV_OSS_ACCESSKEYSECRET: ******************************
          ENV_OSS_ACCESSKEYID: LTAI5tAk7HUDYPdqjUHXJ5VA
          ENV_OSS_BUCKET: esignhub-bucket2
          ENV_OSS_ENDPOINT: oss-ap-southeast-5.aliyuncs.com
          TZ: Asia/Jakarta
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods:
              - POST
    DeleteDocumentStampingJob:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: 'com.adins.esignhubjob.job.DeleteDocumentStampingJob::handleRequest'
        Runtime: java8
        Timeout: 60
        CodeUri: ./
        MemorySize: 128
        EnvironmentVariables:
          ENV_OSS_ACCESSKEYSECRET: ******************************
          ENV_OSS_ACCESSKEYID: LTAI5tAk7HUDYPdqjUHXJ5VA
          ENV_OSS_BUCKET: esignhub-bucket2
          ENV_OSS_ENDPOINT: oss-ap-southeast-5.aliyuncs.com
          TZ: Asia/Jakarta
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods:
              - POST