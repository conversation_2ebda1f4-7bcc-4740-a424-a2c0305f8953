package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.model.custom.cpanel.CpanelEmailBean;
import com.adins.esignhubjob.model.webservice.cpanel.CpanelListEmailResponse;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;
import com.aliyun.fc.runtime.StreamRequestHandler;
import com.google.gson.Gson;

import okhttp3.Credentials;
import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

// Tidak extends ke BaseJobHandler karena tidak butuh koneksi DB
public class DeleteCpanelEmailJob implements StreamRequestHandler {

    private static final String CPANEL_EMAIL_DOMAIN = System.getenv("CPANEL_EMAIL_DOMAIN");
    private static final String CPANEL_HEADER_AUTH_USERNAME = System.getenv("CPANEL_HEADER_AUTH_USERNAME");
    private static final String CPANEL_HEADER_AUTH_PASSWORD = System.getenv("CPANEL_HEADER_AUTH_PASSWORD");
    private static final String CPANEL_LIST_EMAIL_URL = System.getenv("CPANEL_LIST_EMAIL_URL");
    private static final String CPANEL_DELETE_EMAIL_URL = System.getenv("CPANEL_DELETE_EMAIL_URL");
    private static final String CPANEL_DELETE_FORWARDER_URL = System.getenv("CPANEL_DELETE_FORWARDER_URL");

    @Override
    public void handleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        try {
            List<CpanelEmailBean> emails = getListEmail(context);
            context.getLogger().info("Processing " + emails.size() + " cPanel email(s)");
            for (CpanelEmailBean emailBean : emails) {
                String fullEmailAddress = emailBean.getFile() + "@" + CPANEL_EMAIL_DOMAIN;

                if ("esign".equals(emailBean.getFile()) || "esign_default".equals(emailBean.getFile())) {
                    context.getLogger().info("Skip deleting " + fullEmailAddress);
                    continue;
                }

                deleteEmail(fullEmailAddress);
                deleteForwarder(fullEmailAddress);
            }

        } catch (Exception e) {
            String stackTrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stackTrace);
        }
    }

    private List<CpanelEmailBean> getListEmail(Context context) throws IOException {

        // Build URL
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("dir", "mail/" + CPANEL_EMAIL_DOMAIN);
        queryParams.put("type", "dir");
        String url = Tools.appendQueryParamUrl(CPANEL_LIST_EMAIL_URL, queryParams);

        // Build header
        String credential = Credentials.basic(CPANEL_HEADER_AUTH_USERNAME, CPANEL_HEADER_AUTH_PASSWORD);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, credential);
        Headers headers = Headers.of(headerMap);

        // Build body
        RequestBody body = RequestBody.create("", null);

        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(url)
            .post(body)
            .build();

        OkHttpClient client = new OkHttpClient();
        Response okHttpResponse = client.newCall(okHttpRequest).execute();
        String jsonResponse = okHttpResponse.body().string();
        Gson gson = new Gson();
        CpanelListEmailResponse cpanelListEmailResponse = gson.fromJson(jsonResponse, CpanelListEmailResponse.class);
        if (cpanelListEmailResponse.getErrors() != null) {
            String errorMessage = cpanelListEmailResponse.getErrors().toString();
            context.getLogger().error("cPanel get email list error: " + errorMessage);
            return new ArrayList<>();
        }

        return cpanelListEmailResponse.getData();
    }

    private void deleteEmail(String emailAddress) throws IOException {
        
        // Build URL
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("email", emailAddress);
        String completeUrl = Tools.appendQueryParamUrl(CPANEL_DELETE_EMAIL_URL, queryParams);

        // Build header
        String credential = Credentials.basic(CPANEL_HEADER_AUTH_USERNAME, CPANEL_HEADER_AUTH_PASSWORD);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, credential);
        Headers headers = Headers.of(headerMap);

        // Build body
        RequestBody body = RequestBody.create("", null);

        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(completeUrl)
            .post(body)
            .build();

        OkHttpClient client = new OkHttpClient();
        Response response = client.newCall(okHttpRequest).execute();
        response.close();
    }

    private void deleteForwarder(String emailAddress) throws IOException {

        // Build URL
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("address", emailAddress);
        queryParams.put("forwarder", "esign@" + CPANEL_EMAIL_DOMAIN);
        String completeUrl = Tools.appendQueryParamUrl(CPANEL_DELETE_FORWARDER_URL, queryParams);

        // Build header
        String credential = Credentials.basic(CPANEL_HEADER_AUTH_USERNAME, CPANEL_HEADER_AUTH_PASSWORD);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, credential);
        Headers headers = Headers.of(headerMap);

        // Build body
        RequestBody body = RequestBody.create("", null);

        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(completeUrl)
            .post(body)
            .build();

        OkHttpClient client = new OkHttpClient();
        Response response = client.newCall(okHttpRequest).execute();
        response.close();
    }
    
}
