package com.adins.util;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

public class ExcelUtils {

    private ExcelUtils() {
    }

    public static String getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell.getCellType() == CellType.NUMERIC) {
            // Convert ke long untuk hapus koma
            Double cellValue = cell.getNumericCellValue();
            return String.valueOf(cellValue.longValue());
        }

        return cell.getRichStringCellValue().getString();
    }
}
