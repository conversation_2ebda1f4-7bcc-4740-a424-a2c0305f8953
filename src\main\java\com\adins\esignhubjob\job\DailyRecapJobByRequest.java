package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import com.adins.esignhubjob.BaseJobHandler;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

public class DailyRecapJobByRequest extends BaseJobHandler {

    private static final String SCHEDULER = "FC Trigger";
    
	@Override
	public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
		String input = IOUtils.toString(inputStream);
        context.getLogger().info("Process daily recap date " + input);

		AuditContext auditContext = new AuditContext(SCHEDULER);
		logicFactory.getSchedulerLogic().dailyRecapUpdate(input,auditContext, context);
	}
    
}
