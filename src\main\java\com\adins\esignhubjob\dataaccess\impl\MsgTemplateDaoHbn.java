package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.MsgTemplateDao;
import com.adins.esignhubjob.model.table.MsMsgTemplate;

@Transactional
@Component
public class MsgTemplateDaoHbn extends BaseDaoHbn implements MsgTemplateDao {

    @Override
	public MsMsgTemplate getTemplateByCode(String templateCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("templateCode", templateCode);
		return this.managerDAO.selectOne("from MsMsgTemplate where templateCode=:templateCode", params);
	}

	@Override
	public MsMsgTemplate getTemplateByTypeAndCode(String templateType, String templateCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("templateType", templateType);
		params.put("templateCode", templateCode);
		
		return this.managerDAO.selectOne("from MsMsgTemplate where templateType = :templateType and templateCode=:templateCode ", params);
	}
}
