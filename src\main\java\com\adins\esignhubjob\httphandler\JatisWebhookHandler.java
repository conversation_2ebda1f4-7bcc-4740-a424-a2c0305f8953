package com.adins.esignhubjob.httphandler;

import java.io.IOException;
import java.util.Date;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.BaseHttpHandler;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrMessageDeliveryReport;
import com.adins.esignhubjob.model.webservice.jatis.JatisWhatsAppWebhookContent;
import com.adins.exceptions.Status;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

public class JatisWebhookHandler extends BaseHttpHandler {

    @Override
    public void doHandleRequest(HttpServletRequest request, HttpServletResponse response, Context context) throws IOException, ServletException {
        String input = IOUtils.toString(request.getInputStream());
        String requestClientIP = (String) request.getAttribute("FC_REQUEST_CLIENT_IP");
        context.getLogger().info("Webhook from " + requestClientIP + " received: " + input);
        JatisWhatsAppWebhookContent webhookContent = gson.fromJson(input, JatisWhatsAppWebhookContent.class);

        String trxNo = webhookContent.getXid();
        String vendorTrxNo = null;
        if (null != webhookContent.getEntry().get(0).getChanges().get(0).getValue().getStatuses().get(0).getConversation()) {
            vendorTrxNo = webhookContent.getEntry().get(0).getChanges().get(0).getValue().getStatuses().get(0).getConversation().getId();
        }
        
        String status = webhookContent.getEntry().get(0).getChanges().get(0).getValue().getStatuses().get(0).getStatus();

        String phoneNumber = webhookContent.getEntry().get(0).getChanges().get(0).getValue().getStatuses().get(0).getRecipientId();
        String formattedPhoneNumber = phoneNumber.replaceFirst("^62", "0");

        TrBalanceMutation balanceMutation = daoFactory.getBalanceMutationDao().getBalanceMutationByTrxNo(trxNo);

        if (!Constants.BALANCE_TYPE_CODE_WA.equals(balanceMutation.getMsLovByLovBalanceType().getCode())) {
             Status responseStatus = new Status(0, "Success");
             response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
             response.getWriter().write(gson.toJson(responseStatus));
             return;
        }

        TrMessageDeliveryReport existingReport = daoFactory.getMessageDeliveryReportDao().getMessageDeliveryReportByTrxNo(trxNo);
        MsLov lovCredentialType = existingReport.getMsLovCredentialType();
        MsLov lovSendingPoint = existingReport.getMsLovSendingPoint();

        MsTenant tenant = balanceMutation.getMsTenant();
        MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);
        MsLov lovMessageMedia = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_MESSAGE_MEDIA, Constants.LOV_CODE_MESSAGE_MEDIA_WA);
        MsLov lovMessageGateway = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_WA_GATEWAY, Constants.CODE_LOV_WA_GATEWAY_JATIS);

        TrMessageDeliveryReport report = new TrMessageDeliveryReport();
        report.setUsrCrt("FC_WEBHOOK_JATIS");
        report.setDtmCrt(new Date());
        report.setMsVendor(vendor);
        report.setMsTenant(tenant);
        report.setReportTime(new Date());
        report.setRequestTime(balanceMutation.getTrxDate());
        report.setRecipientDetail(formattedPhoneNumber);
        report.setTrxNo(trxNo);
        report.setVendorTrxNo(vendorTrxNo);
        report.setMsLov(lovMessageMedia);
        report.setMsLovMessageGateway(lovMessageGateway);
        report.setMsLovCredentialType(lovCredentialType);
        report.setMsLovSendingPoint(lovSendingPoint);
        report.setDeliveryStatus(getFormattedDeliveryStatus(status));
        daoFactory.getMessageDeliveryReportDao().insertMessageDeliveryReport(report);

        Status responseStatus = new Status(0, "Success");
        response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        response.getWriter().write(gson.toJson(responseStatus));

    }

    private String getFormattedDeliveryStatus(String originalStatus) {
        switch (originalStatus) {
            case "sent":
                return "1";
            case "delivered":
                return "3";
            case "read":
                return "4";
            default:
                return "2";
        }
    }
    
}
