package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_signing_process_audit_trail")
public class TrSigningProcessAuditTrail extends CreatableAndUpdatableEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private long idSigningProcessAuditTrail;
	private byte[] phoneNoBytea;
	private String hashedPhoneNo;
	private String email;
	private AmMsuser amMsUser;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private String notificationMedia;
	private String notificationVendor;
	private MsLov lovSendingPoint;
	private String otpCode;
	private MsLov lovProcessType;
	private String resultStatus;
	private String notes;
	private TrInvitationLink trInvitationLink;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_signing_process_audit_trail", unique = true, nullable = false)
    public long getIdSigningProcessAuditTrail() {
        return this.idSigningProcessAuditTrail;
    }

    public void setIdSigningProcessAuditTrail(long idSigningProcessAuditTrail) {
        this.idSigningProcessAuditTrail = idSigningProcessAuditTrail;
    }

    @Column(name = "phone_no_bytea")
    public byte[] getPhoneNoBytea() {
        return this.phoneNoBytea;
    }

    public void setPhoneNoBytea(byte[] phoneNoBytea) {
        this.phoneNoBytea = phoneNoBytea;
    }

    @Column(name = "hashed_phone_no", length = 200)
    public String getHashedPhoneNo() {
        return this.hashedPhoneNo;
    }

    public void setHashedPhoneNo(String hashedPhoneNo) {
        this.hashedPhoneNo = hashedPhoneNo;
    }

    @Column(name = "email", length = 80)
    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = true)
    public AmMsuser getAmMsUser() {
        return this.amMsUser;
    }

    public void setAmMsUser(AmMsuser amMsUser) {
        this.amMsUser = amMsUser;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = true)
    public MsTenant getMsTenant() {
        return this.msTenant;
    }

    public void setMsTenant(MsTenant msTenant) {
        this.msTenant = msTenant;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "psre_vendor_id", nullable = true)
    public MsVendor getMsVendor() {
        return this.msVendor;
    }

    public void setMsVendor(MsVendor msVendor) {
        this.msVendor = msVendor;
    }

    @Column(name = "notification_media", length = 5)
    public String getNotificationMedia() {
        return this.notificationMedia;
    }

    public void setNotificationMedia(String notificationMedia) {
        this.notificationMedia = notificationMedia;
    }

    @Column(name = "notification_vendor", length = 15)
    public String getNotificationVendor() {
        return this.notificationVendor;
    }

    public void setNotificationVendor(String notificationVendor) {
        this.notificationVendor = notificationVendor;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sending_point", nullable = true)
    public MsLov getLovSendingPoint() {
        return this.lovSendingPoint;
    }

    public void setLovSendingPoint(MsLov lovSendingPoint) {
        this.lovSendingPoint = lovSendingPoint;
    }

    @Column(name = "otp_code", length = 6)
    public String getOtpCode() {
        return this.otpCode;
    }

    public void setOtpCode(String otpCode) {
        this.otpCode = otpCode;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_process_type", nullable = false)
    public MsLov getLovProcessType() {
        return this.lovProcessType;
    }

    public void setLovProcessType(MsLov lovProcessType) {
        this.lovProcessType = lovProcessType;
    }

    @Column(name = "result_status", length = 1)
    public String getResultStatus() {
        return this.resultStatus;
    }

    public void setResultStatus(String resultStatus) {
        this.resultStatus = resultStatus;
    }

    @Column(name = "notes", length = 200)
    public String getNotes() {
        return this.notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_invitation_link", nullable = true)
    public TrInvitationLink getTrInvitationLink() {
        return this.trInvitationLink;
    }

    public void setTrInvitationLink(TrInvitationLink trInvitationLink) {
        this.trInvitationLink = trInvitationLink;
    }

}
