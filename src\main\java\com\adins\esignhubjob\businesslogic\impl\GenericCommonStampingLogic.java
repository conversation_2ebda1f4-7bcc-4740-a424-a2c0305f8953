package com.adins.esignhubjob.businesslogic.impl;

import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.constants.enums.StampingDocumentType;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.CommonStampingLogic;
import com.adins.esignhubjob.businesslogic.api.EmailSenderLogic;
import com.adins.esignhubjob.businesslogic.api.MessageTemplateLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.model.custom.adins.EmailAttachmentBean;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.custom.adins.StampingErrorDetailBean;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentHStampdutyError;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.esignhubjob.model.table.TrStampDuty;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

@Component
@Transactional
public class GenericCommonStampingLogic extends BaseLogic implements CommonStampingLogic {

    @Autowired private AliyunOssCloudStorageLogic cloudStorageLogic;
    @Autowired private MessageTemplateLogic messageTemplateLogic;
    @Autowired private EmailSenderLogic emailSenderLogic;

    // Default error email reciever if tenant receiver is not set
    private static final String[] ERROR_EMAIL_RECEIVERS = {"<EMAIL>"};
	private String username = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_USER);
	private String password = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_PASSWORD);
	private String namaDoc 	= System.getenv(Constants.ENV_VAR_EMETERAI_DEFAULT_DOCNAME);

	public static final long CONN_TIMEOUT = 30_000;
	public static final long READ_TIMEOUT = 300_000;
	public static final int STAMPED_DOC_CHECK_ATTEMPTS = 50;
	public static final long STAMPED_DOC_CHECK_DELAY = 100;

	private static final String ERROR_MSG_LOG = "Tenant %1$s, Kontrak %2$s, error count: %3$s, max error count: %4$s";
	private static final String INSUFFICIENT_BAL_MSG = "Insufficient Balance";

    @Override
    public void updateDocumentDMeteraiProcess(TrDocumentD document, String sdtProcess, Context context) {
        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();
        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, updating sdt_process to %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), sdtProcess));

        document.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
        document.setDtmUpd(new Date());
        document.setSdtProcess(sdtProcess);
        daoFactory.getDocumentDao().updateDocumentDNewTran(document);
    }

    @Override
    public String getDocumentFileToUpload(TrDocumentD document, Context context) {
        
        boolean documentNeedStampOnly = null == document.getTotalSign() || (Short.valueOf("0")).equals(document.getTotalSign());
        // Get non-sign document
        if (documentNeedStampOnly) {
            return getManuallyUploadedBaseStampDocument(document, context);
        }

        // Get signed PSrE document
        if (document.getTotalSign().equals(document.getTotalSigned())) {
            byte[] documentByteArray = cloudStorageLogic.getSignedDocument(document, context);
            return Base64.getEncoder().encodeToString(documentByteArray);
        }

        // Get base sign PSrE document
        byte[] documentByteArray = cloudStorageLogic.getBaseSignDocument(document, context);
        return Base64.getEncoder().encodeToString(documentByteArray);
    }

	@Override
    public String getDocumentFileToUploadPajakku(TrDocumentD document, Context context) {
        
		if (document.getTotalStamping() > 0) {
			byte[] documentByteArray = cloudStorageLogic.getStampedDocument(document, context);
			return Base64.getEncoder().encodeToString(documentByteArray);
		}

        return getDocumentFileToUpload(document, context);
    }

    private String getManuallyUploadedBaseStampDocument(TrDocumentD document, Context context) {
        boolean isPostpaid = "1".equals(document.getTrDocumentH().getIsPostpaidStampduty());
        if (isPostpaid) {
            String tenantCode = document.getMsTenant().getTenantCode();
			String refNumber = document.getTrDocumentH().getRefNumber();
			String documentId = document.getDocumentId();
			byte[] documentByteArray = cloudStorageLogic.getStampingDocument(tenantCode, refNumber, documentId, context);
			
			return Base64.getEncoder().encodeToString(documentByteArray);
        }

        byte[] documentByteArray = cloudStorageLogic.getManualStamp(document, context);
        return Base64.getEncoder().encodeToString(documentByteArray);
    }

	private String getManuallyUploadedBaseStampDocumentVida(TrDocumentD document, Context context) {
        byte[] documentByteArray = cloudStorageLogic.getManualStamp(document, context);
        return Base64.getEncoder().encodeToString(documentByteArray);
    }

	@Override
    public String getDocumentFileToUploadVida(TrDocumentD document, Context context) {

		if (document.getTotalStamping() > 0) {
			byte[] documentByteArray = cloudStorageLogic.getStampedDocument(document, context);
			return Base64.getEncoder().encodeToString(documentByteArray);
		}
        
        // Get signed PSrE document
        if (document.getTotalSign() > 0) {
            byte[] documentByteArray = cloudStorageLogic.getSignedDocument(document, context);
            return Base64.getEncoder().encodeToString(documentByteArray);
        }

		return getManuallyUploadedBaseStampDocumentVida(document, context);
    }

    @Override
    public String getDocumentCategoryForUpload(TrDocumentD document, Context context) {
        if (null == document.getMsPeruriDocType()) {
            return System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_DOC_CATEGORY);
        }

        return document.getMsPeruriDocType().getDocName();
    }

    @Override
    public boolean enoughSdtBalance(TrDocumentD document, int sdtNeeded, Context context) {

		boolean isPostpaid = "1".equals(document.getTrDocumentH().getIsPostpaidStampduty());

        MsLov balanceType = null;
		if (isPostpaid) {
			balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SDT_POSTPAID);
		} else {
			balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SDT);
		}
		
        MsTenant tenant = document.getMsTenant();
        MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        int currentBalance = daoFactory.getBalanceMutationDao().getCurrentBalance(tenant, vendor, balanceType).intValue();
        if (currentBalance >= sdtNeeded) {
            return true;
        }

        TrDocumentH documentH = document.getTrDocumentH();

        if (null != documentH.getEmailSaldo() && documentH.getEmailSaldo().intValue() >= 1) {
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, insufficient balance email has already been sent (Skip sending insufficient balance email)", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
            return false;
        }

        if (null == documentH.getEmailSaldo() || 0 == documentH.getEmailSaldo().intValue()) {
			documentH.setEmailSaldo((short) 1);
			documentH.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHeaderNewTran(documentH);
		}

        String gsCode = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_REMINDER_TOPUP_FREQ);
        int reminderLimit = Integer.parseInt(gsCode);
        String currentDate = Tools.formatDateToStringIn(new Date(), Constants.DATE_FORMAT);

        TrSchedulerJob job = daoFactory.getSchedulerJobDao().getSchedulerJobNewTrx(tenant, currentDate, Constants.CODE_LOV_JOB_TYPE_BALREM, Constants.BALANCE_TYPE_CODE_SDT);
        if (null != job && job.getMailReminderCount() >= reminderLimit) {
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, won't send reminder email (Daily limit reached)", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
            return false;
        }

        if (null == job) {

            MsLov schedulerType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.CODE_LOV_SCHED_TYPE_NON_SCHED);
            MsLov jobType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_JOB_TYPE, Constants.CODE_LOV_JOB_TYPE_BALREM);

            job = new TrSchedulerJob();
			job.setSchedulerStart(new Date());
			job.setSchedulerEnd(new Date());
			job.setDataProcessed(0L);
			job.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
			job.setDtmCrt(new Date());
			job.setMsLovByJobType(jobType);
			job.setMsLovBySchedulerType(schedulerType);
			job.setMsLovByBalanceType(balanceType);
			job.setMailReminderCount((short) 1);
			job.setMsTenant(document.getMsTenant());
			daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(job);

        } else {

            short reminderCount = job.getMailReminderCount();
			reminderCount += 1;
			job.setMailReminderCount(reminderCount);
			job.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
			job.setDtmUpd(new Date());
			daoFactory.getSchedulerJobDao().updateSchedulerJobNewTrx(job);

        }

        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, sending reminder email", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
        String[] recipient = tenant.getEmailReminderDest().split(",");
        String documentName = document.getMsDocTemplate() == null ? document.getDocumentName() : document.getMsDocTemplate().getDocTemplateName();
        sendInsufficientSdtBalanceEmail("generate e-Meterai", documentH.getRefNumber(), documentName, balanceType.getDescription(), currentBalance, recipient, context);
        return false;
    }

    private void sendInsufficientSdtBalanceEmail(String action, String refNo, String documentName, String balanceType, Integer saldo, String[] emailDest, Context context) {
		Map<String, Object> reminder = new HashMap<>();
		reminder.put("title", action);
		reminder.put("action", action);
		reminder.put("refNo", refNo);
		reminder.put("documentName", documentName);
		reminder.put("balanceType", balanceType);
		reminder.put("saldo", saldo);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("reminder", reminder);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_INSUFFICIENT_BAL, templateParameters);
		String[] recipient = emailDest;

		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());

        emailSenderLogic.sendEmail(emailBean, null, context);
	}

    @Override
    public void incrementAgreementStampingErrorCount(TrDocumentH documentH, TrDocumentD document, StampingErrorDetailBean errorDetailBean, Context context) {

        MsTenant tenant = documentH.getMsTenant();

        Exception e = errorDetailBean.getException();
        String errorMessage = null != e ? e.getLocalizedMessage() : errorDetailBean.getErrorMessage();
        String jsonRequest = errorDetailBean.getJsonRequest();
        String jsonResponse = errorDetailBean.getJsonResponse();
        String currentErrorLocation = errorDetailBean.getErrorLocation();
        String currentErrorLocationDetail = errorDetailBean.getErrorLocationDetail();
        String currentErrorMessage = StringUtils.isBlank(errorMessage) ? StringUtils.EMPTY : StringUtils.left(errorMessage, 1000);

		Long idDocumentH = documentH.getIdDocumentH();
		Long idDocumentD = (null == document) ? null : document.getIdDocumentD();

        TrDocumentHStampdutyError documentHSdtError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH, idDocumentD);
        int maxErrorCount = getStampingMaxErrorCount();

        if (null == documentHSdtError) {
            context.getLogger().info(String.format(ERROR_MSG_LOG, tenant.getTenantCode(), documentH.getRefNumber(), 1, maxErrorCount));

            documentHSdtError = new TrDocumentHStampdutyError();
			documentHSdtError.setTrDocumentH(documentH);
			documentHSdtError.setTrDocumentD(document);
			documentHSdtError.setErrorCount((short) 1);
			documentHSdtError.setErrorLocation(currentErrorLocation);
			documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
			documentHSdtError.setErrorMessage(currentErrorMessage);
			documentHSdtError.setIsEmailSent("0");
			documentHSdtError.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
			documentHSdtError.setDtmCrt(new Date());
			daoFactory.getDocumentDao().insertDocumentHStampdutyErrorNewTran(documentHSdtError);

            updateDocumentHMeteraiProcess(documentH, "62", context);
			return;
        }

        if (INSUFFICIENT_BAL_MSG.equals(currentErrorMessage)) {
			documentHSdtError.setErrorCount((short) maxErrorCount);
		} else {
			short errorCount = documentHSdtError.getErrorCount();
			errorCount += 1;
			documentHSdtError.setErrorCount(errorCount);
		}
		
		documentHSdtError.setErrorLocation(currentErrorLocation);
		documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
		documentHSdtError.setErrorMessage(currentErrorMessage);
		documentHSdtError.setDtmUpd(new Date());
		documentHSdtError.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
		daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
		
		context.getLogger().info(String.format(ERROR_MSG_LOG, tenant.getTenantCode(), documentH.getRefNumber(), documentHSdtError.getErrorCount(), maxErrorCount));
        
        if (documentHSdtError.getErrorCount() >= maxErrorCount) {
			String prevEmailFlag = documentHSdtError.getIsEmailSent();

            documentHSdtError.setIsEmailSent("1");
			documentHSdtError.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
			documentHSdtError.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);

			if ("1".equals(prevEmailFlag)) {
				sendAttachEmeteraiErrorEmail(documentHSdtError, e, jsonRequest, jsonResponse, context);
			}
			
            updateDocumentHMeteraiProcess(documentH, "61", context);
            return;
        }

        updateDocumentHMeteraiProcess(documentH, "62", context);
    }

	@Override
    public void incrementAgreementStampingErrorCountPajakku(TrDocumentH documentH, TrDocumentD document, StampingErrorDetailBean errorDetailBean, Context context) {

        MsTenant tenant = documentH.getMsTenant();

        Exception e = errorDetailBean.getException();
        String errorMessage = null != e ? e.getLocalizedMessage() : errorDetailBean.getErrorMessage();
        String jsonRequest = errorDetailBean.getJsonRequest();
        String jsonResponse = errorDetailBean.getJsonResponse();
        String currentErrorLocation = errorDetailBean.getErrorLocation();
        String currentErrorLocationDetail = errorDetailBean.getErrorLocationDetail();
        String currentErrorMessage = StringUtils.isBlank(errorMessage) ? StringUtils.EMPTY : StringUtils.left(errorMessage, 1000);

		Long idDocumentH = documentH.getIdDocumentH();
		Long idDocumentD = (null == document) ? null : document.getIdDocumentD();

        TrDocumentHStampdutyError documentHSdtError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH, idDocumentD);
        int maxErrorCount = getStampingMaxErrorCount();

        if (null == documentHSdtError) {
            context.getLogger().info(String.format(ERROR_MSG_LOG, tenant.getTenantCode(), documentH.getRefNumber(), 1, maxErrorCount));

            documentHSdtError = new TrDocumentHStampdutyError();
			documentHSdtError.setTrDocumentH(documentH);
			documentHSdtError.setTrDocumentD(document);
			documentHSdtError.setErrorCount((short) 1);
			documentHSdtError.setErrorLocation(currentErrorLocation);
			documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
			documentHSdtError.setErrorMessage(currentErrorMessage);
			documentHSdtError.setIsEmailSent("0");
			documentHSdtError.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
			documentHSdtError.setDtmCrt(new Date());
			daoFactory.getDocumentDao().insertDocumentHStampdutyErrorNewTran(documentHSdtError);

            updateDocumentHMeteraiProcess(documentH, "52", context);
			return;
        }

        if (INSUFFICIENT_BAL_MSG.equals(currentErrorMessage)) {
			documentHSdtError.setErrorCount((short) maxErrorCount);
		} else {
			short errorCount = documentHSdtError.getErrorCount();
			errorCount += 1;
			documentHSdtError.setErrorCount(errorCount);
		}
		
		documentHSdtError.setErrorLocation(currentErrorLocation);
		documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
		documentHSdtError.setErrorMessage(currentErrorMessage);
		documentHSdtError.setDtmUpd(new Date());
		documentHSdtError.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
		daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
		
		context.getLogger().info(String.format(ERROR_MSG_LOG, tenant.getTenantCode(), documentH.getRefNumber(), documentHSdtError.getErrorCount(), maxErrorCount));
        
        if (documentHSdtError.getErrorCount() >= maxErrorCount) {
			String prevEmailFlag = documentHSdtError.getIsEmailSent();

            documentHSdtError.setIsEmailSent("1");
			documentHSdtError.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
			documentHSdtError.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);

			if (!"1".equals(prevEmailFlag)) {
				sendAttachEmeteraiErrorEmail(documentHSdtError, e, jsonRequest, jsonResponse, context);
			}
			
            updateDocumentHMeteraiProcess(documentH, "51", context);
            return;
        }

        updateDocumentHMeteraiProcess(documentH, "52", context);
    }

	@Override
    public void incrementAgreementStampingErrorCountVida(TrDocumentH documentH, TrDocumentD document, StampingErrorDetailBean errorDetailBean, Context context) {

        MsTenant tenant = documentH.getMsTenant();

        Exception e = errorDetailBean.getException();
        String errorMessage = null != e ? e.getLocalizedMessage() : errorDetailBean.getErrorMessage();
        String jsonRequest = errorDetailBean.getJsonRequest();
        String jsonResponse = errorDetailBean.getJsonResponse();
        String currentErrorLocation = errorDetailBean.getErrorLocation();
        String currentErrorLocationDetail = errorDetailBean.getErrorLocationDetail();
        String currentErrorMessage = StringUtils.isBlank(errorMessage) ? StringUtils.EMPTY : StringUtils.left(errorMessage, 1000);

		Long idDocumentH = documentH.getIdDocumentH();
		Long idDocumentD = (null == document) ? null : document.getIdDocumentD();

        TrDocumentHStampdutyError documentHSdtError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH, idDocumentD);
        int maxErrorCount = getStampingMaxErrorCount();

        if (null == documentHSdtError) {
            context.getLogger().info(String.format(ERROR_MSG_LOG, tenant.getTenantCode(), documentH.getRefNumber(), 1, maxErrorCount));

            documentHSdtError = new TrDocumentHStampdutyError();
			documentHSdtError.setTrDocumentH(documentH);
			documentHSdtError.setTrDocumentD(document);
			documentHSdtError.setErrorCount((short) 1);
			documentHSdtError.setErrorLocation(currentErrorLocation);
			documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
			documentHSdtError.setErrorMessage(currentErrorMessage);
			documentHSdtError.setIsEmailSent("0");
			documentHSdtError.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
			documentHSdtError.setDtmCrt(new Date());
			daoFactory.getDocumentDao().insertDocumentHStampdutyErrorNewTran(documentHSdtError);

            updateDocumentHMeteraiProcess(documentH, "72", context);
			return;
        }

        if (INSUFFICIENT_BAL_MSG.equals(currentErrorMessage)) {
			documentHSdtError.setErrorCount((short) maxErrorCount);
		} else {
			short errorCount = documentHSdtError.getErrorCount();
			errorCount += 1;
			documentHSdtError.setErrorCount(errorCount);
		}
		
		documentHSdtError.setErrorLocation(currentErrorLocation);
		documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
		documentHSdtError.setErrorMessage(currentErrorMessage);
		documentHSdtError.setDtmUpd(new Date());
		documentHSdtError.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
		daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
		
		context.getLogger().info(String.format(ERROR_MSG_LOG, tenant.getTenantCode(), documentH.getRefNumber(), documentHSdtError.getErrorCount(), maxErrorCount));
        
        if (documentHSdtError.getErrorCount() >= maxErrorCount) {
			String prevEmailFlag = documentHSdtError.getIsEmailSent();

            documentHSdtError.setIsEmailSent("1");
			documentHSdtError.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
			documentHSdtError.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);

			if ("1".equals(prevEmailFlag)) {
				sendAttachEmeteraiErrorEmail(documentHSdtError, e, jsonRequest, jsonResponse, context);
			}
			
            updateDocumentHMeteraiProcess(documentH, "71", context);
            return;
        }

        updateDocumentHMeteraiProcess(documentH, "72", context);
    }

    private void sendAttachEmeteraiErrorEmail(TrDocumentHStampdutyError documentHSdtError, Exception e, String jsonRequest, String jsonResponse, Context context) {
		if (null == documentHSdtError) {
			return;
		}
		
		EmailAttachmentBean[] attachments = null;
		if (null != e) {
			byte[] stackTraceFile = buildStackTraceTextFile(e);
			String filename = buildStackTraceFileName(documentHSdtError.getErrorLocation(), documentHSdtError.getErrorLocationDetail());
			EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
			attachments = new EmailAttachmentBean[] {attachment};
		}
		
		TrDocumentH documentH = documentHSdtError.getTrDocumentH();
		TrDocumentD documentD = documentHSdtError.getTrDocumentD();
		String totalMeterai = null != documentD ? String.valueOf(documentD.getTotalMaterai()) : "0";
		String totalStamping = null != documentD ? String.valueOf(documentD.getTotalStamping()) : "0";
		String documentId = null != documentD ? documentD.getDocumentId() : "";
		
		Map<String, Object> kontrak = new HashMap<>();
		kontrak.put("refNumber", documentH.getRefNumber());
		kontrak.put("documentId", documentId);
		kontrak.put("totalMeterai", totalStamping + "/" + totalMeterai);
		kontrak.put("tenantName", documentH.getMsTenant().getTenantName());
		
		String errorLocation = StringUtils.isBlank(documentHSdtError.getErrorLocation()) ? "(error location unspecified)" : documentHSdtError.getErrorLocation();
		String errorLocationDetail = StringUtils.isBlank(documentHSdtError.getErrorLocationDetail()) ? "(error location detail unspecified)" : documentHSdtError.getErrorLocationDetail();
		String errorMsg = StringUtils.isBlank(documentHSdtError.getErrorMessage()) ? "(error message unspecified)" : documentHSdtError.getErrorMessage();
		String request = StringUtils.isBlank(jsonRequest) ? "(no request)" : jsonRequest;
		String response = StringUtils.isBlank(jsonResponse) ? "(no response)" : jsonResponse;
		
		Map<String, Object> error = new HashMap<>();
		error.put("errorTime", Tools.formatDateToStringIn(documentHSdtError.getDtmUpd(), Constants.DATE_TIME_FORMAT_SEC));
		error.put("errorCount", documentHSdtError.getErrorCount());
		error.put("errorLocation", errorLocation);
		error.put("errorLocationDetail", errorLocationDetail);
		error.put("errorMsg", errorMsg);
		error.put("request", request);
		error.put("response", response);
		
		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("kontrak", kontrak);
		templateParameters.put("error", error);
		
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_ATTACH_EMETERAI_ERROR, templateParameters);
		
		String[] receivers = getErrorEmailRecipients(documentH.getMsTenant());
		
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setSubject(template.getSubject());
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setTo(receivers);
		emailSenderLogic.sendEmail(emailInfo, attachments, context);
		context.getLogger().info("Stamping Error Email Sent.");
	}

    private String[] getErrorEmailRecipients(MsTenant tenant) {
		if (null == tenant) {
			return ERROR_EMAIL_RECEIVERS;
		}
		
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, AmGlobalKey.GS_ATTACH_SDT_ERROR_EMAIL_RECEIVER);
		if (null == tenantSettings) {
			return ERROR_EMAIL_RECEIVERS;
		}
		
		String emailReceivers = tenantSettings.getSettingValue();
		if (StringUtils.isBlank(emailReceivers)) {
			return ERROR_EMAIL_RECEIVERS;
		}
		
		String[] receivers = emailReceivers.split(";");
		if (StringUtils.isBlank(receivers[0])) {
			receivers = ERROR_EMAIL_RECEIVERS;
		}
		return receivers;
	}

    private byte[] buildStackTraceTextFile(Exception e) {
		String stackTrace = ExceptionUtils.getStackTrace(e);
		String base64 = Base64.getEncoder().encodeToString(stackTrace.getBytes());
		return Base64.getDecoder().decode(base64);
	}

    private String buildStackTraceFileName(String errorLocation, String errorLocationDetail) {
		String currentTime = Tools.formatDateToStringIn(new Date(), Constants.DATE_TIME_FORMAT_SEQ);
		StringBuilder filename = new StringBuilder()
			.append(StringUtils.upperCase(errorLocation)).append("_")
			.append(StringUtils.upperCase(errorLocationDetail)).append("_")
			.append(currentTime)
			.append(".txt");
		return filename.toString();
	}

    boolean isRepeatedError(TrDocumentHStampdutyError documentHSdtError, String currentErrorLocation, String currentErrorLocationDetail, String currentErrorMessage) {
        return documentHSdtError.getErrorLocation().equals(currentErrorLocation)
			&& documentHSdtError.getErrorLocationDetail().equals(currentErrorLocationDetail)
			&& documentHSdtError.getErrorMessage().equals(currentErrorMessage);
    }

	@Override
    public int getStampingMaxErrorCount() {
        String gsCode = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_ATTACH_SDT_MAX_REPEATED_ERROR_COUNT);
        if (StringUtils.isBlank(gsCode)) {
            return 3;
        }

        try {
            return Integer.parseInt(gsCode);
        } catch (Exception e) {
            return 3;
        }
    }

    @Override
    public void updateDocumentHMeteraiProcess(TrDocumentH documentH, String prosesMeterai, Context context) {
        MsTenant tenant = documentH.getMsTenant();

        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, updating proses_materai to %3$s", tenant.getTenantCode(), documentH.getRefNumber(), prosesMeterai));
		documentH.setProsesMaterai(new Short(prosesMeterai));
		documentH.setDtmUpd(new Date());
		documentH.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
		daoFactory.getDocumentDao().updateDocumentHeaderNewTran(documentH);
    }

	@Override
	public String getNotesForStampdutyBalanceMutation(TrDocumentD document, String reservedTrxNo, boolean isStampSuccessful, Context context) {
		StringBuilder notes = new StringBuilder();
		if (isStampSuccessful) {
			notes.append("Stamp success");
		} else {
			notes.append("Stamp failed");
		}

		notes
			.append(" ")
			.append(reservedTrxNo)
			.append(StringUtils.remove(document.getDocumentId(), '-'));
		return notes.toString();
	}

	@Override
	public boolean allDocumentsProcessed(List<TrDocumentD> documents, Context context) {
		if (CollectionUtils.isEmpty(documents)) {
			return true;
		}

		for (TrDocumentD document : documents) {

			MsTenant tenant = document.getMsTenant();
			TrDocumentH documentH = document.getTrDocumentH();
			context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, FINAL CHECK: Total stamped: %4$s/ %5$s, Current process: %6$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), document.getTotalStamping(), document.getTotalMaterai(), document.getSdtProcess()));
			
			if (!Constants.STEP_STAMPING_SDT_FIN.equals(document.getSdtProcess())) {
				return false;
			}

		}

		return true;
	}

	@Override
	public String getStampedDocument(TrDocumentD document, Context context) {
		byte[] documentByteArray = cloudStorageLogic.getStampedDocument(document, context);
		if (ArrayUtils.isEmpty(documentByteArray)) {
			 return null;
		}
		return Base64.getEncoder().encodeToString(documentByteArray);
	}

	@Override
	public String getIntegrationValue(TrDocumentD document, Context context) {
		MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(document.getMsTenant(), AmGlobalKey.GS_DMS_USERNAME);
		if (null == ts || StringUtils.isBlank(ts.getSettingValue())) {
			return null;
		}

		return Base64.getEncoder().encodeToString(ts.getSettingValue().getBytes());
	}

	@Override
	public long getStampingConnectionTimeout(StampingDocumentType documentType, Context context) {
		String timeoutValue = null;
		if (StampingDocumentType.PAYMENT_RECEIPT == documentType || StampingDocumentType.PAYMENT_RECEIPT_ON_PREM == documentType) {
			timeoutValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_PAYMENT_RECEIPT_PERURI_CONN_TIMEOUT);
		} else {
			timeoutValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_PERURI_CONN_TIMEOUT);
		}
		
		if (StringUtils.isBlank(timeoutValue)) {
			return CONN_TIMEOUT; 
		}
		
		try {
			return Long.parseLong(timeoutValue);
		} catch (Exception e) {
			return CONN_TIMEOUT;
		}
	}

	@Override
	public String getAccountUsername(TrDocumentH documentH, Context context) {
		if (!"1".equals(documentH.getIsPostpaidStampduty())) {
			return username;
		}
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCodeAndTenant(AmGlobalKey.GS_PAJAKKU_USERNAME, documentH.getMsTenant());
		if (null == gs) {
			return null;
		}
		return gs.getGsValue();
	}

	@Override
	public String getAccountPassword(TrDocumentH documentH, Context context) {
		if (!"1".equals(documentH.getIsPostpaidStampduty())) {
			return password;
		}
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCodeAndTenant(AmGlobalKey.GS_PAJAKKU_PASSWORD, documentH.getMsTenant());
		if (null == gs) {
			return null;
		}
		return gs.getGsValue();
	}
    
	@Override
	public String getStampDutyFee(Context context) {
		String nilaiMeteraiLunas =  daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_NILAI_METERAI_LUNAS);
		if (StringUtils.isBlank(nilaiMeteraiLunas)) {
			nilaiMeteraiLunas = Constants.PAJAKKU_NILAI_METERAI_LUNAS;
		}
		return nilaiMeteraiLunas;
	}

	@Override
	public String getNamaDocForGenerate(TrDocumentD document, Context context) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return document.getMsPeruriDocType().getDocName();
		}
		return namaDoc;
	}

	@Override
	public String getNoDocForGenerate(TrDocumentD document, Context context) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return document.getTrDocumentH().getRefNumber();
		}
		return document.getDocumentId();
	}

	@Override
	public String getTglDocForGenerate(TrDocumentD document, Context context) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return Tools.formatDateToStringIn(document.getRequestDate(), Constants.DATE_FORMAT);
		}
		return Tools.formatDateToStringIn(document.getCompletedDate(), Constants.DATE_FORMAT);
	}

	@Override
	public String getBalanceMutationNotesForGenerate(TrDocumentDStampduty docSdt, Context context) {
		TrDocumentD document = docSdt.getTrDocumentD();
		TrDocumentH documentH = document.getTrDocumentH();
		context.getLogger().info(String.format("Is manual upload : %1$s ", documentH.getIsManualUpload()));
		TrStampDuty sdt = docSdt.getTrStampDuty();
		
		if ("1".equals(documentH.getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return StringUtils.isNotBlank(docSdt.getNotes()) ? docSdt.getNotes() : sdt.getStampDutyNo();
		}
		return sdt.getStampDutyNo();
	}

	@Override
	public String getReasonForStamp(TrDocumentD document, Context context) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload())) {
			return System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_REASON2);
		}
		return System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_REASON);
	}

	@Override
	public String getOnPremStampDestination(TrDocumentD document, Context context) {
		return System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_DEST) + "/" + document.getDocumentId() + ".pdf";
	}

	@Override
	public String getOnPremSpecimenPath(TrDocumentDStampduty docSdt, Context context) {
		return System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_SPECIMEN) + "/" + docSdt.getTrStampDuty().getStampQr();
	}

	@Override
	public String getOnPremSource(TrDocumentD document, Context context) {
		return System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_STAMP_SOURCE) + "/" + document.getDocumentId() + ".pdf";
	}

	@Override
	public int getFileCheckAttempts(Context context) {
		String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_STAMPED_DOC_CHECK_ATTEMPTS);
		if (StringUtils.isBlank(gsValue)) {
			return STAMPED_DOC_CHECK_ATTEMPTS;
		}
		
		try {
			return Integer.valueOf(gsValue);
		} catch (Exception e) {
			return STAMPED_DOC_CHECK_ATTEMPTS;
		}
	}
	
	@Override
	public long getFileCheckDelay(Context context) {
		String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_STAMPED_DOC_CHECK_DELAY);
		if (StringUtils.isBlank(gsValue)) {
			return STAMPED_DOC_CHECK_DELAY;
		}
		
		try {
			return Long.valueOf(gsValue);
		} catch (Exception e) {
			return STAMPED_DOC_CHECK_DELAY;
		}
	}

	@Override
	public long getStampingReadTimeout(StampingDocumentType documentType, Context context) {
		String timeoutValue = null;
		if (StampingDocumentType.PAYMENT_RECEIPT == documentType || StampingDocumentType.PAYMENT_RECEIPT_ON_PREM == documentType) {
			timeoutValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_PAYMENT_RECEIPT_PERURI_READ_TIMEOUT);
		} else {
			timeoutValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_PERURI_READ_TIMEOUT);
		}
		
		if (StringUtils.isBlank(timeoutValue)) {
			return READ_TIMEOUT; 
		}
		
		try {
			return Long.parseLong(timeoutValue);
		} catch (Exception e) {
			return READ_TIMEOUT;
		}
	}
}
