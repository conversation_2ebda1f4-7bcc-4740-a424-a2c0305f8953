package com.adins.esignhubjob.dataaccess.api;

import java.math.BigInteger;
import java.util.Date;

public interface CommonDao {
    long nextSequenceTrBalanceMutationTrxNo();
    String recalculateBalanceMutation(String tenantCode, String vendorCode, String lovBalanceTypeCode, Date startDate, Date endDate);
    String incrementDocumentHTotalSignedNewTran(Long idDocumentH);

    /**
     * @return the number of sign request retried
     */
    BigInteger temporaryRetrySignVida();
    String hitFunctionInDatabase(String functionName, String functionParameter);
}
