package com.adins.esignhubjob.dataaccess;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.framework.persistence.dao.api.ManagerDAO;

@Component
@Transactional
public abstract class BaseDaoHbn {
  @Autowired protected ManagerDAO managerDAO;
  @Autowired protected PersonalDataEncryptionLogic personalDataEncLogic;
  
  public ManagerDAO getManagerDAO() {
    return managerDAO;
	}

}
