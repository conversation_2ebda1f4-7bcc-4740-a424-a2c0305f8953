package com.adins.esignhubjob.model.webservice.privygeneral;

import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralDocumentOwner;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralEmeteraiBean;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralUploadDocumentBean;
import com.google.gson.annotations.SerializedName;

public class PrivyGeneralUploadDocumentRequest {
    @SerializedName("reference_number") private String referenceNumber;
    @SerializedName("channel_id") private String channelId;
    @SerializedName("doc_process") private String docProcess;
    private String info;
    private Boolean visibility;
    @SerializedName("doc_owner") private PrivyGeneralDocumentOwner docOwner;
    private PrivyGeneralUploadDocumentBean document;
    @SerializedName("e_meterai") private PrivyGeneralEmeteraiBean emeterai;

    public String getReferenceNumber() {
        return this.referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getChannelId() {
        return this.channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getDocProcess() {
        return this.docProcess;
    }

    public void setDocProcess(String docProcess) {
        this.docProcess = docProcess;
    }

    public String getInfo() {
        return this.info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public Boolean isVisibility() {
        return this.visibility;
    }

    public Boolean getVisibility() {
        return this.visibility;
    }

    public void setVisibility(Boolean visibility) {
        this.visibility = visibility;
    }

    public PrivyGeneralDocumentOwner getDocOwner() {
        return this.docOwner;
    }

    public void setDocOwner(PrivyGeneralDocumentOwner docOwner) {
        this.docOwner = docOwner;
    }

    public PrivyGeneralUploadDocumentBean getDocument() {
        return this.document;
    }

    public void setDocument(PrivyGeneralUploadDocumentBean document) {
        this.document = document;
    }

    public PrivyGeneralEmeteraiBean getEmeterai() {
        return this.emeterai;
    }

    public void setEmeterai(PrivyGeneralEmeteraiBean emeterai) {
        this.emeterai = emeterai;
    }    

}
