package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SmsVfirstLogic;
import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.custom.vfirst.AddressSmsVfirstBean;
import com.adins.esignhubjob.model.custom.vfirst.SmsVfirstBean;
import com.adins.esignhubjob.model.custom.vfirst.UserVfirstBean;
import com.adins.esignhubjob.model.custom.vfirst.VFirstSmsProperties;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;
import com.adins.esignhubjob.model.webservice.vfirst.GenerateTokenVfirstResponse;
import com.adins.esignhubjob.model.webservice.vfirst.SendSmsVfirstApiResponse;
import com.adins.esignhubjob.model.webservice.vfirst.SendSmsVfirstRequest;
import com.adins.esignhubjob.model.webservice.vfirst.SendSmsVfirstResponse;
import com.adins.exceptions.EsignhubJobException;
import com.adins.exceptions.Status;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
public class GenericSmsVfirstLogic extends BaseLogic implements SmsVfirstLogic {

    @Autowired private PersonalDataEncryptionLogic encryptionLogic;

    private static final String VFIRST_ERR_CODE_28681 = "28681";
    private static final String VFIRST_ERR_CODE_28682 = "28682";

	@Override
	public SendSmsVfirstResponse sendSms(MsTenant tenant, String phone, String message, Context context) {
		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
        VFirstSmsProperties smsProperties = getTenantSmsProperties(tenant, context);

        try {
            String token = getTenantSmsToken(tenant, smsProperties, context);
            return sendSmsVfirst(smsProperties, tenant, phone, message, trxNo, token, context);
        } catch (Exception e) {

            String[] msgArr = e.getMessage().split("@");
            context.getLogger().error("Send SMS VFirst error: " + msgArr[0]);
            context.getLogger().error(ExceptionUtils.getStackTrace(e));

            SendSmsVfirstResponse response = new SendSmsVfirstResponse();
            response.setGuid(msgArr.length > 1 ? msgArr[1] : null);
			response.setErrorMsg(msgArr[0]);
			response.setTrxNo(trxNo);

            if (VFIRST_ERR_CODE_28681.equals(msgArr[0])) {
                response.setErrorCode(VFIRST_ERR_CODE_28681);
            } else if (VFIRST_ERR_CODE_28682.equals(msgArr[0])) {
                response.setErrorCode(VFIRST_ERR_CODE_28682);
            }

            return response;

        }
        
	}

    private VFirstSmsProperties getTenantSmsProperties(MsTenant tenant, Context context) {
        MsTenantSettings usernameSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_VFRIST_USERNAME");
		if (null == usernameSettings) {
			return getApplicationSmsProperties(tenant, context);
		}
		
		MsTenantSettings passwordSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_VFRIST_PASSWORD");
		if (null == passwordSettings) {
			return getApplicationSmsProperties(tenant, context);
		}
		
		MsTenantSettings senderSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_VFRIST_SENDER");
		if (null == senderSettings) {
			return getApplicationSmsProperties(tenant, context);
		}
		
        context.getLogger().debug("Tenant " + tenant.getTenantCode() + " uses VFirst SMS properties from tenant settings");
		return new VFirstSmsProperties(usernameSettings.getSettingValue(), passwordSettings.getSettingValue(), senderSettings.getSettingValue());
    }

    private VFirstSmsProperties getApplicationSmsProperties(MsTenant tenant, Context context) {
		context.getLogger().debug("Tenant " + tenant.getTenantCode() + " uses VFirst SMS properties from environment variables");
		return new VFirstSmsProperties(System.getenv(Constants.ENV_VAR_VFIRST_USERNAME), System.getenv(Constants.ENV_VAR_VFIRST_PASSWORD), System.getenv(Constants.ENV_VAR_VFIRST_SENDER));
	}

    private String getTenantSmsToken(MsTenant tenant, VFirstSmsProperties smsProperties, Context context) throws IOException {
        
        MsTenantSettings tokenSetting = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, Constants.LOV_CODE_TENANT_SETTING_VFIRST_TOKEN_VALUE);
        MsTenantSettings tokenExpirySetting = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, Constants.LOV_CODE_TENANT_SETTING_VFIRST_TOKEN_EXPIRED_DATE);

        // Generate token & insert new tenant setting
        if (null == tokenSetting || null == tokenExpirySetting) {

            GenerateTokenVfirstResponse response = generateToken(tenant, smsProperties, context);
            String token = response.getToken();
            String expiredDate = response.getExpiryDate();

            MsLov lovTokenSetting = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TENANT_SETTING_TYPE, Constants.LOV_CODE_TENANT_SETTING_VFIRST_TOKEN_VALUE);
            MsLov lovTokenExpirySetting = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TENANT_SETTING_TYPE, Constants.LOV_CODE_TENANT_SETTING_VFIRST_TOKEN_EXPIRED_DATE);

            tokenSetting = new MsTenantSettings();
            tokenSetting.setMsTenant(tenant);
            tokenSetting.setLovSettingType(lovTokenSetting);
            tokenSetting.setSettingValue(token);
            tokenSetting.setUsrCrt(context.getRequestId());
            tokenSetting.setDtmCrt(new Date());
            daoFactory.getTenantSettingsDao().insertTenantSettingsNewTrx(tokenSetting);

            tokenExpirySetting = new MsTenantSettings();
            tokenExpirySetting.setMsTenant(tenant);
            tokenExpirySetting.setLovSettingType(lovTokenExpirySetting);
            tokenExpirySetting.setSettingValue(expiredDate);
            tokenExpirySetting.setUsrCrt(context.getRequestId());
            tokenExpirySetting.setDtmCrt(new Date());
            daoFactory.getTenantSettingsDao().insertTenantSettingsNewTrx(tokenExpirySetting);

            return token;
        }

        // Generate token & update tenant setting value
        if (StringUtils.isBlank(tokenSetting.getSettingValue()) || StringUtils.isBlank(tokenExpirySetting.getSettingValue())) {
            GenerateTokenVfirstResponse response = generateToken(tenant, smsProperties, context);
            String token = response.getToken();
            String expiredDate = response.getExpiryDate();

            tokenSetting.setSettingValue(token);
            tokenSetting.setUsrUpd(context.getRequestId());
            tokenSetting.setDtmUpd(new Date());
            daoFactory.getTenantSettingsDao().updateTenantSettingsNewTrx(tokenSetting);

            tokenExpirySetting.setSettingValue(expiredDate);
            tokenExpirySetting.setUsrUpd(context.getRequestId());
            tokenExpirySetting.setDtmUpd(new Date());
            daoFactory.getTenantSettingsDao().updateTenantSettingsNewTrx(tokenExpirySetting);

            return token;
        }

        // Expired, generate token, update tenant setting value
        if (isTenantTokenExpired(tokenExpirySetting)) {
            GenerateTokenVfirstResponse response = generateToken(tenant, smsProperties, context);
            String token = response.getToken();
            String expiredDate = response.getExpiryDate();

            tokenSetting.setSettingValue(token);
            tokenSetting.setUsrUpd(context.getRequestId());
            tokenSetting.setDtmUpd(new Date());
            daoFactory.getTenantSettingsDao().updateTenantSettingsNewTrx(tokenSetting);

            tokenExpirySetting.setSettingValue(expiredDate);
            tokenExpirySetting.setUsrUpd(context.getRequestId());
            tokenExpirySetting.setDtmUpd(new Date());
            daoFactory.getTenantSettingsDao().updateTenantSettingsNewTrx(tokenExpirySetting);

            return token;
        }

        return tokenSetting.getSettingValue();
    }

    private GenerateTokenVfirstResponse generateToken(MsTenant tenant, VFirstSmsProperties tenantSmsProperties, Context context) throws IOException {
        
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.buildBasicAuthorization(tenantSmsProperties.getUsername(), tenantSmsProperties.getPassword()));
        Headers headers = Headers.of(headerMap);

        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(20L, TimeUnit.SECONDS)
            .readTimeout(60L, TimeUnit.SECONDS)
            .build();

        RequestBody body = RequestBody.create("", null);

        Request request = new Request.Builder()
            .url(System.getenv(Constants.ENV_VAR_VFIRST_GENERATE_TOKEN_URL))
            .post(body)
            .headers(headers)
            .build();

        Response response = client.newCall(request).execute();
        String jsonResponse = response.body().string();
        context.getLogger().info(String.format("Tenant %1$s, generate VFirst token response: %2$s", tenant.getTenantCode(), jsonResponse));

        GenerateTokenVfirstResponse generateResponse = gson.fromJson(jsonResponse, GenerateTokenVfirstResponse.class);
        String expiryDate = generateResponse.getExpiryDate();
        String indoDateString = convertToGmt(expiryDate);

        if (StringUtils.isNotBlank(generateResponse.getToken()) || StringUtils.isNotBlank(generateResponse.getExpiryDate())) {
			generateResponse.setExpiryDate(indoDateString);
			return generateResponse;
		}

        throw new EsignhubJobException(jsonResponse);
    }

    private String convertToGmt(String dateString) {

	    Date date = Tools.formatStringToDate(dateString, "yyyy-MM-dd HH:mm:ss");
	    
        Calendar calendar = Calendar.getInstance();
	    calendar.setTime(date);
	    calendar.add(Calendar.MINUTE, 30);
	    calendar.add(Calendar.HOUR, 1);

	    Date indoDate = calendar.getTime();
	    return Tools.formatDateToStringIn(indoDate, "yyyy-MM-dd HH:mm:ss");
	}

    private boolean isTenantTokenExpired(MsTenantSettings tokenExpirySetting) {
        if (null == tokenExpirySetting) {
            return true;
        }

        if (StringUtils.isBlank(tokenExpirySetting.getSettingValue())) {
            return true;
        }

        LocalDate localDate = LocalDate.now();
        String expiryDateString = tokenExpirySetting.getSettingValue().substring(0, 10);
        LocalDate expiryDate = LocalDate.parse(expiryDateString);

        Duration diff = Duration.between(localDate.atStartOfDay(), expiryDate.atStartOfDay());
        long diffDays = diff.toDays();

        String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode("TOKEN_DATE_DIFF_SMS");
        long dateDiff = Long.parseLong(gsValue);
        return diffDays < dateDiff;
    }

    private SendSmsVfirstResponse sendSmsVfirst(VFirstSmsProperties properties, MsTenant tenant, String phone, String message, long trxNo, String token, Context context) throws IOException {

        String to =  Character.compare(phone.charAt(0), '0') == 0 ? "62" + phone.substring(1) : phone;

        AddressSmsVfirstBean addressBean = new AddressSmsVfirstBean();
        addressBean.setFrom(properties.getSender());
        addressBean.setTag(System.getenv(Constants.ENV_VAR_VFIRST_TAG) + " " + tenant.getTenantCode());
        addressBean.setTo(to);
        addressBean.setSeq("1");

        List<AddressSmsVfirstBean> listAddress = new ArrayList<>();
        listAddress.add(addressBean);

        SmsVfirstBean smsVfirstBean = new SmsVfirstBean();
        smsVfirstBean.setCoding("1");
        smsVfirstBean.setUdh("0");
        smsVfirstBean.setText(message);
        smsVfirstBean.setProperty("0");
        smsVfirstBean.setId(String.valueOf(trxNo));
        smsVfirstBean.setAddress(listAddress);

        List<SmsVfirstBean> listSms = new ArrayList<>();
        listSms.add(smsVfirstBean);

        SendSmsVfirstRequest request = new SendSmsVfirstRequest();
        request.setVer(System.getenv(Constants.ENV_VAR_VFIRST_VERSION));
        request.setUser(new UserVfirstBean());
        request.setSms(listSms);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.buildBearerToken(token));
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        Headers headers = Headers.of(headerMap);

        String jsonRequest = gson.toJson(request);
        context.getLogger().info(String.format("Send %1$s SMS VFirst request: %2$s", tenant.getTenantCode(), jsonRequest));
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(System.getenv(Constants.ENV_VAR_VFIRST_SEND_SMS_URL))
            .post(body)
            .build();

        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(20L, TimeUnit.SECONDS)
            .readTimeout(60L, TimeUnit.SECONDS)
            .build();

        Response okHttpResponse = client.newCall(okHttpRequest).execute();
        String jsonResponse = okHttpResponse.body().string();
        context.getLogger().info(String.format("Send %1$s SMS VFirst response code: %2$s, body: %3$s", tenant.getTenantCode(), okHttpResponse.code(), jsonResponse));
        
        SendSmsVfirstApiResponse apiResponse = gson.fromJson(jsonResponse, SendSmsVfirstApiResponse.class);
        if (apiResponse.getMessageack().getErr() != null || apiResponse.getMessageack().getGuid().getError() != null) {
            String errMessage = apiResponse.getMessageack().getErr() != null ? apiResponse.getMessageack().getErr().getDesc() : String.valueOf(apiResponse.getMessageack().getGuid().getError().getCode());
            if (StringUtils.isNumeric(errMessage) && !errMessage.equals("0")) {
                // vfirst_error_code@vfirst_guid
                errMessage = errMessage + "@" + apiResponse.getMessageack().getGuid().getGuid();
            }
            throw new EsignhubJobException(errMessage);
        }

        SendSmsVfirstResponse response = new SendSmsVfirstResponse();
		Status status = new Status();
		status.setCode(0);
		status.setMessage("Success");
		response.setStatus(status);
        response.setGuid(apiResponse.getMessageack().getGuid().getGuid());
        return response;
    }

    private TrSigningProcessAuditTrail insertAuditTrail(SigningProcessAuditTrailBean auditTrailBean, boolean sendSuccess, String notes, Context context) {
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(encryptionLogic.encryptFromString(auditTrailBean.getPhone()));
		trail.setHashedPhoneNo(Tools.getHashedString(auditTrailBean.getPhone()));
		trail.setEmail(StringUtils.upperCase(auditTrailBean.getEmail()));
		trail.setAmMsUser(auditTrailBean.getUser());
		trail.setMsTenant(auditTrailBean.getTenant());
		trail.setMsVendor(auditTrailBean.getVendorPsre());
		trail.setNotificationMedia("SMS");
		trail.setNotificationVendor(daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SMS_GATEWAY, Constants.CODE_LOV_SMS_GATEWAY_VFIRST).getDescription());
		trail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		trail.setLovProcessType(auditTrailBean.getLovProcessType());
		trail.setResultStatus(sendSuccess ? "1" : "0");
		trail.setNotes(notes);
		trail.setUsrCrt(context.getRequestId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

		if (CollectionUtils.isEmpty(auditTrailBean.getDocumentDs())) {
			return trail;
		}

		for (TrDocumentD documentD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail detail = new TrSigningProcessAuditTrailDetail();
			detail.setSigningProcessAuditTrail(trail);
			detail.setTrDocumentD(documentD);
			detail.setUsrCrt(context.getRequestId());
			detail.setDtmCrt(new Date());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTrx(detail);
		}

		return trail;
	}

    @Override
    public SendSmsVfirstResponse sendSms(MsTenant tenant, String phone, String message, SigningProcessAuditTrailBean auditTrailBean, Context context) {
        long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
        VFirstSmsProperties smsProperties = getTenantSmsProperties(tenant, context);

        try {

            String token = getTenantSmsToken(tenant, smsProperties, context);
            SendSmsVfirstResponse response = sendSmsVfirst(smsProperties, tenant, phone, message, trxNo, token, context);
            insertAuditTrail(auditTrailBean, true, null, context);
            return response;

        } catch (Exception e) {

            String[] msgArr = e.getMessage().split("@");
            context.getLogger().error("Send SMS VFirst error: " + msgArr[0]);
            context.getLogger().error(ExceptionUtils.getStackTrace(e));

            SendSmsVfirstResponse response = new SendSmsVfirstResponse();
            response.setGuid(msgArr.length > 1 ? msgArr[1] : null);
			response.setErrorMsg(msgArr[0]);
			response.setTrxNo(trxNo);

            if (VFIRST_ERR_CODE_28681.equals(msgArr[0])) {
                response.setErrorCode(VFIRST_ERR_CODE_28681);
            } else if (VFIRST_ERR_CODE_28682.equals(msgArr[0])) {
                response.setErrorCode(VFIRST_ERR_CODE_28682);
            }

            insertAuditTrail(auditTrailBean, false, msgArr[0], context);

            return response;

        }
    }
    
}
