package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.HashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import com.adins.util.Tools;
import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.custom.komdigi.DocumentKomdigiDetailSign;
import com.adins.esignhubjob.model.custom.komdigi.DocumentKomdigiDetailStamp;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigi;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigiDetailSign;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigiDetailStamp;
import com.adins.esignhubjob.model.webservice.komdigi.DocumentVerifyKomdigiRequest;
import com.adins.esignhubjob.model.webservice.komdigi.DocumentVerifyKomdigiResponse;
import com.adins.exceptions.EsignhubJobException;
import com.aliyun.fc.runtime.Context;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class VerifyDocumentKomdigiJob extends BaseJobHandler {
    private static final String AUDIT = "JOB_VERIFY_DOCUMENT_KOMDIGI";
    private static final String EMAIL_PIC_ESIGN = "EMAIL_PIC_ESIGN";
    private static final String KOMDIGI_VERIFY_URL_ENV = "KOMDIGI_VERIFY_URL";

    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_FAILED = "failed";
    private static final String DATE_FORMAT_SIMPLE = "yyyy-MM-dd";
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        Date onlyDate = Tools.changeDateFormat(new Date(), DATE_FORMAT_SIMPLE);
        Date session1Start = DateUtils.addHours(onlyDate, 8);
        Date session1End = DateUtils.addHours(onlyDate, 11);
        Date session2Start = DateUtils.addHours(onlyDate, 12);
        Date session2End = DateUtils.addHours(onlyDate, 15);
        Date session3Start = DateUtils.addHours(onlyDate, 18);
        Date session3End = DateUtils.addHours(onlyDate, 21);

        long startTime = System.currentTimeMillis();
        context.getLogger().info("Start Komdigi document verification job");

        List<MsTenant> activeTenants = daoFactory.getTenantDao().getActiveTenants();
        if (CollectionUtils.isEmpty(activeTenants)) {
            context.getLogger().info("No active tenants found");
            return;
        }

        List<Date[]> sessions = new ArrayList<>();
        sessions.add(new Date[] { session1Start, session1End });
        sessions.add(new Date[] { session2Start, session2End });
        sessions.add(new Date[] { session3Start, session3End });

        for (int i = 0; i < sessions.size(); i++) {
            int sessionNumber = i + 1;
            Date startHour = sessions.get(i)[0];
            Date endHour = sessions.get(i)[1];
            context.getLogger().info("Processing session " + sessionNumber + " (" + startHour + "-" + endHour + ")");

            for (MsTenant tenant : activeTenants) {
                processDocumentsForTenant(tenant, startHour, endHour, sessionNumber, context);
            }
        }

        sendDailySummaryEmail(context);
        logProcessDuration("Komdigi document verification job", startTime);
        context.getLogger().info("Komdigi document verification job completed");
    }

    private void processDocumentsForTenant(MsTenant tenant, Date startHour, Date endHour, int sessionNumber, Context context) {
        List<TrDocumentD> documents = daoFactory.getDocumentDao().getDocumentsToVerifyKomdigiByTenantAndSessionNewTrx(
                tenant.getIdMsTenant(), startHour, endHour);
        if (CollectionUtils.isEmpty(documents)) {
            context.getLogger().info("No documents found to verify for tenant: " + tenant.getTenantCode()
                    + " in session " + sessionNumber);
            return;
        }
        context.getLogger().info(
                "Processing documents for tenant: " + tenant.getTenantCode() + 
                ", total documents: " + documents.size());

        for (TrDocumentD document : documents) {
            try {
                verifyDocument(document, context);
            } catch (Exception e) {
                saveVerificationError(document, e, context);
            }
        }
    }

    private void verifyDocument(TrDocumentD document, Context context) throws IOException {
        context.getLogger().info("Verifying document: " + document.getDocumentId());

        String base64Document = logicFactory.getCommonStampingLogic().getStampedDocument(document, context);
        if (StringUtils.isBlank(base64Document)) {
            context.getLogger().error("Failed to get stamped document for " + document.getDocumentId());
            throw new EsignhubJobException("Failed to get stamped document");
        }

        DocumentVerifyKomdigiResponse verificationResult = sendDocumentForVerification(document, base64Document,
                context);

        saveVerificationResult(document, verificationResult);

        context.getLogger().info("Verification completed for document: " + document.getDocumentId());
    }

    private DocumentVerifyKomdigiResponse sendDocumentForVerification(TrDocumentD document,
            String base64Document, Context context) throws IOException {
        String documentId = document.getDocumentId();
        String refNumber = document.getTrDocumentH().getRefNumber();
        String url = System.getenv(KOMDIGI_VERIFY_URL_ENV);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        Headers headers = Headers.of(headerMap);

        DocumentVerifyKomdigiRequest requestBody = new DocumentVerifyKomdigiRequest();
        requestBody.setFilename(documentId + ".pdf");
        requestBody.setAgrmntNo(refNumber);
        requestBody.setBase64(base64Document);
        String jsonRequest = gson.toJson(requestBody);
        context.getLogger().info("Sending verification request for document: " + documentId);
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        Request okHttpRequest = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(body)
                .build();

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .build();

        long startTime = System.currentTimeMillis();
        Response okHttResponse = client.newCall(okHttpRequest).execute();
        logProcessDuration("API verification call for document " + documentId, startTime);

        String responseBody = okHttResponse.body().string();
        // Check Response Komdigi
        if (!okHttResponse.isSuccessful()) {
            String errorDetails = "API verification failed - Status: " + okHttResponse.code() +
                    ", Message: " + okHttResponse.message() +
                    ", Body: " + responseBody;
            context.getLogger().error(errorDetails);
            throw new EsignhubJobException(errorDetails);
        }

        DocumentVerifyKomdigiResponse response = gson.fromJson(responseBody, DocumentVerifyKomdigiResponse.class);
        response.setJsonResponse(responseBody);
        return response;
    }

    private void saveVerificationResult(TrDocumentD document, DocumentVerifyKomdigiResponse verificationResult) {
        Date onlyDate = Tools.changeDateFormat(new Date(), DATE_FORMAT_SIMPLE);
        TrVerifyDocumentKomdigi verifyDocument = new TrVerifyDocumentKomdigi();
        verifyDocument.setTrDocumentD(document);
        verifyDocument.setVerifyDate(onlyDate);

        String verifyStatus = "0"; 
        String documentStatus = STATUS_FAILED;
        String statusValue = verificationResult.getStatus();
        if (STATUS_SUCCESS.equalsIgnoreCase(statusValue)) {
            verifyStatus = "1"; 
            documentStatus = STATUS_SUCCESS;
        }
        verifyDocument.setVerifyStatus(verifyStatus);
        verifyDocument.setDocumentStatus(documentStatus);
        verifyDocument.setJsonResponse(verificationResult.getJsonResponse());
        verifyDocument.setUsrCrt(AUDIT);
        verifyDocument.setDtmCrt(new Date());

        daoFactory.getVerifyDocumentKomdigiDao().insertVerifyDocumentKomdigiNewTrx(verifyDocument);

        if ("1".equals(verifyStatus)) {
            for (DocumentKomdigiDetailSign sign : verificationResult.getDetailSign()) {
                saveSignatureDetails(sign, verifyDocument);
            }
            for (DocumentKomdigiDetailStamp stamp : verificationResult.getDetailStamp()) {
                saveStampDetails(stamp, verifyDocument);
            }
        }
    }

    private void saveSignatureDetails(DocumentKomdigiDetailSign signatures, TrVerifyDocumentKomdigi verifyDocument) {
        TrVerifyDocumentKomdigiDetailSign signDetail = new TrVerifyDocumentKomdigiDetailSign();
        signDetail.setTrVerifyDocumentKomdigi(verifyDocument);
        signDetail.setSignedBy(signatures.getSignedBy());
        signDetail.setSignReason(signatures.getReason());
        signDetail.setSignStatus(signatures.getSignature());
        signDetail.setSignLocation(signatures.getLocation());
        signDetail.setSignCertifIdentity(signatures.getIdentity());
        signDetail.setSignLtv(signatures.getLtv());

        String trusted = signatures.getTrusted();
        signDetail.setSignTrusted(convertBooleanToOneZero(trusted));
        signDetail.setSignIssuer(signatures.getIssuer());
        signDetail.setSignChainLevel(signatures.getChainLevel());

        String timestamp = signatures.getStampingTime();
        signDetail.setSignTimestamp(parseTimestamp(timestamp));
        signDetail.setUsrCrt(AUDIT);
        signDetail.setDtmCrt(new Date());
        daoFactory.getVerifyDocumentKomdigiDao().insertVerifyDocumentKomdigiDetailSignNewTrx(signDetail);
    }

    private void saveStampDetails(DocumentKomdigiDetailStamp stamps, TrVerifyDocumentKomdigi verifyDocument) {
        TrVerifyDocumentKomdigiDetailStamp stampDetail = new TrVerifyDocumentKomdigiDetailStamp();
        stampDetail.setTrVerifyDocumentKomdigi(verifyDocument);
        stampDetail.setStampDutySn(stamps.getSerialNumber());
        stampDetail.setStampDutyReason(stamps.getReason());
        stampDetail.setStampDutySignatureStatus(stamps.getSignature());
        stampDetail.setStampDutyLocation(stamps.getLocation());
        stampDetail.setStampDutyCertifIdentity(stamps.getIdentity());
        stampDetail.setStampDutyLtv(stamps.getLtv());
        String trusted = stamps.getTrusted();
        stampDetail.setStampDutyTrusted(convertBooleanToOneZero(trusted));
        stampDetail.setStampDutyIssuer(stamps.getIssuer());
        stampDetail.setStampDutyChainLevel(stamps.getChainLevel());
        String timestamp = stamps.getStampingTime();
        stampDetail.setStampDutyTimestamp(parseTimestamp(timestamp));
        stampDetail.setUsrCrt(AUDIT);
        stampDetail.setDtmCrt(new Date());
        daoFactory.getVerifyDocumentKomdigiDao().insertVerifyDocumentKomdigiDetailStampNewTrx(stampDetail);
    }

    private void saveVerificationError(TrDocumentD document, Exception e, Context context) {
        String documentId = document.getDocumentId();
        Date onlyDate = Tools.changeDateFormat(new Date(), DATE_FORMAT_SIMPLE);
        TrVerifyDocumentKomdigi verifyDocument = new TrVerifyDocumentKomdigi();
        verifyDocument.setTrDocumentD(document);
        verifyDocument.setVerifyDate(onlyDate);
        verifyDocument.setVerifyStatus("0"); 
        verifyDocument.setDocumentStatus(STATUS_FAILED);

        String errorMessage = e.getMessage();
        verifyDocument.setJsonResponse("{\"error\":\"" + errorMessage + "\"}");
        verifyDocument.setStackTrace(ExceptionUtils.getStackTrace(e));
        verifyDocument.setUsrCrt(AUDIT);
        verifyDocument.setDtmCrt(new Date());
        daoFactory.getVerifyDocumentKomdigiDao().insertVerifyDocumentKomdigiNewTrx(verifyDocument);
        context.getLogger().info("Verification error saved for document: " + documentId);
    }

    private Date parseTimestamp(String timestamp) {
        Date date = null;
        date = Tools.formatStringToDate(timestamp, DATE_FORMAT);
        return date;
    }

    private String convertBooleanToOneZero(String booleanValue) {
        booleanValue = "true".equalsIgnoreCase(booleanValue) ? "1" : "0";
        return booleanValue;
    }

    private void sendDailySummaryEmail(Context context) {
        String emailRecipients = null;
        Date onlyDate = Tools.changeDateFormat(new Date(), DATE_FORMAT_SIMPLE);
        emailRecipients = daoFactory.getGeneralSettingDao().getGsValueByCode(EMAIL_PIC_ESIGN);

        int totalVerified = daoFactory.getVerifyDocumentKomdigiDao().countTotalVerifyDocumentKomdigiByDate(onlyDate);
        int successCount = daoFactory.getVerifyDocumentKomdigiDao().countVerifyDocumentKomdigiByStatusAndDate("1",
                onlyDate);
        int failedCount = daoFactory.getVerifyDocumentKomdigiDao().countVerifyDocumentKomdigiByStatusAndDate("0",
                onlyDate);

        Map<String, Object> params = new HashMap<>();
        params.put("date", new SimpleDateFormat("dd-MM-yyyy").format(new Date()));
        params.put("totalVerified", String.valueOf(totalVerified));
        params.put("successCount", String.valueOf(successCount));
        params.put("failedCount", String.valueOf(failedCount));

        MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(
                Constants.TEMPLATE_SEND_VERIFY_DOCUMENT_KOMDIGI, params);

        EmailInformationBean emailInfo = new EmailInformationBean();
        emailInfo.setSubject(template.getSubject());
        emailInfo.setBodyMessage(template.getBody());
        emailInfo.setTo(emailRecipients.split(";"));
        logicFactory.getEmailSenderLogic().sendEmail(emailInfo, null, context);

        context.getLogger().info("Successfully sent verification summary email");
    }
}