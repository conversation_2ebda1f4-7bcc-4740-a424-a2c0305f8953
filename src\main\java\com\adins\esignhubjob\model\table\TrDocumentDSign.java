package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_document_d_sign")
public class TrDocumentDSign extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	private long idDocumentDSign;
	private AmMsuser amMsuser;
	private MsLov msLovByLovAutosign;
	private MsLov msLovByLovSignType;
	private MsLov msLovByLovSignerType;
	private TrDocumentD trDocumentD;
	private Date sentDate;
	private Date signDate;
	private String signLocation;
	private Integer signPage;
	private Short seqNo;
	private String vidaSignLocation;
	private String privySignLocation;
	private String vendorRegistrationId;
	private String poaId;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_document_d_sign", unique = true, nullable = false)
	public long getIdDocumentDSign() {
		return this.idDocumentDSign;
	}

	public void setIdDocumentDSign(long idDocumentDSign) {
		this.idDocumentDSign = idDocumentDSign;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_autosign")
	public MsLov getMsLovByLovAutosign() {
		return this.msLovByLovAutosign;
	}

	public void setMsLovByLovAutosign(MsLov msLovByLovAutosign) {
		this.msLovByLovAutosign = msLovByLovAutosign;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sign_type")
	public MsLov getMsLovByLovSignType() {
		return this.msLovByLovSignType;
	}

	public void setMsLovByLovSignType(MsLov msLovByLovSignType) {
		this.msLovByLovSignType = msLovByLovSignType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_signer_type")
	public MsLov getMsLovByLovSignerType() {
		return this.msLovByLovSignerType;
	}

	public void setMsLovByLovSignerType(MsLov msLovByLovSignerType) {
		this.msLovByLovSignerType = msLovByLovSignerType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d", nullable = false)
	public TrDocumentD getTrDocumentD() {
		return this.trDocumentD;
	}

	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "sent_date", length = 29)
	public Date getSentDate() {
		return this.sentDate;
	}

	public void setSentDate(Date sentDate) {
		this.sentDate = sentDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "sign_date", length = 29)
	public Date getSignDate() {
		return this.signDate;
	}

	public void setSignDate(Date signDate) {
		this.signDate = signDate;
	}

	@Column(name = "sign_location", length = 250)
	public String getSignLocation() {
		return this.signLocation;
	}

	public void setSignLocation(String signLocation) {
		this.signLocation = signLocation;
	}

	@Column(name = "sign_page")
	public Integer getSignPage() {
		return this.signPage;
	}

	public void setSignPage(Integer signPage) {
		this.signPage = signPage;
	}

	@Column(name = "seq_no")
	public Short getSeqNo() {
		return this.seqNo;
	}

	public void setSeqNo(Short seqNo) {
		this.seqNo = seqNo;
	}

	@Column(name = "vida_sign_location", length = 100)
	public String getVidaSignLocation() {
		return vidaSignLocation;
	}

	public void setVidaSignLocation(String vidaSignLocation) {
		this.vidaSignLocation = vidaSignLocation;
	}

	@Column(name = "privy_sign_location", length = 100)
	public String getPrivySignLocation() {
		return privySignLocation;
	}

	public void setPrivySignLocation(String privySignLocation) {
		this.privySignLocation = privySignLocation;
	}

	@Column(name = "vendor_registration_id", length = 50)
	public String getVendorRegistrationId() {
		return vendorRegistrationId;
	}

	public void setVendorRegistrationId(String vendorRegistrationId) {
		this.vendorRegistrationId = vendorRegistrationId;
	}

	@Column(name = "poa_id", length = 40)
	public String getPoaId() {
		return poaId;
	}

	public void setPoaId(String poaId) {
		this.poaId = poaId;
	}
}
