package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_error_history")
public class TrErrorHistory extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	public static final String ID_ERROR_HISTORY_HBM = "idErrorHistory";
	public static final String RERUN_PROCESS_HBM = "rerunProcess";
	
	private long idErrorHistory;
	private MsLov msLov;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private String refNumber;
	private String custName;
	private String office;
	private String region;
	private String businessLine;
	private String errorType;
	private Date errorDate;
	private String errorMessage;
	private String custIdno;
	private String spsName;
	private String spsIdno;
	private String grtName;
	private String grtIdno;
	private String rerunProcess;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_error_history", unique = true, nullable = false)
	public long getIdErrorHistory() {
		return idErrorHistory;
	}

	public void setIdErrorHistory(long idErrorHistory) {
		this.idErrorHistory = idErrorHistory;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_modul", nullable = false)
	public MsLov getMsLov() {
		return msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor")
	public MsVendor getMsVendor() {
		return msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@Column(name = "ref_number", length = 50)
	public String getRefNumber() {
		return refNumber;
	}

	public void setRefNumber(String refNumber) {
		this.refNumber = refNumber;
	}

	@Column(name = "cust_name", length = 80)
	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	@Column(name = "office", length = 80)
	public String getOffice() {
		return office;
	}

	public void setOffice(String office) {
		this.office = office;
	}

	@Column(name = "region", length = 50)
	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	@Column(name = "business_line", length = 50)
	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	@Column(name = "error_type", length = 20)
	public String getErrorType() {
		return errorType;
	}

	public void setErrorType(String errorType) {
		this.errorType = errorType;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "error_date", length = 13)
	public Date getErrorDate() {
		return errorDate;
	}

	public void setErrorDate(Date errorDate) {
		this.errorDate = errorDate;
	}

	@Column(name = "error_message", length = 300)
	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	@Column(name = "cust_idno", length = 50)
	public String getCustIdno() {
		return custIdno;
	}

	public void setCustIdno(String custIdno) {
		this.custIdno = custIdno;
	}

	@Column(name = "sps_name", length = 80)
	public String getSpsName() {
		return spsName;
	}

	public void setSpsName(String spsName) {
		this.spsName = spsName;
	}

	@Column(name = "sps_idno", length = 50)
	public String getSpsIdno() {
		return spsIdno;
	}

	public void setSpsIdno(String spsIdno) {
		this.spsIdno = spsIdno;
	}

	@Column(name = "grt_name", length = 80)
	public String getGrtName() {
		return grtName;
	}

	public void setGrtName(String grtName) {
		this.grtName = grtName;
	}

	@Column(name = "grt_idno", length = 50)
	public String getGrtIdno() {
		return grtIdno;
	}

	public void setGrtIdno(String grtIdno) {
		this.grtIdno = grtIdno;
	}

	@Column(name = "rerun_process", length = 2)
	public String getRerunProcess() {
		return rerunProcess;
	}

	public void setRerunProcess(String rerunProcess) {
		this.rerunProcess = rerunProcess;
	}
}
