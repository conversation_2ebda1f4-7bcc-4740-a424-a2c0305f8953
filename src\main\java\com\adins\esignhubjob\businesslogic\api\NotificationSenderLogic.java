package com.adins.esignhubjob.businesslogic.api;

import com.adins.esignhubjob.model.custom.adins.RegisterExternalRequestBean;
import com.adins.esignhubjob.model.custom.adins.UserBean;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.aliyun.fc.runtime.Context;

public interface NotificationSenderLogic {

    // Send successful privy verification notif
    void sendSuccessfulPrivyVerifEmailExternal(MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifEmailInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifSmsVfirstExternal(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifSmsVfirstInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifSmsJatisExternal(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifSmsJatisInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifWhatsAppExternal(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifWhatsAppInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifHalosisWhatsAppExternal(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);
    void sendSuccessfulPrivyVerifHalosisWhatsAppInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context);

    // Send failed privy verification notif
    void sendFailedPrivyVerifEmailExternal(RegisterExternalRequestBean registerRequest, String rejectMessage, Context context);
    void sendFailedPrivyVerifEmailInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context);
    void sendFailedPrivyVerifSmsVfirstExternal(MsTenant tenant, RegisterExternalRequestBean registerRequest, String rejectMessage, Context context);
    void sendFailedPrivyVerifSmsVfirstInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context);
    void sendFailedPrivyVerifSmsJatisExternal(MsTenant tenant, RegisterExternalRequestBean registerRequest, String rejectMessage, Context context);
    void sendFailedPrivyVerifSmsJatisInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context);
    void sendFailedPrivyVerifWhatsAppExternal(MsTenant tenant, RegisterExternalRequestBean registerRequest, String rejectMessage, Context context);
    void sendFailedPrivyVerifWhatsAppInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context);
    void sendFailedPrivyVerifHalosisWhatsAppExternal(MsTenant tenant, RegisterExternalRequestBean registerRequest, String rejectMessage, Context context);
    void sendFailedPrivyVerifHalosisWhatsAppInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context);

}