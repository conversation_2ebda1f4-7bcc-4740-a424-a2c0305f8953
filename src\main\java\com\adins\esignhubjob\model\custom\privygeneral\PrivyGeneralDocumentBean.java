package com.adins.esignhubjob.model.custom.privygeneral;

import java.util.List;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralDocumentBean {
    private String name;
    private String status;
    @SerializedName("document_token") private String documentToken;
    @SerializedName("reference_number") private String referenceNumber;
    private List<PrivyGeneralSignerBean> signers;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDocumentToken() {
        return this.documentToken;
    }

    public void setDocumentToken(String documentToken) {
        this.documentToken = documentToken;
    }

    public String getReferenceNumber() {
        return this.referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public List<PrivyGeneralSignerBean> getSigners() {
        return this.signers;
    }

    public void setSigners(List<PrivyGeneralSignerBean> signers) {
        this.signers = signers;
    }

}
