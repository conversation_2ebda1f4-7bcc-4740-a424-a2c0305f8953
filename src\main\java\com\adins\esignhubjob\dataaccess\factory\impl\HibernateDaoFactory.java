package com.adins.esignhubjob.dataaccess.factory.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esignhubjob.dataaccess.api.BackgroundProcessFailDao;
import com.adins.esignhubjob.dataaccess.api.BalanceMutationDao;
import com.adins.esignhubjob.dataaccess.api.BalanceTopUpDao;
import com.adins.esignhubjob.dataaccess.api.BalancevendoroftenantDao;
import com.adins.esignhubjob.dataaccess.api.ClientCallbackRequestDao;
import com.adins.esignhubjob.dataaccess.api.CommonDao;
import com.adins.esignhubjob.dataaccess.api.DailyRecapDao;
import com.adins.esignhubjob.dataaccess.api.DocumentDao;
import com.adins.esignhubjob.dataaccess.api.DocumentSigningRequestDao;
import com.adins.esignhubjob.dataaccess.api.EmailDao;
import com.adins.esignhubjob.dataaccess.api.GeneralSettingDao;
import com.adins.esignhubjob.dataaccess.api.HousekeepingOssDao;
import com.adins.esignhubjob.dataaccess.api.InvitationLinkDao;
import com.adins.esignhubjob.dataaccess.api.JobCheckRegisterStatusDao;
import com.adins.esignhubjob.dataaccess.api.JobResultDao;
import com.adins.esignhubjob.dataaccess.api.JobUpdatePsreIdDao;
import com.adins.esignhubjob.dataaccess.api.LovDao;
import com.adins.esignhubjob.dataaccess.api.MessageDeliveryReportDao;
import com.adins.esignhubjob.dataaccess.api.MsgTemplateDao;
import com.adins.esignhubjob.dataaccess.api.NotificationtypeoftenantDao;
import com.adins.esignhubjob.dataaccess.api.OfficeDao;
import com.adins.esignhubjob.dataaccess.api.PeruriDocTypeDao;
import com.adins.esignhubjob.dataaccess.api.ProcessAutosignBmDao;
import com.adins.esignhubjob.dataaccess.api.RegistrationLivenessResultDao;
import com.adins.esignhubjob.dataaccess.api.RoleDao;
import com.adins.esignhubjob.dataaccess.api.SchedulerJobDao;
import com.adins.esignhubjob.dataaccess.api.SigningProcessAuditTrailDao;
import com.adins.esignhubjob.dataaccess.api.StampDutyDao;
import com.adins.esignhubjob.dataaccess.api.TenantDao;
import com.adins.esignhubjob.dataaccess.api.TenantSettingsDao;
import com.adins.esignhubjob.dataaccess.api.UrlForwarderDao;
import com.adins.esignhubjob.dataaccess.api.UserDao;
import com.adins.esignhubjob.dataaccess.api.UseroftenantDao;
import com.adins.esignhubjob.dataaccess.api.VendorDao;
import com.adins.esignhubjob.dataaccess.api.VendorRegisteredUserDao;
import com.adins.esignhubjob.dataaccess.api.VendoroftenantDao;
import com.adins.esignhubjob.dataaccess.api.VerifyDocumentKomdigiDao;
import com.adins.esignhubjob.dataaccess.factory.api.DaoFactory;


@Component
public class HibernateDaoFactory implements DaoFactory {

    @Autowired private BalanceMutationDao balanceMutationDao;
    @Autowired private BalancevendoroftenantDao balancevendoroftenantDao;
    @Autowired private ClientCallbackRequestDao clientCallbackRequestDao;
    @Autowired private CommonDao commonDao;
    @Autowired private DailyRecapDao dailyRecapDao;
    @Autowired private DocumentDao documentDao;
    @Autowired private DocumentSigningRequestDao documentSigningRequestDao;
    @Autowired private EmailDao emailDao;
    @Autowired private GeneralSettingDao generalSettingDao;
    @Autowired private JobCheckRegisterStatusDao jobCheckRegisterStatusDao;
    @Autowired private JobResultDao jobResultDao;
    @Autowired private JobUpdatePsreIdDao jobUpdatePsreIdDao;
    @Autowired private LovDao lovDao;
    @Autowired private MessageDeliveryReportDao messageDeliveryReportDao;
    @Autowired private MsgTemplateDao msgTemplateDao;
    @Autowired private OfficeDao officeDao;
    @Autowired private PeruriDocTypeDao peruriDocTypeDao;
    @Autowired private RegistrationLivenessResultDao registrationLivenessResultDao;
    @Autowired private RoleDao roleDao;
    @Autowired private SchedulerJobDao schedulerJobDao;
    @Autowired private UrlForwarderDao urlForwarderDao;
    @Autowired private TenantDao tenantDao;
    @Autowired private UserDao userDao;
    @Autowired private UseroftenantDao useroftenantDao;
    @Autowired private VendorDao vendorDao;
    @Autowired private VendoroftenantDao vendoroftenantDao;
    @Autowired private VendorRegisteredUserDao vendorRegisteredUserDao;
    @Autowired private TenantSettingsDao tenantSettingsDao;
    @Autowired private NotificationtypeoftenantDao notificationtypeoftenantDao;
    @Autowired private InvitationLinkDao invitationLinkDao;
    @Autowired private ProcessAutosignBmDao processAutosignBmDao;
    @Autowired private StampDutyDao stampDutyDao;
    @Autowired private SigningProcessAuditTrailDao signingProcessAuditTrailDao;
    @Autowired private HousekeepingOssDao housekeepingOssDao;
    @Autowired private BalanceTopUpDao balanceTopUpDao;
    @Autowired private BackgroundProcessFailDao backgroundProcessFailDao;
    @Autowired private VerifyDocumentKomdigiDao verifyDocumentKomdigiDao;

    @Override
    public BalanceMutationDao getBalanceMutationDao() {
        return balanceMutationDao;
    }
    @Override
    public BalancevendoroftenantDao getBalancevendoroftenantDao() {
        return balancevendoroftenantDao;
    }
    @Override
    public CommonDao getCommonDao() {
        return commonDao;
    }
    @Override
    public JobResultDao getJobResultDao() {
        return jobResultDao;
    }
    @Override
    public LovDao getLovDao() {
        return lovDao;
    }
    @Override
    public UserDao getUserDao() {
        return userDao;
    }
    @Override
    public VendorDao getVendorDao() {
        return vendorDao;
    }
    @Override
    public VendoroftenantDao getVendoroftenantDao() {
        return vendoroftenantDao;
    }
    @Override
    public GeneralSettingDao getGeneralSettingDao() {
        return generalSettingDao;
    }
    @Override
    public SchedulerJobDao getSchedulerJobDao() {
        return schedulerJobDao;
    }
    @Override
    public DocumentDao getDocumentDao() {
        return documentDao;
    }
    @Override
    public DocumentSigningRequestDao getDocumentSigningRequestDao() {
        return documentSigningRequestDao;
    }
    @Override
    public VendorRegisteredUserDao getVendorRegisteredUserDao() {
        return vendorRegisteredUserDao;
    }
	@Override
    public DailyRecapDao getDailyRecapDao() {
       return dailyRecapDao;
    }
	@Override
    public PeruriDocTypeDao getPeruriDocTypeDao() {
        return peruriDocTypeDao;
    }
    @Override
    public JobUpdatePsreIdDao getJobUpdatePsreIdDao() {
        return jobUpdatePsreIdDao;
    }
    @Override
    public RegistrationLivenessResultDao getRegistrationLivenessResultDao() {
        return registrationLivenessResultDao;
    }
    @Override
    public JobCheckRegisterStatusDao getJobCheckRegisterStatusDao() {
        return jobCheckRegisterStatusDao;
    }
    @Override
    public UrlForwarderDao getUrlForwarderDao() {
        return urlForwarderDao;
    }
    @Override
    public EmailDao getEmailDao() {
        return emailDao;
    }
    @Override
    public RoleDao getRoleDao() {
        return roleDao;
    }
    @Override
    public OfficeDao getOfficeDao() {
        return officeDao;
    }
    @Override
    public UseroftenantDao getUseroftenantDao() {
        return useroftenantDao;
    }
    @Override
    public MessageDeliveryReportDao getMessageDeliveryReportDao() {
        return this.messageDeliveryReportDao;
    }
    @Override
    public TenantDao getTenantDao() {
        return this.tenantDao;
    }
    @Override
    public ClientCallbackRequestDao getClientCallbackRequestDao() {
        return this.clientCallbackRequestDao;
    }
    @Override
    public MsgTemplateDao getMsgTemplateDao() {
        return this.msgTemplateDao;
    }
    @Override
    public TenantSettingsDao getTenantSettingsDao() {
        return tenantSettingsDao;
    }
    @Override
    public NotificationtypeoftenantDao getNotificationtypeoftenantDao() {
        return notificationtypeoftenantDao;
    }
    @Override
    public InvitationLinkDao geInvitationLinkDao() {
        return invitationLinkDao;
    }
    @Override
    public ProcessAutosignBmDao getProcessAutosignBmDao() {
        return processAutosignBmDao;
    }
    @Override
    public StampDutyDao getStampDutyDao() {
        return stampDutyDao;
    }
    @Override
    public SigningProcessAuditTrailDao getSigningProcessAuditTrailDao() {
        return signingProcessAuditTrailDao;
    }
    @Override
    public HousekeepingOssDao getHousekeepingOss() {
        return housekeepingOssDao;
    }
    @Override
    public BalanceTopUpDao getBalanceTopUpDao() {
        return balanceTopUpDao;
    }
    @Override
	public BackgroundProcessFailDao getBackgroundProcessFailDao() {
		return backgroundProcessFailDao;
	}
    @Override
	public VerifyDocumentKomdigiDao getVerifyDocumentKomdigiDao() {
		return verifyDocumentKomdigiDao;
	}

}
