package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_vendor")
public class MsVendor extends ActivatableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	public static final String ID_VENDOR_HBM = "idMsVendor";
	public static final String VENDOR_CODE_HBM = "vendorCode";
	public static final String IS_OPERATING_HBM = "isOperating";
	
	private long idMsVendor;
	private MsLov msLovVendorType;
	private String vendorCode;
	private String vendorName;
	private String resendActivationLink;
	private String editAfterSend;
	private String userMustRegister;
	private String verifPhone;
	private String reregistAvailable;
	private String editAfterRegister;
	private String isOperating;
	private MsLov msLovVendorSignPaymentType;

	private Set<MsVendorRegisteredUser> msVendorRegisteredUsers = new HashSet<>(0);
	private Set<MsVendoroftenant> msVendoroftenants = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);
	private Set<TrBalanceDailyRecap> trBalanceDailyRecaps = new HashSet<>(0);
	private Set<TrDocumentD> trDocumentDs = new HashSet<>(0);
	private Set<TrStampDuty> trStampDuties = new HashSet<>(0);
	private Set<MsEmailHosting> msEmailHostings = new HashSet<>(0);
	private Set<MsBalancevendoroftenant> msBalancevendoroftenants = new HashSet<>(0);
	private Set<TrInvitationLink> trInvitationLinks = new HashSet<>(0);
	private Set<TrErrorHistory> trErrorHistories = new HashSet<>(0);
	private Set<MsDocTemplate> msDocTemplates = new HashSet<>(0);
	private Set<TrDocumentSigningRequest> trDocumentSigningRequests = new HashSet<>(0);
	private Set<TrJobUpdatePsreId> trJobUpdatePsreIds = new HashSet<>(0);
	private Set<TrProcessAutosignBmH> trProcessAutosignBmHs = new HashSet<>(0);

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_vendor", unique = true, nullable = false)
	public long getIdMsVendor() {
		return this.idMsVendor;
	}

	public void setIdMsVendor(long idMsVendor) {
		this.idMsVendor = idMsVendor;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_vendor_type", nullable = false)
	public MsLov getMsLovVendorType() {
		return this.msLovVendorType;
	}

	public void setMsLovVendorType(MsLov msLov) {
		this.msLovVendorType = msLov;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_vendor_sign_payment_type", nullable = true)
	public MsLov getMsLovVendorSignPaymentType() {
		return msLovVendorSignPaymentType;
	}

	public void setMsLovVendorSignPaymentType(MsLov msLovVendorSignPaymentType) {
		this.msLovVendorSignPaymentType = msLovVendorSignPaymentType;
	}

	@Column(name = "vendor_code", nullable = false, length = 20)
	public String getVendorCode() {
		return this.vendorCode;
	}

	public void setVendorCode(String vendorCode) {
		this.vendorCode = vendorCode;
	}
	
	@Column(name = "verif_phone", nullable = false, length = 1)
	public String getVerifPhone() {
		return verifPhone;
	}

	public void setVerifPhone(String verifPhone) {
		this.verifPhone = verifPhone;
	}

	@Column(name = "reregist_available", nullable = false, length = 1)
	public String getReregistAvailable() {
		return reregistAvailable;
	}

	public void setReregistAvailable(String reregistAvailable) {
		this.reregistAvailable = reregistAvailable;
	}

	@Column(name = "vendor_name", nullable = false, length = 100)
	public String getVendorName() {
		return this.vendorName;
	}

	public void setVendorName(String vendorName) {
		this.vendorName = vendorName;
	}

	@Column(name = "resend_activation_link", length = 1)
	public String getResendActivationLink() {
		return resendActivationLink;
	}

	public void setResendActivationLink(String resendActivationLink) {
		this.resendActivationLink = resendActivationLink;
	}

	@Column(name = "edit_after_send", length = 1)
	public String getEditAfterSend() {
		return editAfterSend;
	}

	public void setEditAfterSend(String editAfterSend) {
		this.editAfterSend = editAfterSend;
	}

	@Column(name = "user_must_register", length = 1)
	public String getUserMustRegister() {
		return userMustRegister;
	}

	public void setUserMustRegister(String userMustRegister) {
		this.userMustRegister = userMustRegister;
	}

	@Column(name = "edit_after_register", length = 1)
	public String getEditAfterRegister() {
		return editAfterRegister;
	}

	public void setEditAfterRegister(String editAfterRegister) {
		this.editAfterRegister = editAfterRegister;
	}
	
	@Column(name = "is_operating", length = 1)
	public String getIsOperating() {
		return isOperating;
	}

	public void setIsOperating(String isOperating) {
		this.isOperating = isOperating;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<MsVendorRegisteredUser> getMsVendorRegisteredUsers() {
		return this.msVendorRegisteredUsers;
	}

	public void setMsVendorRegisteredUsers(Set<MsVendorRegisteredUser> msVendorRegisteredUsers) {
		this.msVendorRegisteredUsers = msVendorRegisteredUsers;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<MsVendoroftenant> getMsVendoroftenants() {
		return this.msVendoroftenants;
	}

	public void setMsVendoroftenants(Set<MsVendoroftenant> msVendoroftenants) {
		this.msVendoroftenants = msVendoroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return this.trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrBalanceDailyRecap> getTrBalanceDailyRecaps() {
		return this.trBalanceDailyRecaps;
	}

	public void setTrBalanceDailyRecaps(Set<TrBalanceDailyRecap> trBalanceDailyRecaps) {
		this.trBalanceDailyRecaps = trBalanceDailyRecaps;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrDocumentD> getTrDocumentDs() {
		return this.trDocumentDs;
	}

	public void setTrDocumentDs(Set<TrDocumentD> trDocumentDs) {
		this.trDocumentDs = trDocumentDs;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrStampDuty> getTrStampDuties() {
		return this.trStampDuties;
	}

	public void setTrStampDuties(Set<TrStampDuty> trStampDuties) {
		this.trStampDuties = trStampDuties;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<MsEmailHosting> getMsEmailHostings() {
		return this.msEmailHostings;
	}

	public void setMsEmailHostings(Set<MsEmailHosting> msEmailHostings) {
		this.msEmailHostings = msEmailHostings;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<MsBalancevendoroftenant> getMsBalancevendoroftenants() {
		return this.msBalancevendoroftenants;
	}

	public void setMsBalancevendoroftenants(Set<MsBalancevendoroftenant> msBalancevendoroftenants) {
		this.msBalancevendoroftenants = msBalancevendoroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrInvitationLink> getTrInvitationLinks() {
		return trInvitationLinks;
	}

	public void setTrInvitationLinks(Set<TrInvitationLink> trInvitationLinks) {
		this.trInvitationLinks = trInvitationLinks;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrErrorHistory> getTrErrorHistories() {
		return trErrorHistories;
	}

	public void setTrErrorHistories(Set<TrErrorHistory> trErrorHistories) {
		this.trErrorHistories = trErrorHistories;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<MsDocTemplate> getMsDocTemplates() {
		return msDocTemplates;
	}

	public void setMsDocTemplates(Set<MsDocTemplate> msDocTemplates) {
		this.msDocTemplates = msDocTemplates;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrDocumentSigningRequest> getTrDocumentSigningRequests() {
		return trDocumentSigningRequests;
	}

	public void setTrDocumentSigningRequests(Set<TrDocumentSigningRequest> trDocumentSigningRequests) {
		this.trDocumentSigningRequests = trDocumentSigningRequests;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrJobUpdatePsreId> getTrJobUpdatePsreIds() {
		return trJobUpdatePsreIds;
	}

	public void setTrJobUpdatePsreIds(Set<TrJobUpdatePsreId> trJobUpdatePsreIds) {
		this.trJobUpdatePsreIds = trJobUpdatePsreIds;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msVendor")
	public Set<TrProcessAutosignBmH> getTrProcessAutosignBmHs() {
		return this.trProcessAutosignBmHs;
	}

	public void setTrProcessAutosignBmHs(Set<TrProcessAutosignBmH> trProcessAutosignBmHs) {
		this.trProcessAutosignBmHs = trProcessAutosignBmHs;
	}
	
}
