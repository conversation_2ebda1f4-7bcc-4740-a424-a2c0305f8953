package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_msg_template")
public class MsMsgTemplate extends CreatableAndUpdatableEntity implements Serializable{
    private static final long serialVersionUID = 1L;

	private long idMsgTemplate;
	private String templateType;
	private String templateCode;
	private String name;
	private String subject;
	private String body;
	private String waTemplateCode;
	private String waHalosisTemplateCode;

	private Set<MsEmailPattern> msEmailPatterns = new HashSet<>(0);

	public MsMsgTemplate() {
	}

	public MsMsgTemplate(long idMsgTemplate, String usrCrt, Date dtmCrt, String templateType, String templateCode) {
		this.idMsgTemplate = idMsgTemplate;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.templateType = templateType;
		this.templateCode = templateCode;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY) 
	@Column(name = "id_msg_template", unique = true, nullable = false)
	public long getIdMsgTemplate() {
		return this.idMsgTemplate;
	}

	public void setIdMsgTemplate(long idMsgTemplate) {
		this.idMsgTemplate = idMsgTemplate;
	}

	@Column(name = "template_type", nullable = false, length = 20)
	public String getTemplateType() {
		return this.templateType;
	}

	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}

	@Column(name = "template_code", nullable = false, length = 20)
	public String getTemplateCode() {
		return this.templateCode;
	}

	public void setTemplateCode(String templateCode) {
		this.templateCode = templateCode;
	}

	@Column(name = "name", length = 100)
	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "subject", length = 1000)
	public String getSubject() {
		return this.subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	@Column(name = "body")
	public String getBody() {
		return this.body;
	}

	public void setBody(String body) {
		this.body = body;
	}

	@Column(name = "wa_template_code", length = 40)
	public String getWaTemplateCode() {
		return waTemplateCode;
	}

	public void setWaTemplateCode(String waTemplateCode) {
		this.waTemplateCode = waTemplateCode;
	}

	@Column(name = "wa_halosis_template_code", length = 40)
	public String getWaHalosisTemplateCode() {
		return waHalosisTemplateCode;
	}

	public void setWaHalosisTemplateCode(String waHalosisTemplateCode) {
		this.waHalosisTemplateCode = waHalosisTemplateCode;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msMsgTemplate")
	public Set<MsEmailPattern> getMsEmailPatterns() {
		return this.msEmailPatterns;
	}

	public void setMsEmailPatterns(Set<MsEmailPattern> msEmailPatterns) {
		this.msEmailPatterns = msEmailPatterns;
	}
}
