package com.adins.esignhubjob.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.JobResultDao;
import com.adins.esignhubjob.model.table.TrJobResult;
import com.adins.util.Tools;

@Component
@Transactional
public class JobResultDaoHbn extends BaseDaoHbn implements JobResultDao {

    @Override
    public TrJobResult getJobResultByIdJobResult(long idJobResult) {
        Object[][] params = {
            {TrJobResult.ID_JOB_RESULT_HBM, idJobResult}
        };

        return managerDAO.selectOne(
            "from TrJobResult jr "
            + "join fetch jr.msJob mj "
            + "left join fetch jr.msTenant mt "
            + "join fetch jr.amMsuser mu "
            + "join fetch jr.msLovJobType ljt "
            + "where jr.idJobResult = :idJobResult ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrJobResult getJobResultByIdJobResultNewTran(long idJobResult) {
        Object[][] params = {
            {TrJobResult.ID_JOB_RESULT_HBM, idJobResult}
        };

        return managerDAO.selectOne(
            "from TrJobResult jr "
            + "join fetch jr.msJob mj "
            + "left join fetch jr.msTenant mt "
            + "join fetch jr.amMsuser mu "
            + "join fetch jr.msLovJobType ljt "
            + "where jr.idJobResult = :idJobResult ", params);
    }

    @Override
    public void updateJobResult(TrJobResult jobResult) {
        jobResult.setUsrUpd(Tools.maskData(jobResult.getUsrUpd()));
        managerDAO.update(jobResult);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateJobResultNewTran(TrJobResult jobResult) {
        jobResult.setUsrUpd(Tools.maskData(jobResult.getUsrUpd()));
        managerDAO.update(jobResult);
    }
    
}
