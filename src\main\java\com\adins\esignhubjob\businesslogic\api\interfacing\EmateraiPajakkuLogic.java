package com.adins.esignhubjob.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.esignhubjob.model.custom.pajakku.AttachEmeteraiErrorDetail;
import com.adins.esignhubjob.model.custom.pajakku.PajakkuDocumentTypeBean;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.webservice.pajakku.EmeteraiPajakkuLoginResponseBean;
import com.adins.esignhubjob.model.webservice.pajakku.GenerateEmeteraiPajakkuResponse;
import com.adins.esignhubjob.model.webservice.pajakku.StampingEmeteraiPajakkuResponse;
import com.adins.esignhubjob.model.webservice.pajakku.UploadDocPajakkuResponseBean;
import com.adins.util.FtpClient;
import com.aliyun.fc.runtime.Context;

public interface EmateraiPajakkuLogic {
    PajakkuDocumentTypeBean getDocumentType(Context context) throws IOException;
    EmeteraiPajakkuLoginResponseBean getToken(Context context) throws IOException;
    EmeteraiPajakkuLoginResponseBean loginPeruri(TrDocumentH documentH, long connectTimeout, long readTimeout, Context context);
    UploadDocPajakkuResponseBean uploadDocumentForStamping(TrDocumentD document, TrDocumentDStampduty docSdt, FtpClient ftpClient, Context context);
    GenerateEmeteraiPajakkuResponse generateEmeterai(TrDocumentD document, TrDocumentDStampduty docSdt, String token, long connectTimeout, long readTimeout, FtpClient ftpClient, Context context);
    StampingEmeteraiPajakkuResponse stampEmeterai(TrDocumentD document, TrDocumentDStampduty docSdt, String token, long connectTimeout, long readTimeout, FtpClient ftpClient, Context context);
    AttachEmeteraiErrorDetail storeStampedDocumentToOss(TrDocumentD document, FtpClient ftpClient, Context context);
}
