package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.ProcessAutosignBmDao;
import com.adins.esignhubjob.model.table.TrProcessAutosignBmD;
import com.adins.esignhubjob.model.table.TrProcessAutosignBmH;

@Component
@Transactional
public class ProcessAutosignBmDaoHbn extends BaseDaoHbn implements ProcessAutosignBmDao {

    @Override
    public TrProcessAutosignBmH getProcessAutosignBmH(long idProcessAutosignBmH) {
        Map<String, Object> params = new HashMap<>();
        params.put("idProcessAutosignBmH", idProcessAutosignBmH);

        return managerDAO.selectOne(
            "from TrProcessAutosignBmH pabh "
            + "where pabh.idProcessAutosignBmH = :idProcessAutosignBmH "
            , params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrProcessAutosignBmH getProcessAutosignBmHNewTrx(long idProcessAutosignBmH) {
        Map<String, Object> params = new HashMap<>();
        params.put("idProcessAutosignBmH", idProcessAutosignBmH);

        return managerDAO.selectOne(
            "from TrProcessAutosignBmH pabh "
            + "join fetch pabh.msTenant mt "
            + "join fetch pabh.msVendor mv "
            + "where pabh.idProcessAutosignBmH = :idProcessAutosignBmH "
            , params);
    }

    @Override
    public void updateProcessAutosignBmH(TrProcessAutosignBmH processAutosignBmH) {
        managerDAO.update(processAutosignBmH);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateProcessAutosignBmHNewTrx(TrProcessAutosignBmH processAutosignBmH) {
        managerDAO.update(processAutosignBmH);
    }

    @Override
    public void insertProcessAutosignBmD(TrProcessAutosignBmD processAutosignBmD) {
        managerDAO.insert(processAutosignBmD);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertProcessAutosignBmDNewTrx(TrProcessAutosignBmD processAutosignBmD) {
        managerDAO.insert(processAutosignBmD);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<TrProcessAutosignBmH> getProcessAutosignBmHs(String status, String executeType) {
        Map<String, Object> params = new HashMap<>();
        params.put("status", status);
        params.put("executeType", executeType);

        return (List<TrProcessAutosignBmH>) managerDAO.list(
            "from TrProcessAutosignBmH pabh "
            + "where pabh.status = :status "
            + "and pabh.executeType = :executeType ", params).get(Constants.MAP_RESULT_LIST);
    }
    
}
