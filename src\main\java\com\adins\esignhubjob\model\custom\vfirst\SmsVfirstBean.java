package com.adins.esignhubjob.model.custom.vfirst;

import java.util.List;

import com.google.gson.annotations.SerializedName;

public class SmsVfirstBean {
    
    @SerializedName("@UDH") private String udh;
	@SerializedName("@CODING") private String coding;
	@SerializedName("@TEXT") private String text;
	@SerializedName("@PROPERTY") private String property;
	@SerializedName("@ID") private String id;
	@SerializedName("ADDRESS") private List<AddressSmsVfirstBean> address;
	
	public List<AddressSmsVfirstBean> getAddress() {
		return address;
	}
	public void setAddress(List<AddressSmsVfirstBean> address) {
		this.address = address;
	}
	public String getUdh() {
		return udh;
	}
	public void setUdh(String udh) {
		this.udh = udh;
	}
	public String getCoding() {
		return coding;
	}
	public void setCoding(String coding) {
		this.coding = coding;
	}
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	public String getProperty() {
		return property;
	}
	public void setProperty(String property) {
		this.property = property;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
}
