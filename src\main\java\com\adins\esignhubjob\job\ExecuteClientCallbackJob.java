package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrClientCallbackRequest;
import com.adins.util.IOUtils;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class ExecuteClientCallbackJob extends BaseJobHandler {

    private static final String AUDIT = "FC_CALLBACK";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String input = IOUtils.toString(inputStream);
        context.getLogger().info("Processing callback for request ID " + input);

        Long idClientCallbackRequest = Long.valueOf(input);
        TrClientCallbackRequest callbackRequest = daoFactory.getClientCallbackRequestDao().getCallbackRequestNewTrx(idClientCallbackRequest);
        if (null == callbackRequest) {
            context.getLogger().error("Request ID " + input + " not found");
            return;
        }

        String limitGs = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_LIMIT_RETRY_CALLBACK_CLIENT);
        int limit = 1;

        if (!StringUtils.isBlank(limitGs)) {
            try {
                limit = Integer.parseInt(limitGs);
            } catch (Exception e) {
                logger.info("limit general setting not valid, using default limit");
            }
        }

        int attempts = 0;
        boolean isSuccessful = false;
        
        while (attempts < limit && !isSuccessful) {
            isSuccessful = callClientCallback(callbackRequest, context);
            logger.info("failed to callback to client, attemps " + String.valueOf(attempts + 1));
            attempts++;
        }
        

        callbackRequest.setUsrUpd(AUDIT);
        callbackRequest.setDtmUpd(new Date());
        callbackRequest.setRequestStatus(isSuccessful ? (short) 3 : (short) 2);
        daoFactory.getClientCallbackRequestDao().updateCallbackRequestNewTrx(callbackRequest);      
        
    }

    /**
     * @return true if http response code == 200
     */
    private boolean callClientCallback(TrClientCallbackRequest callbackRequest, Context context) {
        MsTenant tenant = callbackRequest.getMsTenant();
        String callbackUrl = tenant.getClientCallbackUrl();

        // Header
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_ACCEPT, HttpHeaders.APPLICATION_JSON);
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        headerMap.put(HttpHeaders.KEY_X_API_KEY, tenant.getApiKey());
        Headers headers = Headers.of(headerMap);

        // Body
        String jsonRequest = callbackRequest.getCallbackRequest();
        context.getLogger().info(String.format("Request ID %1$s, callback request: %2$s", callbackRequest.getIdClientCallbackRequest(), jsonRequest));
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        // Prepare request
        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(callbackUrl)
            .post(body)
            .build();

        try {
            
            OkHttpClient client = Tools.getUnsafeOkHttpClient(20, 60);
            Response response = client.newCall(okHttpRequest).execute();
            String jsonResponse = response.body().string();
            context.getLogger().info(String.format("Request ID %1$s, callback response code: %2$s, body: %3$s", callbackRequest.getIdClientCallbackRequest(), response.code(), jsonResponse));
            return response.code() == 200;

        } catch (Exception e) {
            context.getLogger().error(String.format("Request ID %1$s, callback exception: %2$s", callbackRequest.getIdClientCallbackRequest(), e.getLocalizedMessage()));
            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);
            return false;
        }
    }
    
}
