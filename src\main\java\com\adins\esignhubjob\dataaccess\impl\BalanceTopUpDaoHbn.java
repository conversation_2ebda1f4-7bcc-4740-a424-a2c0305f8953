package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.BalanceTopUpDao;
import com.adins.esignhubjob.model.custom.adins.RemainingQuotaBean;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrBalanceTopUp;
import com.adins.framework.persistence.dao.api.ManagerDAO;
import com.adins.util.Tools;

@Component
@Transactional
public class BalanceTopUpDaoHbn extends BaseDaoHbn implements BalanceTopUpDao {

	@Override
	public List<RemainingQuotaBean> getExpiredTopupBalanceByTenant(Date range, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("expiredDate", range);
		params.put("tenantCode", StringUtils.upperCase(tenantCode));

		StringBuilder query = new StringBuilder();

		query.append("select CAST(bmtp.qty + COALESCE(bmuse.qtyuse, 0) AS INTEGER) AS sisaBalance, ")
				.append("       CAST(bmtp.qty AS INTEGER) AS topup_qty, ")
				.append("       CAST(COALESCE(bmuse.qtyuse, 0) AS INTEGER) AS use_qty, ")
				.append("       btu.id_balance_top_up, ")
				.append("       btu.id_balance_mutation ")
				.append("from tr_balance_top_up btu ")
				.append("join tr_balance_mutation bmtp on btu.id_balance_mutation = bmtp.id_balance_mutation ")
				.append("join ms_tenant mt on bmtp.id_ms_tenant = mt.id_ms_tenant ")
				.append("join lateral ( ")
				.append("    select sum(qty) as qtyuse ")
				.append("    from tr_balance_mutation bmuse ")
				.append("    where bmuse.id_balance_top_up = btu.id_balance_top_up ")
				.append("      and bmuse.id_ms_tenant = bmtp.id_ms_tenant ")
				.append("      and bmuse.id_ms_vendor = bmtp.id_ms_vendor ")
				.append("      and bmuse.lov_balance_type = bmtp.lov_balance_type ")
				.append(") as bmuse on true ")
				.append("where Date(expired_date) < :expiredDate ")
				.append("and btu.is_used = '0'")
				.append("and mt.tenant_code = :tenantCode");

		List<Map<String, Object>> listResult = this.managerDAO.selectAllNativeString(query.toString(), params);
		Iterator<Map<String, Object>> itr = listResult.iterator();

		List<RemainingQuotaBean> quotaBean = new ArrayList<>();

		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();

			RemainingQuotaBean bean = new RemainingQuotaBean();

			bean.setRemainingQuota((int) map.get("d0"));
			bean.setTopupQty((int) map.get("d1"));
			bean.setUsedQty((int) map.get("d2"));
			bean.setBaltop((BigInteger) map.get("d3"));
			bean.setBalmut((BigInteger) map.get("d4"));

			quotaBean.add(bean);
		}

		return quotaBean;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<TrBalanceTopUp> getReminderExpiredBalanceTopUp(Date today, Date targetDate, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("startDate", today);
		params.put("endDate", targetDate);
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));

		return (List<TrBalanceTopUp>) managerDAO.list(
				"from TrBalanceTopUp btu " +
				"join fetch btu.trBalanceMutation bm " +
				"join fetch bm.msTenant mt " +
				"join fetch bm.msVendor mv " +
				"join fetch bm.msLovByLovBalanceType lbt " +
				"where Date(btu.expiredDate) >= :startDate " +
				"and Date(btu.expiredDate) <= :endDate " +
				"and mt.tenantCode = :tenantCode",
			params).get(ManagerDAO.MAP_RESULT_LIST);
	}

	@Override
	public void insertTrBalanceTopUp(TrBalanceTopUp balanceTopUp) {
		balanceTopUp.setUsrCrt(Tools.maskData(balanceTopUp.getUsrCrt()));
		this.managerDAO.insert(balanceTopUp);
	}

	@Override
	public void updateBalanceTopUp(TrBalanceTopUp updBaltop) {
		updBaltop.setUsrUpd(Tools.maskData(updBaltop.getUsrUpd()));
		this.managerDAO.update(updBaltop);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrBalanceTopUp getTrBalanceTopUp(long idTrBalanceTopUp) {
		if (0 == idTrBalanceTopUp) {
			return null;
		}

		return this.managerDAO.selectOne(
				"from TrBalanceTopUp t "
						+ "where t.idBalanceTopUp = :idTrBalanceTopUp ",
				new Object[][] { { "idTrBalanceTopUp", idTrBalanceTopUp } });
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateBalanceTopUpNativeString(long idBalanceTopup, String usrUpd, String isUsed) {
		Map<String, Object> params = new HashMap<>();
		params.put("usrUpd", Tools.maskData(usrUpd));
		params.put("isUsed", isUsed);
		params.put("idBalanceTopup", idBalanceTopup);

		StringBuilder query = new StringBuilder();
		query.append(
				" UPDATE tr_balance_top_up SET dtm_upd = NOW(), usr_upd = :usrUpd, is_used = :isUsed WHERE id_balance_top_up = :idBalanceTopup ");

		managerDAO.updateNativeString(query.toString(), params);
	}

}
