package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_peruri_doc_type")
public class MsPeruriDocType extends ActivatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	
	public static final String MS_PERURI_DOC_TYPE_HBM = "msPeruriDocType";
	public static final String PERURI_DOC_ID_HBM = "peruriDocId";
	public static final String ID_PERURI_DOC_TYPE = "idPeruriDocType";
	public static final String DOC_CODE_HBM = "docCode";


	private long idPeruriDocType;
	private String peruriDocId;
	private String docCode;
	private String docName;
	private Set<TrDocumentD> trDocumentDs = new HashSet<>(0);
	private String vidaDocCode;

	public MsPeruriDocType() {
	}

	public MsPeruriDocType(long idPeruriDocType, String peruriDocId, String docCode, String docName,
			String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd) {
		super();
		this.idPeruriDocType = idPeruriDocType;
		this.peruriDocId = peruriDocId;
		this.docCode = docCode;
		this.docName = docName;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_peruri_doc_type", unique = true, nullable = false)
	public long getIdPeruriDocType() {
		return idPeruriDocType;
	}

	public void setIdPeruriDocType(long idPeruriDocType) {
		this.idPeruriDocType = idPeruriDocType;
	}
	
	@Column(name = "peruri_doc_id", unique = true, nullable = false)
	public String getPeruriDocId() {
		return peruriDocId;
	}

	public void setPeruriDocId(String peruriDocId) {
		this.peruriDocId = peruriDocId;
	}
	
	@Column(name = "doc_code", unique = true, nullable = false)
	public String getDocCode() {
		return docCode;
	}

	public void setDocCode(String docCode) {
		this.docCode = docCode;
	}
	
	@Column(name = "doc_name", unique = true, nullable = false)
	public String getDocName() {
		return docName;
	}

	public void setDocName(String docName) {
		this.docName = docName;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msPeruriDocType")
	public Set<TrDocumentD> getTrDocumentDs() {
		return trDocumentDs;
	}

	public void setTrDocumentDs(Set<TrDocumentD> trDocumentDs) {
		this.trDocumentDs = trDocumentDs;
	}

	@Column(name = "vida_doc_code")
	public String getVidaDocCode() {
		return vidaDocCode;
	}

	public void setVidaDocCode(String vidaDocCode) {
		this.vidaDocCode = vidaDocCode;
	}

}
