package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.UserDao;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.AmUserPersonalData;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.PersonalDataBean;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;
@Component
@Transactional
public class UserDaoHbn extends BaseDaoHbn implements UserDao {

    @Autowired private AliyunOssCloudStorageLogic cloudStorageLogic;

    @Override
    public AmMsuser getUserByPhone(String phone) {
        if (null == phone) {
            return null;
        }
        
        Object[][] params = new Object[][] {{AmMsuser.HASHED_PHONE_HBM, Tools.getHashedString(phone)}};
        return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.hashedPhone = :hashedPhone ", 
				params);
    }

    @Override
    public AmMsuser getUserByIdNo(String idNo) {
        if (StringUtils.isBlank(idNo)) {
			return null;
		}
		
		Object[][] params = new Object[][] {{AmMsuser.HASHED_IDNO_HBM, Tools.getHashedString(idNo)}};
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.hashedIdNo = :hashedIdNo and u.isDeleted ='0' ", 
				params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public AmMsuser getUserByIdNoNewTran(String idNo) {
        if (StringUtils.isBlank(idNo)) {
			return null;
		}
		
		Object[][] params = new Object[][] {{AmMsuser.HASHED_IDNO_HBM, Tools.getHashedString(idNo)}};
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.hashedIdNo = :hashedIdNo and u.isDeleted ='0' ", 
				params);
    }

	@Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
	public AmMsuser getUserByEmailNewTran(String loginId) {
		if (StringUtils.isBlank(loginId)){
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.loginId = :loginId and u.isDeleted ='0' ", 
						new Object[][] {{AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId)}});
	}

    @Override
    public void insertUser(AmMsuser user) {
		user.setUsrCrt(Tools.maskData(user.getUsrCrt()));
        managerDAO.insert(user);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertUserNewTran(AmMsuser user) {
		user.setUsrCrt(Tools.maskData(user.getUsrCrt()));
        managerDAO.insert(user);
    }

    @Override
    public void insertUserPersonalData(PersonalDataBean personalDataBean, Context context) {
        AmUserPersonalData amUserPersonalData = personalDataBean.getUserPersonalData();
		
		if(StringUtils.isNotBlank(personalDataBean.getIdNoRaw())) {
			amUserPersonalData.setIdNoBytea(personalDataEncLogic.encryptFromString(personalDataBean.getIdNoRaw()));
		}
		if(StringUtils.isNotBlank(personalDataBean.getPhoneRaw())) {
			amUserPersonalData.setPhoneBytea(personalDataEncLogic.encryptFromString(personalDataBean.getPhoneRaw()));
		}
		if(StringUtils.isNotBlank(personalDataBean.getAddressRaw())) {
			amUserPersonalData.setAddressBytea(personalDataEncLogic.encryptFromString(personalDataBean.getAddressRaw()));
		}
		if(null != personalDataBean.getPhotoIdRaw()) {
			String key = cloudStorageLogic.storeRegistrationKtp(personalDataBean.getIdNoRaw(), personalDataBean.getPhotoIdRaw(), context);
			amUserPersonalData.setPhotoIdBytea(key.getBytes());
		}
		if(null != personalDataBean.getSelfPhotoRaw()) {
			String key = cloudStorageLogic.storeRegistrationSelfie(personalDataBean.getIdNoRaw(), personalDataBean.getSelfPhotoRaw(), context);
			amUserPersonalData.setPhotoSelf(key.getBytes());
		}
		
		amUserPersonalData.setUsrCrt(Tools.maskData(amUserPersonalData.getUsrCrt()));
		managerDAO.insert(amUserPersonalData);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertUserPersonalDataNewTrx(PersonalDataBean personalDataBean, Context context) {
        AmUserPersonalData amUserPersonalData = personalDataBean.getUserPersonalData();
		
		if(StringUtils.isNotBlank(personalDataBean.getIdNoRaw())) {
			amUserPersonalData.setIdNoBytea(personalDataEncLogic.encryptFromString(personalDataBean.getIdNoRaw()));
		}
		if(StringUtils.isNotBlank(personalDataBean.getPhoneRaw())) {
			amUserPersonalData.setPhoneBytea(personalDataEncLogic.encryptFromString(personalDataBean.getPhoneRaw()));
		}
		if(StringUtils.isNotBlank(personalDataBean.getAddressRaw())) {
			amUserPersonalData.setAddressBytea(personalDataEncLogic.encryptFromString(personalDataBean.getAddressRaw()));
		}
		if(null != personalDataBean.getPhotoIdRaw()) {
			String key = cloudStorageLogic.storeRegistrationKtp(personalDataBean.getIdNoRaw(), personalDataBean.getPhotoIdRaw(), context);
			amUserPersonalData.setPhotoIdBytea(key.getBytes());
		}
		if(null != personalDataBean.getSelfPhotoRaw()) {
			String key = cloudStorageLogic.storeRegistrationSelfie(personalDataBean.getIdNoRaw(), personalDataBean.getSelfPhotoRaw(), context);
			amUserPersonalData.setPhotoSelf(key.getBytes());
		}
		
		amUserPersonalData.setUsrCrt(Tools.maskData(amUserPersonalData.getUsrCrt()));
		managerDAO.insert(amUserPersonalData);
    }

    @Override
    public PersonalDataBean getUserDataByIdMsUser(long idMsUser, boolean getPhoto, Context context) {
        AmUserPersonalData personalData = this.managerDAO.selectOne(
				"from AmUserPersonalData ud "
				+ "join fetch ud.amMsuser u "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' and u.isActive ='1' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}});

		PersonalDataBean personalDataBean = new PersonalDataBean();
		if (null != personalData) {
			personalDataBean.setUserPersonalData(personalData);
			if(null != personalData.getPhoneBytea()) {
				personalDataBean.setPhoneRaw(personalDataEncLogic.decryptToString(personalData.getPhoneBytea()));
			}
			if(null != personalData.getIdNoBytea()) {
				personalDataBean.setIdNoRaw(personalDataEncLogic.decryptToString(personalData.getIdNoBytea()));
			}
			if(null != personalData.getAddressBytea()) {
				personalDataBean.setAddressRaw(personalDataEncLogic.decryptToString(personalData.getAddressBytea()));
			}
			if(getPhoto) {
				if(null != personalData.getPhotoIdBytea()) {
					String key = new String(personalData.getPhotoIdBytea());
					byte[] bytePhoto = cloudStorageLogic.getContentKtp(key, context);
					personalDataBean.setPhotoIdRaw(bytePhoto);
				}
				if(null != personalData.getPhotoSelf()) {
					String key = new String(personalData.getPhotoSelf());
					byte[] bytePhoto = cloudStorageLogic.getContentNonKtp(key, context);
					personalDataBean.setSelfPhotoRaw(bytePhoto);
				}
			}
		}
		return personalDataBean;
    }

    @Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
    public PersonalDataBean getUserDataByIdMsUserNewTrx(long idMsUser, boolean getPhoto, Context context) {
        AmUserPersonalData personalData = this.managerDAO.selectOne(
				"from AmUserPersonalData ud "
				+ "join fetch ud.amMsuser u "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' and u.isActive ='1' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}});

		PersonalDataBean personalDataBean = new PersonalDataBean();
		if (null != personalData) {
			personalDataBean.setUserPersonalData(personalData);
			if(null != personalData.getPhoneBytea()) {
				personalDataBean.setPhoneRaw(personalDataEncLogic.decryptToString(personalData.getPhoneBytea()));
			}
			if(null != personalData.getIdNoBytea()) {
				personalDataBean.setIdNoRaw(personalDataEncLogic.decryptToString(personalData.getIdNoBytea()));
			}
			if(null != personalData.getAddressBytea()) {
				personalDataBean.setAddressRaw(personalDataEncLogic.decryptToString(personalData.getAddressBytea()));
			}
			if(getPhoto) {
				if(null != personalData.getPhotoIdBytea()) {
					String key = new String(personalData.getPhotoIdBytea());
					byte[] bytePhoto = cloudStorageLogic.getContentKtp(key, context);
					personalDataBean.setPhotoIdRaw(bytePhoto);
				}
				if(null != personalData.getPhotoSelf()) {
					String key = new String(personalData.getPhotoSelf());
					byte[] bytePhoto = cloudStorageLogic.getContentNonKtp(key, context);
					personalDataBean.setSelfPhotoRaw(bytePhoto);
				}
			}
		}
		return personalDataBean;
    }
    
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<AmMsuser> getNonDormantUsers() {
		Object[][] queryParams = {{"isDormant", "0"}};
		
		return (List<AmMsuser>) this.managerDAO.list(
			"from AmMsuser "
			+ "where isDormant = :isDormant ", queryParams).get("resultList");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateUserNewTran(AmMsuser user) {
		managerDAO.update(user);
	}

	@Override
	public Long getIdMsUserRegisteredInOtherVendorByEmail(String email, MsVendor vendor) {
		Map<String, Object> params = new HashMap<>();
		params.put("email", StringUtils.upperCase(email));
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select distinct mu.id_ms_user ")
			.append("from am_msuser mu ")
			.append("join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
			.append("where vru.signer_registered_email = :email ")
			.append("and vru.id_ms_vendor != :idMsVendor ");
		
		BigInteger idMsUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsUser) {
			return null;
		}
		
		return idMsUser.longValue();
	}

	@Override
	public Long getIdMsUserRegisteredInOtherVendorByPhone(String phone, MsVendor vendor) {
		Map<String, Object> params = new HashMap<>();
		params.put("hashedPhone", Tools.getHashedString(phone));
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select distinct mu.id_ms_user ")
			.append("from am_msuser mu ")
			.append("join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
			.append("where vru.hashed_signer_registered_phone = :hashedPhone ")
			.append("and vru.id_ms_vendor != :idMsVendor ");
		
		BigInteger idMsUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsUser) {
			return null;
		}
		
		return idMsUser.longValue();
	}

	@Override
	public Long getIdMsUserRegisteredInOtherVendorByNik(String nik, MsVendor vendor) {
		Map<String, Object> params = new HashMap<>();
		params.put("hashedIdNo", Tools.getHashedString(nik));
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select distinct mu.id_ms_user ")
			.append("from am_msuser mu ")
			.append("join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
			.append("where mu.hashed_id_no = :hashedIdNo ")
			.append("and vru.id_ms_vendor != :idMsVendor ");
		
		BigInteger idMsUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsUser) {
			return null;
		}
		
		return idMsUser.longValue();
	}

	@Override
	public void updateUserPersonalData(PersonalDataBean personalDataBean, Context context) {
		AmUserPersonalData amUserPersonalData = personalDataBean.getUserPersonalData();
		
		if (StringUtils.isNotBlank(personalDataBean.getIdNoRaw())) {
			amUserPersonalData.setIdNoBytea(personalDataEncLogic.encryptFromString(personalDataBean.getIdNoRaw()));
		} else {
			amUserPersonalData.setIdNoBytea(null);
		}

		if (StringUtils.isNotBlank(personalDataBean.getPhoneRaw())) {
			amUserPersonalData.setPhoneBytea(personalDataEncLogic.encryptFromString(personalDataBean.getPhoneRaw()));
		} else {
			amUserPersonalData.setPhoneBytea(null);
		}
		
		if (StringUtils.isNotBlank(personalDataBean.getAddressRaw())) {
			amUserPersonalData.setAddressBytea(personalDataEncLogic.encryptFromString(personalDataBean.getAddressRaw()));
		} else {
			amUserPersonalData.setAddressBytea(null);
		}

		if (null != personalDataBean.getPhotoIdRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationKtp(personalDataBean.getIdNoRaw(), personalDataBean.getPhotoIdRaw(), context);
			amUserPersonalData.setPhotoIdBytea(key.getBytes());
		} else {
			amUserPersonalData.setPhotoIdBytea(null);
		}

		if (null != personalDataBean.getSelfPhotoRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationSelfie(personalDataBean.getIdNoRaw(), personalDataBean.getSelfPhotoRaw(), context);
			amUserPersonalData.setPhotoSelf(key.getBytes());
		} else {
			amUserPersonalData.setPhotoSelf(null);
		}
		
		amUserPersonalData.setUsrUpd(Tools.maskData(amUserPersonalData.getUsrUpd()));
		managerDAO.update(amUserPersonalData);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateUserPersonalDataNewTrx(PersonalDataBean personalDataBean, Context context) {
		AmUserPersonalData amUserPersonalData = personalDataBean.getUserPersonalData();
		
		if (StringUtils.isNotBlank(personalDataBean.getIdNoRaw())) {
			amUserPersonalData.setIdNoBytea(personalDataEncLogic.encryptFromString(personalDataBean.getIdNoRaw()));
		} else {
			amUserPersonalData.setIdNoBytea(null);
		}

		if (StringUtils.isNotBlank(personalDataBean.getPhoneRaw())) {
			amUserPersonalData.setPhoneBytea(personalDataEncLogic.encryptFromString(personalDataBean.getPhoneRaw()));
		} else {
			amUserPersonalData.setPhoneBytea(null);
		}
		
		if (StringUtils.isNotBlank(personalDataBean.getAddressRaw())) {
			amUserPersonalData.setAddressBytea(personalDataEncLogic.encryptFromString(personalDataBean.getAddressRaw()));
		} else {
			amUserPersonalData.setAddressBytea(null);
		}

		if (null != personalDataBean.getPhotoIdRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationKtp(personalDataBean.getIdNoRaw(), personalDataBean.getPhotoIdRaw(), context);
			amUserPersonalData.setPhotoIdBytea(key.getBytes());
		} else {
			amUserPersonalData.setPhotoIdBytea(null);
		}

		if (null != personalDataBean.getSelfPhotoRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationSelfie(personalDataBean.getIdNoRaw(), personalDataBean.getSelfPhotoRaw(), context);
			amUserPersonalData.setPhotoSelf(key.getBytes());
		} else {
			amUserPersonalData.setPhotoSelf(null);
		}
		
		amUserPersonalData.setUsrUpd(Tools.maskData(amUserPersonalData.getUsrUpd()));
		managerDAO.update(amUserPersonalData);
	}

	@Override
	public AmMsuser getUserByIdMsUser(long id) {
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "join fetch u.msOffice mo "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' and u.isActive = '1' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, id}});

	}
}
