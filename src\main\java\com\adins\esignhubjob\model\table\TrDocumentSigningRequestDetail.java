package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_document_signing_request_detail")
public class TrDocumentSigningRequestDetail extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	
	private long idDocumentSigningRequestDetail;
	private TrDocumentSigningRequest trDocumentSigningRequest;
	private TrDocumentD trDocumentD;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_document_signing_request_detail", unique = true, nullable = false)
	public long getIdDocumentSigningRequestDetail() {
		return idDocumentSigningRequestDetail;
	}
	
	public void setIdDocumentSigningRequestDetail(long idDocumentSigningRequestDetail) {
		this.idDocumentSigningRequestDetail = idDocumentSigningRequestDetail;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_signing_request")
	public TrDocumentSigningRequest getTrDocumentSigningRequest() {
		return trDocumentSigningRequest;
	}
	
	public void setTrDocumentSigningRequest(TrDocumentSigningRequest trDocumentSigningRequest) {
		this.trDocumentSigningRequest = trDocumentSigningRequest;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return trDocumentD;
	}
	
	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}
}
