package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.CommonDao;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrDocumentH;

@Component
@Transactional
public class CommonDaoHbn extends BaseDaoHbn implements CommonDao {

    @Override
	public long nextSequenceTrBalanceMutationTrxNo() {
		BigInteger result = (BigInteger) this.managerDAO.selectOneNativeString("select nextval('sq_trbalancemutation_trxno')", null);
		return result.longValue();
	}

	@Override
	public String recalculateBalanceMutation(String tenantCode, String vendorCode, String lovBalanceTypeCode, Date startDate, Date endDate) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(lovBalanceTypeCode));
		params.put("startDate", startDate);
		params.put("endDate", endDate);

		return (String) managerDAO.selectOneNativeString("select update_daily_recap(:tenantCode, :vendorCode, :code, :startDate, :endDate)", params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public String incrementDocumentHTotalSignedNewTran(Long idDocumentH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH);

		return (String) managerDAO.selectOneNativeString("select increment_document_h_total_signed(:idDocumentH)", params);
	}

	@Override
	public BigInteger temporaryRetrySignVida() {
		return (BigInteger) managerDAO.selectOneNativeString("select temp_retry_sign_vida()", new HashMap<>());
	}

	@Override
	public String hitFunctionInDatabase(String functionName, String functionParameter) {
		return (String) managerDAO.selectOneNativeString("select " + functionName + "(" + functionParameter + ")", null);
	}
    
}
