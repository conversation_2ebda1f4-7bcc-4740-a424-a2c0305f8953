package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.aliyun.fc.runtime.Context;

public class HitFunctionInDatabaseJob extends BaseJobHandler {

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {
        
        String functionName = System.getenv(Constants.ENV_VAR_FUNCTION_NAME);
        String functionParameter = System.getenv(Constants.ENV_VAR_FUNCTION_PARAMETER);
        context.getLogger().info("hitFunctionInDatabase : select " + functionName + "(" + functionParameter + ")");
        String hitFunctionInDatabaseResult = daoFactory.getCommonDao().hitFunctionInDatabase(functionName, functionParameter);
        context.getLogger().info("hitFunctionInDatabase result : " + hitFunctionInDatabaseResult);
    }
    
}
