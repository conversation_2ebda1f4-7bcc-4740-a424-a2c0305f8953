package com.adins.esignhubjob.model.custom.adins;

import java.util.Date;

public class VidaStampingTokenBean {
    private String accessToken;
    private Date accessTokenGenTime;
    private boolean isSuccessful;
    
    public String getAccessToken() {
        return accessToken;
    }
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    public Date getAccessTokenGenTime() {
        return accessTokenGenTime;
    }
    public void setAccessTokenGenTime(Date accessTokenGenTime) {
        this.accessTokenGenTime = accessTokenGenTime;
    }
    public boolean isSuccessful() {
        return isSuccessful;
    }
    public void setSuccessful(boolean isSuccessful) {
        this.isSuccessful = isSuccessful;
    }
    
}
