package com.adins.esignhubjob.model.custom.adins;
import java.math.BigInteger;

public class RemainingQuotaBean {
    private int remainingQuota;
    private int topupQty;
    private int usedQty;
    private BigInteger balmut;
    private BigInteger baltop;
    public int getRemainingQuota() {
        return remainingQuota;
    }
    public void setRemainingQuota(int remainingQuota) {
        this.remainingQuota = remainingQuota;
    }
    public int getTopupQty() {
        return topupQty;
    }
    public void setTopupQty(int topupQty) {
        this.topupQty = topupQty;
    }
    public int getUsedQty() {
        return usedQty;
    }
    public void setUsedQty(int usedQty) {
        this.usedQty = usedQty;
    }
    public BigInteger getBalmut() {
        return balmut;
    }
    public void setBalmut(BigInteger balmut) {
        this.balmut = balmut;
    }
    public BigInteger getBaltop() {
        return baltop;
    }
    public void setBaltop(BigInteger baltop) {
        this.baltop = baltop;
    }

    
    
}
