package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_doc_template")
public class MsDocTemplate extends ActivatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	public static final String DOCUMENT_TEMPLATE_CODE_HBM = "docTemplateCode";
	public static final String DOCUMENT_TEMPLATE_NAME_HBM = "docTemplateName";
	public static final String ID_DOC_TEMPLATE_DB = "id_doc_template";

	private long idDocTemplate;
	private MsLov msLovPaymentSignType;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private String docTemplateCode;
	private String docTemplateName;
	private String docTemplateDescription;
	private Short numberOfPage;
	private byte[] docExample;
	private String isSequence;
	private String height;
	private String width;
	private Set<MsDocTemplateSignLoc> msDocTemplateSignLocs = new HashSet<>(0);
	private Set<TrDocumentD> trDocumentDs = new HashSet<>(0);

	public MsDocTemplate() {
	}

	public MsDocTemplate(long idDocTemplate, MsTenant msTenant, String docTemplateCode, String docTemplateName,
			String usrCrt, Date dtmCrt) {
		this.idDocTemplate = idDocTemplate;
		this.msTenant = msTenant;
		this.docTemplateCode = docTemplateCode;
		this.docTemplateName = docTemplateName;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}

	public MsDocTemplate(long idDocTemplate, MsLov msLovPaymentSignType, MsTenant msTenant, MsVendor msVendor,
			String docTemplateCode, String docTemplateName, String docTemplateDescription, Short numberOfPage,
			byte[] docExample, String isSequence, String height, String width,
			Set<MsDocTemplateSignLoc> msDocTemplateSignLocs, Set<TrDocumentD> trDocumentDs) {
		super();
		this.idDocTemplate = idDocTemplate;
		this.msLovPaymentSignType = msLovPaymentSignType;
		this.msTenant = msTenant;
		this.msVendor = msVendor;
		this.docTemplateCode = docTemplateCode;
		this.docTemplateName = docTemplateName;
		this.docTemplateDescription = docTemplateDescription;
		this.numberOfPage = numberOfPage;
		this.docExample = docExample;
		this.isSequence = isSequence;
		this.height = height;
		this.width = width;
		this.msDocTemplateSignLocs = msDocTemplateSignLocs;
		this.trDocumentDs = trDocumentDs;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_doc_template", unique = true, nullable = false)
	public long getIdDocTemplate() {
		return this.idDocTemplate;
	}

	public void setIdDocTemplate(long idDocTemplate) {
		this.idDocTemplate = idDocTemplate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_payment_sign_type")
	public MsLov getMsLovPaymentSignType() {
		return this.msLovPaymentSignType;
	}

	public void setMsLovPaymentSignType(MsLov msLovPaymentSignType) {
		this.msLovPaymentSignType = msLovPaymentSignType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@Column(name = "doc_template_code", nullable = false, length = 20)
	public String getDocTemplateCode() {
		return this.docTemplateCode;
	}

	public void setDocTemplateCode(String docTemplateCode) {
		this.docTemplateCode = docTemplateCode;
	}

	@Column(name = "doc_template_name", nullable = false, length = 100)
	public String getDocTemplateName() {
		return this.docTemplateName;
	}

	public void setDocTemplateName(String docTemplateName) {
		this.docTemplateName = docTemplateName;
	}

	@Column(name = "doc_template_description", length = 200)
	public String getDocTemplateDescription() {
		return this.docTemplateDescription;
	}

	public void setDocTemplateDescription(String docTemplateDescription) {
		this.docTemplateDescription = docTemplateDescription;
	}

	@Column(name = "number_of_page")
	public Short getNumberOfPage() {
		return this.numberOfPage;
	}

	public void setNumberOfPage(Short numberOfPage) {
		this.numberOfPage = numberOfPage;
	}

	@Column(name = "doc_example")
	public byte[] getDocExample() {
		return this.docExample;
	}

	public void setDocExample(byte[] docExample) {
		this.docExample = docExample;
	}

	@Column(name = "is_sequence", length = 1)
	public String getIsSequence() {
		return this.isSequence;
	}

	public void setIsSequence(String isSequence) {
		this.isSequence = isSequence;
	}
	
	@Column(name = "height", length = 20)
	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	@Column(name = "width", length = 20)
	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msDocTemplate")
	public Set<MsDocTemplateSignLoc> getMsDocTemplateSignLocs() {
		return this.msDocTemplateSignLocs;
	}

	public void setMsDocTemplateSignLocs(Set<MsDocTemplateSignLoc> msDocTemplateSignLocs) {
		this.msDocTemplateSignLocs = msDocTemplateSignLocs;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msDocTemplate")
	public Set<TrDocumentD> getTrDocumentDs() {
		return this.trDocumentDs;
	}

	public void setTrDocumentDs(Set<TrDocumentD> trDocumentDs) {
		this.trDocumentDs = trDocumentDs;
	}
}
