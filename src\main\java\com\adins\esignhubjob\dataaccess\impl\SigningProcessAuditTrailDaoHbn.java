package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.SigningProcessAuditTrailDao;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;

@Component
@Transactional
public class SigningProcessAuditTrailDaoHbn extends BaseDaoHbn implements SigningProcessAuditTrailDao {
    
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public  void insertSigningProcessAuditTrailNewTrx(TrSigningProcessAuditTrail auditTrail) {
        auditTrail.setEmail(StringUtils.upperCase(auditTrail.getEmail()));
        managerDAO.insert(auditTrail);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public TrSigningProcessAuditTrail getLatestAuditTrailNewTrx(String hashedPhone, String email, MsTenant tenant, MsVendor vendor, MsLov lovProcessType) {
        Map<String, Object> params = new HashMap<>();
        params.put("hashedPhone", hashedPhone);
        params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
        params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
        params.put(MsLov.ID_LOV_HBM, lovProcessType.getIdLov());
        params.put("email", StringUtils.upperCase(email));

        StringBuilder query = new StringBuilder();
        query
            .append("select id_signing_process_audit_trail ")
            .append("from tr_signing_process_audit_trail ")
            .append("where hashed_phone_no = :hashedPhone ")
            .append("and id_ms_tenant = :idMsTenant ")
            .append("and psre_vendor_id = :idMsVendor ")
            .append("and lov_process_type = :idLov ")
            .append("and email = :email ")
            .append("order by id_signing_process_audit_trail desc limit 1 ");

        BigInteger idAuditTrail = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
        if (null == idAuditTrail) {
            return null;
        }

        Map<String, Object> trueParam = new HashMap<>();
        trueParam.put("idSigningProcessAuditTrail", idAuditTrail.longValue());

        return managerDAO.selectOne("from TrSigningProcessAuditTrail where idSigningProcessAuditTrail = :idSigningProcessAuditTrail ", trueParam);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertSigningProcessAuditTrailDetailNewTrx(TrSigningProcessAuditTrailDetail auditTrailDetail) {
        managerDAO.insert(auditTrailDetail);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateSigningProcessAuditTrailNewTrx(TrSigningProcessAuditTrail auditTrail) {
        managerDAO.update(auditTrail);
    }
}
