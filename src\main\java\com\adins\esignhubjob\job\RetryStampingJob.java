package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentHStampdutyError;
import com.aliyun.fc.runtime.Context;

public class RetryStampingJob extends BaseJobHandler {

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {

        // Get maximum automatic retry count
        String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_DOCUMENT_MAX_RETRY_STAMPING);
        int maxRetryCount = Integer.parseInt(gsValue);

        // Get maximum retry count
        int maxErrorCount = logicFactory.getCommonStampingLogic().getStampingMaxErrorCount();

        // Get limit of data to be processed
        String limitString = System.getenv(Constants.ENV_VAR_DATA_PROCESSED_LIMIT);
        int limit = Integer.parseInt(limitString);

        Short[] prosesMaterai = {51, 61, 521};
        List<Map<String, Object>> idDocumentHs = daoFactory.getDocumentDao().getIdDocumentHs(prosesMaterai, maxErrorCount, maxRetryCount, limit);

        if (CollectionUtils.isEmpty(idDocumentHs)) {
            context.getLogger().info("Job does not process any data");
            return;
        }

        context.getLogger().info("Job processing " + idDocumentHs.size() + " data");

        for (Map<String,Object> idMap : idDocumentHs) {

            BigInteger idDocumentH = (BigInteger) idMap.get("d0");
            TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByIdDocumentHNewTrx(idDocumentH.longValue());
            List<TrDocumentHStampdutyError> stampdutyErrors = daoFactory.getDocumentDao().getDocumentHStampdutyErrorsNewTran(documentH);

            for (TrDocumentHStampdutyError stampdutyError : stampdutyErrors) {
                stampdutyError.setErrorCount((short) 0);

                Short currentRetryCount = stampdutyError.getRetryCount();
                if (currentRetryCount == null || currentRetryCount.equals((short) 0)) {
                    stampdutyError.setRetryCount((short) 1);
                } else {
                    short retryCount = currentRetryCount;
                    retryCount += 1;
                    stampdutyError.setRetryCount(retryCount);
                }

                stampdutyError.setUsrUpd(context.getRequestId());
                stampdutyError.setDtmUpd(new Date());
                daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(stampdutyError);

            }

            short newProsesMaterai = 0;
            if (documentH.getProsesMaterai().equals((short) 51)) {
                newProsesMaterai = 52;
            } else if (documentH.getProsesMaterai().equals((short) 61)) {
                newProsesMaterai = 62;
            } else if (documentH.getProsesMaterai().equals((short) 521)) {
                newProsesMaterai = 522;
            }

            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, proses_materai updated from %3$s to %4$s", documentH.getMsTenant().getTenantCode(), documentH.getRefNumber(), documentH.getProsesMaterai(), newProsesMaterai));

            documentH.setProsesMaterai(newProsesMaterai);
            documentH.setUsrUpd(context.getRequestId());
            documentH.setDtmUpd(new Date());
            daoFactory.getDocumentDao().updateDocumentHeaderNewTran(documentH);
        }

    }
    
}
