package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_document_signing_request")
public class TrDocumentSigningRequest extends CreatableAndUpdatableEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private long idDocumentSigningRequest;
	private AmMsuser amMsuser;
	private TrDocumentH trDocumentH;
	private TrDocumentD trDocumentD;
	private Date requestStart;
	private Date requestEnd;
	private Short requestStatus;
	private String userRequestIp;
	private String userRequestBrowserInformation;
	private Date userSigningConsentTimestamp;
	private MsVendor msVendor;
	
	private Set<TrDocumentSigningRequestDetail> trDocumentSigningRequestDetails = new HashSet<>(0);
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_document_signing_request", unique = true, nullable = false)
	public long getIdDocumentSigningRequest() {
		return idDocumentSigningRequest;
	}
	
	public void setIdDocumentSigningRequest(long idDocumentSigningRequest) {
		this.idDocumentSigningRequest = idDocumentSigningRequest;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
	public AmMsuser getAmMsuser() {
		return amMsuser;
	}
	
	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_h")
	public TrDocumentH getTrDocumentH() {
		return trDocumentH;
	}

	public void setTrDocumentH(TrDocumentH trDocumentH) {
		this.trDocumentH = trDocumentH;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return trDocumentD;
	}

	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "request_start", length = 29)
	public Date getRequestStart() {
		return requestStart;
	}
	
	public void setRequestStart(Date requestStart) {
		this.requestStart = requestStart;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "request_end", length = 29)
	public Date getRequestEnd() {
		return requestEnd;
	}
	
	public void setRequestEnd(Date requestEnd) {
		this.requestEnd = requestEnd;
	}
	
	@Column(name = "request_status")
	public Short getRequestStatus() {
		return requestStatus;
	}
	
	public void setRequestStatus(Short requestStatus) {
		this.requestStatus = requestStatus;
	}
	
	@Column(name = "user_request_ip", nullable = false, length = 20)
	public String getUserRequestIp() {
		return userRequestIp;
	}
	
	public void setUserRequestIp(String userRequestIp) {
		this.userRequestIp = userRequestIp;
	}
	
	@Column(name = "user_request_browser_information", nullable = false, length = 50)
	public String getUserRequestBrowserInformation() {
		return userRequestBrowserInformation;
	}
	
	public void setUserRequestBrowserInformation(String userRequestBrowserInformation) {
		this.userRequestBrowserInformation = userRequestBrowserInformation;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "user_signing_consent_timestamp", length = 29)
	public Date getUserSigningConsentTimestamp() {
		return userSigningConsentTimestamp;
	}
	
	public void setUserSigningConsentTimestamp(Date userSigningConsentTimestamp) {
		this.userSigningConsentTimestamp = userSigningConsentTimestamp;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentSigningRequest")
	public Set<TrDocumentSigningRequestDetail> getTrDocumentSigningRequestDetails() {
		return trDocumentSigningRequestDetails;
	}

	public void setTrDocumentSigningRequestDetails(Set<TrDocumentSigningRequestDetail> trDocumentSigningRequestDetails) {
		this.trDocumentSigningRequestDetails = trDocumentSigningRequestDetails;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor")
	public MsVendor getMsVendor() {
		return msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}
	
	
}