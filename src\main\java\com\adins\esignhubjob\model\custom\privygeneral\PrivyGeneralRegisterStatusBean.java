package com.adins.esignhubjob.model.custom.privygeneral;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralRegisterStatusBean {
    @SerializedName("reference_number") private String referenceNumber;
    @SerializedName("channel_id") private String channelId;
    private String info;
    @SerializedName("register_token") private String registerToken;
    private String status;
    @SerializedName("privy_id") private String privyId;
    private String email;
    private String phone;
    private PrivyGeneralIdentityBean identity;
    @SerializedName("reject_reason") private PrivyGeneralRejectReasonBean rejectReason;

    public String getReferenceNumber() {
        return this.referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getChannelId() {
        return this.channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getInfo() {
        return this.info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getRegisterToken() {
        return this.registerToken;
    }

    public void setRegisterToken(String registerToken) {
        this.registerToken = registerToken;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPrivyId() {
        return this.privyId;
    }

    public void setPrivyId(String privyId) {
        this.privyId = privyId;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public PrivyGeneralIdentityBean getIdentity() {
        return this.identity;
    }

    public void setIdentity(PrivyGeneralIdentityBean identity) {
        this.identity = identity;
    }

    public PrivyGeneralRejectReasonBean getRejectReason() {
        return this.rejectReason;
    }

    public void setRejectReason(PrivyGeneralRejectReasonBean rejectReason) {
        this.rejectReason = rejectReason;
    }   

}
