package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailAttachmentBean;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class DeactivateUserJob extends BaseJobHandler {

    private static final String AUDIT = "FC_DEACT_USER";
    private static final String MONTHS = " months";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {
        
        MsLov schedulerType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.CODE_LOV_SCHED_TYPE_MONTHLY);
        MsLov jobType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_JOB_TYPE, Constants.CODE_LOV_JOB_TYPE_UPDATE_DORMANT_INACTIVE_USER);
        
        try {
            Date startDate = new Date();
            List<AmMsuser> nonDormantUsers = daoFactory.getUserDao().getNonDormantUsers();
            long dataProcessed = nonDormantUsers.size();
            context.getLogger().info("Non dormant users : " + dataProcessed);
    
            for (AmMsuser user : nonDormantUsers) {
                context.getLogger().info("Checking dormancy for " + user.getLoginId() + " (" + user.getIdMsUser() +")");
                if (isDormant(user, context)) {
                    context.getLogger().info(user.getLoginId() + " is dormant");
                    user.setIsDormant("1");
                    user.setDormantDate(new Date());
                    user.setUsrCrt(AUDIT);
                    user.setDtmUpd(new Date());
                    daoFactory.getUserDao().updateUserNewTran(user);
                } else {
                    context.getLogger().info(user.getLoginId() + " is not dormant");
                }
            }
    
            Date endDate = new Date();
           
            TrSchedulerJob schedulerJob = new TrSchedulerJob();
            schedulerJob.setSchedulerStart(startDate);
            schedulerJob.setSchedulerEnd(endDate);
            schedulerJob.setMsLovBySchedulerType(schedulerType);
            schedulerJob.setMsLovByJobType(jobType);
            schedulerJob.setDataProcessed(dataProcessed);
            schedulerJob.setUsrCrt(AUDIT);
            schedulerJob.setDtmCrt(new Date());
            daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
        } catch (Exception e) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode("PIC_OPERATION_ADINS");
            String[] recipient = gs.getGsValue().split(";");
            EmailAttachmentBean[] attachments = null;

			byte[] stackTraceFile = buildStackTraceTextFile(e);
			String filename = buildStackTraceFileName(jobType.getDescription(), schedulerType.getDescription());
			EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
			attachments = new EmailAttachmentBean[] {attachment};

            EmailInformationBean emailBean = new EmailInformationBean();
		    emailBean.setTo(recipient);
		    emailBean.setBodyMessage("There was an error in Job " + schedulerType.getDescription() + " " + jobType.getDescription() + ". Stack Trace is attached on this email.");
		    emailBean.setSubject("Job " + schedulerType.getDescription() + " " + jobType.getDescription());
            logicFactory.getEmailSenderLogic().sendEmail(emailBean, attachments, context);
            context.getLogger().info("Exception Thrown : " + e.getClass().getName() + " " + e.getMessage());
        }
    }
    
    private boolean isDormant(AmMsuser user,  Context context) {
        Date now = new Date();
        Calendar endCalendar = new GregorianCalendar();
        endCalendar.setTime(now);

        AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode("WAITING_TIME_BEFORE_DORMANT");
        int waitTimeInMonth = Integer.parseInt(gs.getGsValue());

        Calendar startCalendar = new GregorianCalendar();

        if (null != user.getLastLoggedIn()) {
            startCalendar.setTime(user.getLastLoggedIn());
            int diffYearLogin = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
            int diffMonthLogin = diffYearLogin * 12 + endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
            if (diffMonthLogin < waitTimeInMonth) {
                context.getLogger().info(user.getLoginId() + " logged in within the last " + waitTimeInMonth + MONTHS);
                return false;
            }
        }

        MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getLastUpdatedVendorRegisteredUser(user);
        if (null != vru) {
            Date maxDate = null == vru.getDtmUpd() ? vru.getDtmCrt() : vru.getDtmUpd();
            startCalendar = new GregorianCalendar();
            startCalendar.setTime(maxDate);
    
            int diffYearReg = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
            int diffMonthReg = diffYearReg * 12 + endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
            if (diffMonthReg < waitTimeInMonth) {
                context.getLogger().info(user.getLoginId() + " registered/ re-registered within the last " + waitTimeInMonth + MONTHS);
                return false;
            }
        }

        TrDocumentDSign docDSign = daoFactory.getDocumentDao().getLatestSignedDocumentDSign(user.getIdMsUser());
        if (null != docDSign && null != docDSign.getSignDate()) {
            startCalendar = new GregorianCalendar();
            startCalendar.setTime(docDSign.getSignDate());
            int diffYearSign = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
            int diffMonthSign = diffYearSign * 12 + endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
            if (diffMonthSign < waitTimeInMonth) {
                context.getLogger().info(user.getLoginId() + " signed a document within the last " + waitTimeInMonth + MONTHS);
                return false;
            }
        }

        TrBalanceMutation bm = daoFactory.getBalanceMutationDao().getUsersLatestTrx(user.getIdMsUser());
        if (null != bm) {
            startCalendar = new GregorianCalendar();
            startCalendar.setTime(bm.getTrxDate());
            int diffYearTrx = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
            int diffMonthTrx = diffYearTrx * 12 + endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
            if (diffMonthTrx < waitTimeInMonth) {
                context.getLogger().info(user.getLoginId() + " has transactions within the last " + waitTimeInMonth + MONTHS);
                return false;
            }
        }

        return true;
    }

    private byte[] buildStackTraceTextFile(Exception e) {
		String stackTrace = ExceptionUtils.getStackTrace(e);
		String base64 = Base64.getEncoder().encodeToString(stackTrace.getBytes());
		return Base64.getDecoder().decode(base64);
	}

    private String buildStackTraceFileName(String jobType, String schedulerType) {
		String currentTime = Tools.formatDateToStringIn(new Date(), Constants.DATE_TIME_FORMAT_SEQ);
		StringBuilder filename = new StringBuilder()
			.append(StringUtils.upperCase(jobType)).append("_")
			.append(StringUtils.upperCase(schedulerType)).append("_")
			.append(currentTime)
			.append(".txt");
		return filename.toString();
	}
}
