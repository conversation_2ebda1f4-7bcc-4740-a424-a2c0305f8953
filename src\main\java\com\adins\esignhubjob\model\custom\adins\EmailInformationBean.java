package com.adins.esignhubjob.model.custom.adins;

import java.io.Serializable;

public class EmailInformationBean implements Serializable {
	private static final long serialVersionUID = -6388097585214801788L;
	private String from;
	private String replyTo;
	private String[] to; 
	private String[] cc; 
	private String[] bcc;
	private String subject;
	private String bodyMessage;
	private boolean isImportance = false;

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public String getReplyTo() {
		return replyTo;
	}

	public void setReplyTo(String replyTo) {
		this.replyTo = replyTo;
	}

	public String[] getTo() {
		return to;
	}

	public void setTo(String[] to) {
		this.to = to;
	}

	public String[] getCc() {
		return cc;
	}

	public void setCc(String[] cc) {
		this.cc = cc;
	}

	public String[] getBcc() {
		return bcc;
	}

	public void setBcc(String[] bcc) {
		this.bcc = bcc;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getBodyMessage() {
		return bodyMessage;
	}

	public void setBodyMessage(String bodyMessage) {
		this.bodyMessage = bodyMessage;
	}

	public boolean isImportance() {
		return isImportance;
	}

	public void setImportance(boolean isImportance) {
		this.isImportance = isImportance;
	}
}