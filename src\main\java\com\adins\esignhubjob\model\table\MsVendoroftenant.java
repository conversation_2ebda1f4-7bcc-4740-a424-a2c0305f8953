package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_vendoroftenant")
public class MsVendoroftenant extends CreatableAndUpdatableEntity implements Serializable{
    private static final long serialVersionUID = 1L;
	
	public static final String DEFAULT_VENDOR_HBM = "defaultVendor";
	
	private long idMsVendoroftenant;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private String token;
	private String encryptionKey;
	private String emailPartner;
	private String isUseVendorEkyc;
	private String defaultVendor;
	private String clientId;
	private String clientSecret;
	private String vendorMerchantKey;
	private String vendorChannelId;
	private String vendorEnterpriseId;
	private String vendorEnterpriseToken;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_vendoroftenant", unique = true, nullable = false)
	public long getIdMsVendoroftenant() {
		return this.idMsVendoroftenant;
	}

	public void setIdMsVendoroftenant(long idMsVendoroftenant) {
		this.idMsVendoroftenant = idMsVendoroftenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return this.msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@Column(name = "token", length = 500)
	public String getToken() {
		return this.token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	@Column(name = "encryption_key", length = 500)
	public String getEncryptionKey() {
		return this.encryptionKey;
	}

	public void setEncryptionKey(String encryptionKey) {
		this.encryptionKey = encryptionKey;
	}

	@Column(name = "email_partner", length = 80)
	public String getEmailPartner() {
		return this.emailPartner;
	}

	public void setEmailPartner(String emailPartner) {
		this.emailPartner = emailPartner;
	}

	@Column(name = "is_use_vendor_ekyc", length = 1)
	public String getIsUseVendorEkyc() {
		return this.isUseVendorEkyc;
	}

	public void setIsUseVendorEkyc(String isUseVendorEkyc) {
		this.isUseVendorEkyc = isUseVendorEkyc;
	}

	@Column(name = "default_vendor", length = 1)
	public String getDefaultVendor() {
		return defaultVendor;
	}

	public void setDefaultVendor(String defaultVendor) {
		this.defaultVendor = defaultVendor;
	}

	@Column(name = "client_id", length = 40)
	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	@Column(name = "client_secret", length = 40)
	public String getClientSecret() {
		return clientSecret;
	}

	public void setClientSecret(String clientSecret) {
		this.clientSecret = clientSecret;
	}

	@Column(name = "vendor_merchant_key", length = 25)
	public String getVendorMerchantKey() {
		return this.vendorMerchantKey;
	}

	public void setVendorMerchantKey(String vendorMerchantKey) {
		this.vendorMerchantKey = vendorMerchantKey;
	}

	@Column(name = "vendor_channel_id", length = 50)
	public String getVendorChannelId() {
		return this.vendorChannelId;
	}

	public void setVendorChannelId(String vendorChannelId) {
		this.vendorChannelId = vendorChannelId;
	}

	@Column(name = "vendor_enterprise_id", length = 10)
	public String getVendorEnterpriseId() {
		return this.vendorEnterpriseId;
	}

	public void setVendorEnterpriseId(String vendorEnterpriseId) {
		this.vendorEnterpriseId = vendorEnterpriseId;
	}
	
	@Column(name = "vendor_enterprise_token", length = 45)
	public String getVendorEnterpriseToken() {
		return this.vendorEnterpriseToken;
	}

	public void setVendorEnterpriseToken(String vendorEnterpriseToken) {
		this.vendorEnterpriseToken = vendorEnterpriseToken;
	}

}
