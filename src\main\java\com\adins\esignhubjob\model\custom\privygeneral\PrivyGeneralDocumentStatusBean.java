package com.adins.esignhubjob.model.custom.privygeneral;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralDocumentStatusBean {
    @SerializedName("reference_number") private String referenceNumber;
    @SerializedName("channel_id") private String channelId;
    @SerializedName("document_token") private String documentToken;
    private String status;
    private String message;
    @SerializedName("unsigned_document") private String unsignedDocument;
    @SerializedName("signed_document") private String signedDocument;

    public String getReferenceNumber() {
        return this.referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getChannelId() {
        return this.channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getDocumentToken() {
        return this.documentToken;
    }

    public void setDocumentToken(String documentToken) {
        this.documentToken = documentToken;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getUnsignedDocument() {
        return this.unsignedDocument;
    }

    public void setUnsignedDocument(String unsignedDocument) {
        this.unsignedDocument = unsignedDocument;
    }

    public String getSignedDocument() {
        return this.signedDocument;
    }

    public void setSignedDocument(String signedDocument) {
        this.signedDocument = signedDocument;
    }
    
}
