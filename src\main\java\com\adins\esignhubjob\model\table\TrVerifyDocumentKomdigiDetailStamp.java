package com.adins.esignhubjob.model.table;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;
@Entity
@Table(name = "tr_verify_document_komdigi_detail_stamp")
public class TrVerifyDocumentKomdigiDetailStamp extends CreatableAndUpdatableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	private long idVerifyDocumentKomdigiDetailStamp;
	private TrVerifyDocumentKomdigi trVerifyDocumentKomdigi;
	private String stampDutySn;
	private Date stampDutyTimestamp;
	private String stampDutyReason;
	private String stampDutySignatureStatus;
	private String stampDutyLocation;
	private String stampDutyCertifIdentity;
	private String stampDutyLtv;
	private String stampDutyTrusted;
	private String stampDutyIssuer;
	private String stampDutyChainLevel;
	
	public TrVerifyDocumentKomdigiDetailStamp() {
		//TODO Auto-generated constructor stub
	}
	public TrVerifyDocumentKomdigiDetailStamp(long idVerifyDocumentKomdigiDetailStamp, TrVerifyDocumentKomdigi trVerifyDocumentKomdigi, String stampDutySn, Date stampDutyTimestamp, String stampDutyReason, String stampDutySignatureStatus, String stampDutyLocation, String stampDutyCertifIdentity, String stampDutyLtv, String stampDutyTrusted, String stampDutyIssuer, String stampDutyChainLevel, String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd) {
		this.idVerifyDocumentKomdigiDetailStamp = idVerifyDocumentKomdigiDetailStamp;
		this.trVerifyDocumentKomdigi = trVerifyDocumentKomdigi;
		this.stampDutySn = stampDutySn;
		this.stampDutyTimestamp = stampDutyTimestamp;
		this.stampDutyReason = stampDutyReason;
		this.stampDutySignatureStatus = stampDutySignatureStatus;
		this.stampDutyLocation = stampDutyLocation;
		this.stampDutyCertifIdentity = stampDutyCertifIdentity;
		this.stampDutyLtv = stampDutyLtv;
		this.stampDutyTrusted = stampDutyTrusted;
		this.stampDutyIssuer = stampDutyIssuer;
		this.stampDutyChainLevel = stampDutyChainLevel;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_verify_document_komdigi_detail_stamp", unique = true, nullable = false)
	public long getIdVerifyDocumentKomdigiDetailStamp() {
		return this.idVerifyDocumentKomdigiDetailStamp;
	}
	public void setIdVerifyDocumentKomdigiDetailStamp(long idVerifyDocumentKomdigiDetailStamp) {
		this.idVerifyDocumentKomdigiDetailStamp = idVerifyDocumentKomdigiDetailStamp;
	}
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_verify_document_komdigi")
	public TrVerifyDocumentKomdigi getTrVerifyDocumentKomdigi() {
		return this.trVerifyDocumentKomdigi;
	}
	public void setTrVerifyDocumentKomdigi(TrVerifyDocumentKomdigi trVerifyDocumentKomdigi) {
		this.trVerifyDocumentKomdigi = trVerifyDocumentKomdigi;
	}
	@Column(name = "stamp_duty_sn")
	public String getStampDutySn() {
		return this.stampDutySn;
	}
	public void setStampDutySn(String stampDutySn) {
		this.stampDutySn = stampDutySn;
	}
	@Column(name = "stamp_duty_timestamp", length = 29)
	public Date getStampDutyTimestamp() {
		return this.stampDutyTimestamp;
	}
	public void setStampDutyTimestamp(Date stampDutyTimestamp) {
		this.stampDutyTimestamp = stampDutyTimestamp;
	}
	@Column(name = "stamp_duty_reason", length = 100)
	public String getStampDutyReason() {
		return this.stampDutyReason;
	}
	public void setStampDutyReason(String stampDutyReason) {
		this.stampDutyReason = stampDutyReason;
	}
	@Column(name = "stamp_duty_signature_status")
	public String getStampDutySignatureStatus() {
		return this.stampDutySignatureStatus;
	}
	public void setStampDutySignatureStatus(String stampDutySignatureStatus) {
		this.stampDutySignatureStatus = stampDutySignatureStatus;
	}
	@Column(name = "stamp_duty_location", length = 255)
	public String getStampDutyLocation() {
		return this.stampDutyLocation;
	}
	public void setStampDutyLocation(String stampDutyLocation) {
		this.stampDutyLocation = stampDutyLocation;
	}
	@Column(name = "stamp_duty_certif_identity", length = 1)
	public String getStampDutyCertifIdentity() {
		return this.stampDutyCertifIdentity;
	}
	public void setStampDutyCertifIdentity(String stampDutyCertifIdentity) {
		this.stampDutyCertifIdentity = stampDutyCertifIdentity;
	}
	@Column(name = "stamp_duty_ltv", length = 10)
	public String getStampDutyLtv() {
		return this.stampDutyLtv;
	}
	public void setStampDutyLtv(String stampDutyLtv) {
		this.stampDutyLtv = stampDutyLtv;
	}
	@Column(name = "stamp_duty_trusted", length = 1)
	public String getStampDutyTrusted() {
		return this.stampDutyTrusted;
	}
	public void setStampDutyTrusted(String stampDutyTrusted) {
		this.stampDutyTrusted = stampDutyTrusted;
	}
	@Column(name = "stamp_duty_issuer")
	public String getStampDutyIssuer() {
		return this.stampDutyIssuer;
	}
	public void setStampDutyIssuer(String stampDutyIssuer) {
		this.stampDutyIssuer = stampDutyIssuer;
	}
	@Column(name = "stamp_duty_chain_level", length = 1)
	public String getStampDutyChainLevel() {
		return this.stampDutyChainLevel;
	}
	public void setStampDutyChainLevel(String stampDutyChainLevel) {
		this.stampDutyChainLevel = stampDutyChainLevel;
	}
}