package com.adins.esignhubjob.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.CommonLogic;
import com.adins.esignhubjob.businesslogic.api.EmailSenderLogic;
import com.adins.esignhubjob.businesslogic.api.MessageTemplateLogic;
import com.adins.esignhubjob.businesslogic.api.NotificationSenderLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SmsJatisLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SmsVfirstLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.WhatsAppJatisLogic;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.custom.adins.RegisterExternalRequestBean;
import com.adins.esignhubjob.model.custom.adins.UserBean;
import com.adins.esignhubjob.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esignhubjob.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppRequestBean;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsBusinessLine;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsOffice;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrInvitationLink;
import com.adins.esignhubjob.model.webservice.vfirst.SendSmsVfirstResponse;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

@Component
public class GenericNotificationSenderLogic extends BaseLogic implements NotificationSenderLogic {

    private static final String WA_HEADER_PRIVY_VERIF_FAILED = "Verifikasi Privy Gagal";
    private static final String WA_HEADER_PRIVY_VERIF_SUCCESS = "Verifikasi Privy Berhasil";
    private static final String NOTES_VERIF_SUCCESS = "Verifikasi Berhasil";
    private static final String BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS = "%1$s : Send SMS Privy Verif Result Notification (%2$s)";
    private static final String BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA = "%1$s : Send WhatsApp Privy Verif Result Notification (%2$s)";

    private static final String KEY_INV_URL = "inv_url";
    private static final String KEY_NAME = "name";
    private static final String KEY_REJECT_MESSAGE = "reject_message";
    private static final String KEY_TENANT_NAME = "tenant_name";

    private static final String VFIRST_CODE_28681 = "28681";
    private static final String VFIRST_CODE_28682 = "28682";

    private static final String NOTES_SUFFIX_ERROR = " error";

    @Autowired private MessageTemplateLogic messageTemplateLogic;
    @Autowired private EmailSenderLogic emailSenderLogic;
    @Autowired private CommonLogic commonLogic;
    @Autowired private PersonalDataEncryptionLogic personalDataEncryptionLogic;
    @Autowired private SmsVfirstLogic smsVfirstLogic;
    @Autowired private SmsJatisLogic smsJatisLogic;
    @Autowired private WhatsAppJatisLogic whatsAppJatisLogic;
    @Autowired private WhatsAppHalosisLogic whatsAppHalosisLogic;

    @Override
    public void sendSuccessfulPrivyVerifEmailExternal(MsVendorRegisteredUser vendorUser, Context context) {
        Map<String, Object> templateParam = new HashMap<>();
        templateParam.put(KEY_NAME, vendorUser.getAmMsuser().getFullName());
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_EXT_EMAIL, templateParam);

        String[] recipient = new String[] {vendorUser.getSignerRegisteredEmail()};

        EmailInformationBean emailInfo = new EmailInformationBean();
        emailInfo.setSubject(template.getSubject());
        emailInfo.setBodyMessage(template.getBody());
        emailInfo.setTo(recipient);

        emailSenderLogic.sendEmail(emailInfo, null, context);
    }

    @Override
    public void sendSuccessfulPrivyVerifEmailInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        String receiverDetail = null;
        if ("1".equals(vendorUser.getEmailService())) {
            receiverDetail = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        } else {
            receiverDetail = vendorUser.getSignerRegisteredEmail();
        }
        
        MsVendor vendor = vendorUser.getMsVendor();

        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendor, tenant);
        if (null == invitationLink) {
            return;
        }

        // Send email
        String encryptedCode = commonLogic.encryptMessageToString(invitationLink.getInvitationCode());
        String urlEncoded = Tools.urlEncode(encryptedCode, context);
        String invitationUrl = System.getenv(Constants.ENV_VAR_REGIS_INV_BASE_URL) + urlEncoded;

        Map<String, Object> templateParam = new HashMap<>();
        templateParam.put(KEY_NAME, vendorUser.getAmMsuser().getFullName());
        templateParam.put(KEY_INV_URL, invitationUrl);

        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_INV_EMAIL, templateParam);
        String[] recipient = new String[] {vendorUser.getSignerRegisteredEmail()};

        EmailInformationBean emailInfo = new EmailInformationBean();
        emailInfo.setSubject(template.getSubject());
        emailInfo.setBodyMessage(template.getBody());
        emailInfo.setTo(recipient);

        emailSenderLogic.sendEmail(emailInfo, null, context);
    }

    @Override
    public void sendSuccessfulPrivyVerifSmsVfirstExternal(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        Map<String, Object> templateParam = new HashMap<>();
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_EXT_SMS, templateParam);

        String phone = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        SendSmsVfirstResponse response = smsVfirstLogic.sendSms(tenant, phone, template.getBody(), context);
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS, phone, NOTES_VERIF_SUCCESS);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SMS);
        MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USMS);
        MsVendor balmutVendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation mutation = new TrBalanceMutation();
        mutation.setMsLovByLovBalanceType(balanceType);
        mutation.setMsLovByLovTrxType(trxType);
        mutation.setMsTenant(tenant);
        mutation.setMsVendor(balmutVendor);
        mutation.setTrxNo(trxNo);
        mutation.setTrxDate(new Date());
        mutation.setVendorTrxNo(response.getGuid());
        mutation.setUsrCrt(context.getRequestId());
        mutation.setDtmCrt(new Date());
        mutation.setAmMsuser(vendorUser.getAmMsuser());

        if (response.getErrorCode() == null || (!VFIRST_CODE_28682.equals(response.getErrorCode()) && !VFIRST_CODE_28681.equals(response.getErrorCode()))) {
            mutation.setQty(-1);
            mutation.setNotes(notes);
        } else {
            mutation.setQty(0);
            mutation.setNotes(notes + NOTES_SUFFIX_ERROR);
        }

        daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
    }

    @Override
    public void sendSuccessfulPrivyVerifSmsVfirstInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        String receiverDetail = null;
        if ("1".equals(vendorUser.getEmailService())) {
            receiverDetail = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        } else {
            receiverDetail = vendorUser.getSignerRegisteredEmail();
        }

        MsVendor vendor = vendorUser.getMsVendor();
        
        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendor, tenant);
        if (null == invitationLink) {
            return;
        }

        String encryptedCode = commonLogic.encryptMessageToString(invitationLink.getInvitationCode());
        String urlEncoded = Tools.urlEncode(encryptedCode, context);
        String invitationUrl = System.getenv(Constants.ENV_VAR_REGIS_INV_BASE_URL) + urlEncoded;

        Map<String, Object> templateParam = new HashMap<>();
        templateParam.put(KEY_INV_URL, invitationUrl);
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_INV_SMS, templateParam);

        String phone = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        SendSmsVfirstResponse response = smsVfirstLogic.sendSms(tenant, phone, template.getBody(), context);
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS, phone, NOTES_VERIF_SUCCESS);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SMS);
        MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USMS);
        MsVendor balmutVendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation mutation = new TrBalanceMutation();
        mutation.setMsLovByLovBalanceType(balanceType);
        mutation.setMsLovByLovTrxType(trxType);
        mutation.setMsTenant(tenant);
        mutation.setMsVendor(balmutVendor);
        mutation.setTrxNo(trxNo);
        mutation.setTrxDate(new Date());
        mutation.setVendorTrxNo(response.getGuid());
        mutation.setMsBusinessLine(invitationLink.getMsBusinessLine());
        mutation.setMsOffice(invitationLink.getMsOffice());
        mutation.setUsrCrt(context.getRequestId());
        mutation.setDtmCrt(new Date());
        mutation.setAmMsuser(vendorUser.getAmMsuser());

        if (response.getErrorCode() == null || (!VFIRST_CODE_28682.equals(response.getErrorCode()) && !VFIRST_CODE_28681.equals(response.getErrorCode()))) {
            mutation.setQty(-1);
            mutation.setNotes(notes);
        } else {
            mutation.setQty(0);
            mutation.setNotes(notes + NOTES_SUFFIX_ERROR);
        }

        daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
    }

    @Override
    public void sendSuccessfulPrivyVerifSmsJatisExternal(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        Map<String, Object> templateParam = new HashMap<>();
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_EXT_SMS, templateParam);

        String phone = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

        JatisSmsRequestBean smsRequestBean = new JatisSmsRequestBean(tenant, null, null, phone, template.getBody(), trxNo, false);
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS, phone, NOTES_VERIF_SUCCESS);
        smsJatisLogic.sendSmsAndCutBalance(smsRequestBean, null, null, vendorUser.getAmMsuser(), notes, context);
    }

    @Override
    public void sendSuccessfulPrivyVerifSmsJatisInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        String receiverDetail = null;
        if ("1".equals(vendorUser.getEmailService())) {
            receiverDetail = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        } else {
            receiverDetail = vendorUser.getSignerRegisteredEmail();
        }

        MsVendor vendor = vendorUser.getMsVendor();
        
        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendor, tenant);
        if (null == invitationLink) {
            return;
        }
        
        String encryptedCode = commonLogic.encryptMessageToString(invitationLink.getInvitationCode());
        String urlEncoded = Tools.urlEncode(encryptedCode, context);
        String invitationUrl = System.getenv(Constants.ENV_VAR_REGIS_INV_BASE_URL) + urlEncoded;

        Map<String, Object> templateParam = new HashMap<>();
        templateParam.put(KEY_INV_URL, invitationUrl);
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_INV_SMS, templateParam);
        
        String phone = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        MsOffice office = invitationLink.getMsOffice();
        MsBusinessLine businessLine = invitationLink.getMsBusinessLine();

        JatisSmsRequestBean smsRequestBean = new JatisSmsRequestBean(tenant, office, businessLine, phone, template.getBody(), trxNo, false);
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS, phone, NOTES_VERIF_SUCCESS);
        smsJatisLogic.sendSmsAndCutBalance(smsRequestBean, null, null, vendorUser.getAmMsuser(), notes, context);
    }

    @Override
    public void sendSuccessfulPrivyVerifWhatsAppExternal(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        
        AmMsuser user = vendorUser.getAmMsuser();
        String phone = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA, phone, NOTES_VERIF_SUCCESS);

        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_EXT_WA);

        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(vendorUser.getAmMsuser().getFullName());

        JatisWhatsAppRequestBean requestBean = new JatisWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phone);
        requestBean.setAmMsuser(user);
        requestBean.setMsTenant(tenant);
        requestBean.setRemoveHeader(true);
        requestBean.setNotes("trxNo");
        requestBean.setNotes(notes);
        requestBean.setIsOtp(false);
        whatsAppJatisLogic.sendMessageAndCutBalance(requestBean, context);

    }

    @Override
    public void sendSuccessfulPrivyVerifWhatsAppInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        String receiverDetail = null;
        if ("1".equals(vendorUser.getEmailService())) {
            receiverDetail = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        } else {
            receiverDetail = vendorUser.getSignerRegisteredEmail();
        }

        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorUser.getMsVendor(), tenant);
        if (null == invitationLink) {
            return;
        }

        AmMsuser user = vendorUser.getAmMsuser();
        String phone = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA, phone, NOTES_VERIF_SUCCESS);
        
        String encryptedCode = commonLogic.encryptMessageToString(invitationLink.getInvitationCode());
        String encoded = Tools.urlEncode(encryptedCode, context);

        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_INV_WA);
        // Param WA: Nama, Encrypted inv_code
        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(user.getFullName());
        bodyTexts.add(encoded);

        JatisWhatsAppRequestBean requestBean = new JatisWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setButtonText(encoded);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phone);
        requestBean.setAmMsuser(user);
        requestBean.setMsTenant(tenant);
        requestBean.setRemoveHeader(true);
        requestBean.setNotes(notes);
        requestBean.setIsOtp(false);
        whatsAppJatisLogic.sendMessageAndCutBalance(requestBean, context);

    }

    @Override
    public void sendFailedPrivyVerifEmailExternal(RegisterExternalRequestBean registerRequest, String rejectMessage, Context context) {
        // Param: nama, reject message
        Map<String, Object> param = new HashMap<>();
        param.put(KEY_NAME, registerRequest.getNama());
        param.put(KEY_REJECT_MESSAGE, rejectMessage);

        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_FAILED_EXT_EMAIL, param);
        String[] recipient = new String[] {registerRequest.getEmail()};

        EmailInformationBean emailInfo = new EmailInformationBean();
        emailInfo.setSubject(template.getSubject());
        emailInfo.setBodyMessage(template.getBody());
        emailInfo.setTo(recipient);

        emailSenderLogic.sendEmail(emailInfo, null, context);
    }

    @Override
    public void sendFailedPrivyVerifEmailInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context) {
        Map<String, Object> param = new HashMap<>();
        param.put(KEY_NAME, userBean.getName());
        param.put(KEY_REJECT_MESSAGE, rejectMessage);
        param.put(KEY_TENANT_NAME, tenant.getTenantName());

        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_FAILED_INV_EMAIL, param);
        String[] recipient = new String[] {userBean.getEmail()};

        EmailInformationBean emailInfo = new EmailInformationBean();
        emailInfo.setSubject(template.getSubject());
        emailInfo.setBodyMessage(template.getBody());
        emailInfo.setTo(recipient);

        emailSenderLogic.sendEmail(emailInfo, null, context);
    }

    @Override
    public void sendFailedPrivyVerifSmsVfirstExternal(MsTenant tenant, RegisterExternalRequestBean registerRequest, String rejectMessage, Context context) {
        // Param: reject message
        Map<String, Object> param = new HashMap<>();
        param.put(KEY_REJECT_MESSAGE, rejectMessage);
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_FAILED_EXT_SMS, param);

        String phone = registerRequest.getTlp();
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

        SendSmsVfirstResponse response = smsVfirstLogic.sendSms(tenant, phone, template.getBody(), context);
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS, phone, rejectMessage);

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SMS);
        MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USMS);
        MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation mutation = new TrBalanceMutation();
        mutation.setMsLovByLovBalanceType(balanceType);
        mutation.setMsLovByLovTrxType(trxType);
        mutation.setMsTenant(tenant);
        mutation.setMsVendor(vendor);
        mutation.setTrxNo(trxNo);
        mutation.setTrxDate(new Date());
        mutation.setVendorTrxNo(trxNo);
        mutation.setUsrCrt(context.getRequestId());
        mutation.setDtmCrt(new Date());

        if (response.getErrorCode() == null || (!VFIRST_CODE_28682.equals(response.getErrorCode()) && !VFIRST_CODE_28681.equals(response.getErrorCode()))) {
            mutation.setQty(-1);
            mutation.setNotes(notes);
        } else {
            mutation.setQty(0);
            mutation.setNotes(notes + NOTES_SUFFIX_ERROR);
        }

        daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
    }

    @Override
    public void sendFailedPrivyVerifSmsVfirstInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context) {
        // Param: reject message, tenant name
        Map<String, Object> param = new HashMap<>();
        param.put(KEY_REJECT_MESSAGE, rejectMessage);
        param.put(KEY_TENANT_NAME, tenant.getTenantName());
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_FAILED_INV_SMS, param);

        String phone = userBean.getUserPhone();
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

        SendSmsVfirstResponse response = smsVfirstLogic.sendSms(tenant, phone, template.getBody(), context);
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS, phone, rejectMessage);

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SMS);
        MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USMS);
        MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation mutation = new TrBalanceMutation();
        mutation.setMsLovByLovBalanceType(balanceType);
        mutation.setMsLovByLovTrxType(trxType);
        mutation.setMsTenant(tenant);
        mutation.setMsVendor(vendor);
        mutation.setTrxNo(trxNo);
        mutation.setTrxDate(new Date());
        mutation.setVendorTrxNo(trxNo);
        mutation.setUsrCrt(context.getRequestId());
        mutation.setDtmCrt(new Date());

        if (response.getErrorCode() == null || (!VFIRST_CODE_28682.equals(response.getErrorCode()) && !VFIRST_CODE_28681.equals(response.getErrorCode()))) {
            mutation.setQty(-1);
            mutation.setNotes(notes);
        } else {
            mutation.setQty(0);
            mutation.setNotes(notes + NOTES_SUFFIX_ERROR);
        }

        daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
    }

    @Override
    public void sendFailedPrivyVerifSmsJatisExternal(MsTenant tenant, RegisterExternalRequestBean registerRequest, String rejectMessage, Context context) {
        // Param: reject message
        Map<String, Object> param = new HashMap<>();
        param.put(KEY_REJECT_MESSAGE, rejectMessage);
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_FAILED_EXT_SMS, param);

        String phone = registerRequest.getTlp();
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

        JatisSmsRequestBean requestBean = new JatisSmsRequestBean(tenant, phone, template.getBody(), trxNo, false);
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS, phone, rejectMessage);
        smsJatisLogic.sendSmsAndCutBalance(requestBean, null, null, null, notes, context);
    }

    @Override
    public void sendFailedPrivyVerifSmsJatisInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context) {
        Map<String, Object> param = new HashMap<>();
        param.put(KEY_REJECT_MESSAGE, rejectMessage);
        param.put(KEY_TENANT_NAME, tenant.getTenantName());
        MsMsgTemplate template = messageTemplateLogic.getAndParseContent(Constants.TEMPLATE_PRIVY_VERIF_FAILED_INV_SMS, param);
        
        String phone = userBean.getUserPhone();
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

        JatisSmsRequestBean requestBean = new JatisSmsRequestBean(tenant, phone, template.getBody(), trxNo, false);
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_SMS, phone, rejectMessage);
        smsJatisLogic.sendSmsAndCutBalance(requestBean, null, null, null, notes, context);
    }

    @Override
    public void sendFailedPrivyVerifWhatsAppExternal(MsTenant tenant, RegisterExternalRequestBean registerRequest, String rejectMessage, Context context) {
        // Param: nama, reject message
        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, Constants.TEMPLATE_PRIVY_VERIF_FAILED_EXT_WA);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String phone = registerRequest.getTlp();
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA, phone, rejectMessage);

        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(registerRequest.getNama());
        bodyTexts.add(rejectMessage);

        JatisWhatsAppRequestBean requestBean = new JatisWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phone);
        requestBean.setMsTenant(tenant);
        requestBean.setRemoveHeader(true);
        requestBean.setNotes(notes);
        requestBean.setIsOtp(false);
        whatsAppJatisLogic.sendMessageAndCutBalance(requestBean, context);
    }

    @Override
    public void sendFailedPrivyVerifWhatsAppInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context) {
        // Param: nama, reject message, tenant name

        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, Constants.TEMPLATE_PRIVY_VERIF_FAILED_INV_WA);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String phone = userBean.getUserPhone();
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA, phone, rejectMessage);

        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(userBean.getName());
        bodyTexts.add(rejectMessage);
        bodyTexts.add(tenant.getTenantName());

        JatisWhatsAppRequestBean requestBean = new JatisWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phone);
        requestBean.setMsTenant(tenant);
        requestBean.setRemoveHeader(true);
        requestBean.setNotes(notes);
        requestBean.setIsOtp(false);
        whatsAppJatisLogic.sendMessageAndCutBalance(requestBean, context);
    }

    @Override
    public void sendSuccessfulPrivyVerifHalosisWhatsAppExternal(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        
        List<String> headerTexts = new ArrayList<>();
        headerTexts.add(WA_HEADER_PRIVY_VERIF_SUCCESS);

        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(vendorUser.getAmMsuser().getFullName());

        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_EXT_WA);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String phone = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA, phone, NOTES_VERIF_SUCCESS);

        HalosisSendWhatsAppRequestBean requestBean = new HalosisSendWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setHeaderTexts(headerTexts);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phone);
        requestBean.setAmMsuser(vendorUser.getAmMsuser());
        requestBean.setMsTenant(tenant);
        requestBean.setNotes(notes);
        requestBean.setIsOtp(false);
        whatsAppHalosisLogic.sendMessage(requestBean, context);
    }

    @Override
    public void sendSuccessfulPrivyVerifHalosisWhatsAppInvitation(MsTenant tenant, MsVendorRegisteredUser vendorUser, Context context) {
        String receiverDetail = null;
        if ("1".equals(vendorUser.getEmailService())) {
            receiverDetail = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        } else {
            receiverDetail = vendorUser.getSignerRegisteredEmail();
        }

        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorUser.getMsVendor(), tenant);
        if (null == invitationLink) {
            return;
        }

        

        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, Constants.TEMPLATE_PRIVY_VERIF_SUCCESS_INV_WA);
        AmMsuser user = vendorUser.getAmMsuser();
        String phone = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA, phone, NOTES_VERIF_SUCCESS);
        
        String encryptedCode = commonLogic.encryptMessageToString(invitationLink.getInvitationCode());
        String encoded = Tools.urlEncode(encryptedCode, context);

        List<String> headerTexts = new ArrayList<>();
        headerTexts.add(WA_HEADER_PRIVY_VERIF_SUCCESS);
        
        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(user.getFullName());
        bodyTexts.add(encoded);

        HalosisSendWhatsAppRequestBean requestBean = new HalosisSendWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setHeaderTexts(headerTexts);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phone);
        requestBean.setAmMsuser(user);
        requestBean.setMsTenant(tenant);
        requestBean.setNotes(notes);
        requestBean.setIsOtp(false);
        whatsAppHalosisLogic.sendMessage(requestBean, context);

    }

    @Override
    public void sendFailedPrivyVerifHalosisWhatsAppExternal(MsTenant tenant, RegisterExternalRequestBean registerRequest, String rejectMessage, Context context) {
        
        List<String> headerTexts = new ArrayList<>();
        headerTexts.add(WA_HEADER_PRIVY_VERIF_FAILED);

        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(registerRequest.getNama());
        bodyTexts.add(rejectMessage);

        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, Constants.TEMPLATE_PRIVY_VERIF_FAILED_EXT_WA);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String phone = registerRequest.getTlp();
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA, phone, rejectMessage);

        HalosisSendWhatsAppRequestBean requestBean = new HalosisSendWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setHeaderTexts(headerTexts);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phone);
        requestBean.setMsTenant(tenant);
        requestBean.setNotes(notes);
        requestBean.setIsOtp(false);
        whatsAppHalosisLogic.sendMessage(requestBean, context);
    }

    @Override
    public void sendFailedPrivyVerifHalosisWhatsAppInvitation(MsTenant tenant, UserBean userBean, String rejectMessage, Context context) {
        
        List<String> headerTexts = new ArrayList<>();
        headerTexts.add(WA_HEADER_PRIVY_VERIF_FAILED);

        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(userBean.getName());
        bodyTexts.add(rejectMessage);
        bodyTexts.add(tenant.getTenantName());

        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, Constants.TEMPLATE_PRIVY_VERIF_FAILED_INV_WA);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String phone = userBean.getUserPhone();
        String notes = String.format(BALMUT_NOTES_PRIVY_VERIF_NOTIF_WA, phone, rejectMessage);

        HalosisSendWhatsAppRequestBean requestBean = new HalosisSendWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setHeaderTexts(headerTexts);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phone);
        requestBean.setMsTenant(tenant);
        requestBean.setNotes(notes);
        requestBean.setIsOtp(false);
        whatsAppHalosisLogic.sendMessage(requestBean, context);
    }

    
}
