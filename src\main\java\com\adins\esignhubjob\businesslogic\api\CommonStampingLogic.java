package com.adins.esignhubjob.businesslogic.api;

import java.util.List;

import com.adins.constants.enums.StampingDocumentType;
import com.adins.esignhubjob.model.custom.adins.StampingErrorDetailBean;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.aliyun.fc.runtime.Context;

public interface CommonStampingLogic {
    // get params
    long getStampingConnectionTimeout(StampingDocumentType documentType, Context context);
    long getStampingReadTimeout(StampingDocumentType documentType, Context context);
    String getAccountUsername(TrDocumentH documentH, Context context);
	String getAccountPassword(TrDocumentH documentH, Context context);
    String getStampDutyFee(Context context);

    // Updating DB
    void updateDocumentDMeteraiProcess(TrDocumentD document, String sdtProcess, Context context);
    void updateDocumentHMeteraiProcess(TrDocumentH documentH, String prosesMeterai, Context context);
    void incrementAgreementStampingErrorCount(TrDocumentH documentH, TrDocumentD document, StampingErrorDetailBean errorDetailBean, Context context);
    void incrementAgreementStampingErrorCountVida(TrDocumentH documentH, TrDocumentD document, StampingErrorDetailBean errorDetailBean, Context context);
    void incrementAgreementStampingErrorCountPajakku(TrDocumentH documentH, TrDocumentD document, StampingErrorDetailBean errorDetailBean, Context context);

    String getDocumentFileToUpload(TrDocumentD document, Context context);
    String getDocumentFileToUploadPajakku(TrDocumentD document, Context context);
    String getDocumentFileToUploadVida(TrDocumentD document, Context context);
    String getStampedDocument(TrDocumentD document, Context context);
    String getDocumentCategoryForUpload(TrDocumentD document, Context context);

    String getIntegrationValue(TrDocumentD document, Context context);

    boolean enoughSdtBalance(TrDocumentD document, int sdtNeeded, Context context);
    String getNotesForStampdutyBalanceMutation(TrDocumentD document, String reservedTrxNo, boolean isStampSuccessful, Context context);
    boolean allDocumentsProcessed(List<TrDocumentD> documents, Context context);

    int getStampingMaxErrorCount();

    // GEN_SDT Pajakku
    String getNamaDocForGenerate(TrDocumentD document, Context context);
	String getNoDocForGenerate(TrDocumentD document, Context context);
	String getTglDocForGenerate(TrDocumentD document, Context context);
	String getBalanceMutationNotesForGenerate(TrDocumentDStampduty docSdt, Context context);

    // STM_SDT parameter
	String getReasonForStamp(TrDocumentD document, Context context);
	String getOnPremStampDestination(TrDocumentD document, Context context);
	String getOnPremSpecimenPath(TrDocumentDStampduty docSdt, Context context);
	String getOnPremSource(TrDocumentD document, Context context);

    // UPL_OSS parameter
	int getFileCheckAttempts(Context context);
	long getFileCheckDelay(Context context);
}
