package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class EmailSendingException extends AdInsException {
    public EmailSendingException(String message, Throwable cause) {
        super(message, cause);
    }

    public EmailSendingException(String message) {
        super(message);
    }

    @Override
    public int getErrorCode() {
        // Nanti bisa ubah kalau tidak mau return code 9999 saja
        return StatusCode.UNKNOWN;
    }
}
