package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.VendorRegisteredUserDao;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.util.Tools;

@Component
@Transactional
public class VendorRegisteredUserDaoHbn extends BaseDaoHbn implements VendorRegisteredUserDao {

    @Override
    public MsVendorRegisteredUser getVendorRegisteredUser(AmMsuser user, MsVendor vendor) {
        Map<String, Object> params = new HashMap<>();
        params.put("user", user);
        params.put("vendor", vendor);

        return managerDAO.selectOne(
            "from MsVendorRegisteredUser vru "
            + "join fetch vru.amMsuser mu "
            + "join fetch vru.msVendor mv "
            + "where vru.amMsuser = :user "
            + "and vru.msVendor =: vendor ", params);
    }

    @Override
    public void updateVendorRegisteredUser(MsVendorRegisteredUser registeredUser) {
        registeredUser.setUsrUpd(Tools.maskData(registeredUser.getUsrUpd()));
        managerDAO.update(registeredUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateVendorRegisteredUserNewTran(MsVendorRegisteredUser registeredUser) {
        registeredUser.setUsrUpd(Tools.maskData(registeredUser.getUsrUpd()));
        managerDAO.update(registeredUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MsVendorRegisteredUser getVendorRegisteredUserNewTran(AmMsuser user, MsVendor vendor) {
        Map<String, Object> params = new HashMap<>();
        params.put("user", user);
        params.put("vendor", vendor);

        return managerDAO.selectOne(
            "from MsVendorRegisteredUser vru "
            + "join fetch vru.amMsuser mu "
            + "join fetch vru.msVendor mv "
            + "where vru.amMsuser = :user "
            + "and vru.msVendor =: vendor ", params);
    }

    @Override
    public void insertVendorRegisteredUser(MsVendorRegisteredUser registeredUser) {
        registeredUser.setUsrCrt(Tools.maskData(registeredUser.getUsrCrt()));
        managerDAO.insert(registeredUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertVendorRegisteredUserNewTran(MsVendorRegisteredUser registeredUser) {
        registeredUser.setUsrCrt(Tools.maskData(registeredUser.getUsrCrt()));
        managerDAO.insert(registeredUser);
    }
    
    @Override
    public MsVendorRegisteredUser getLastUpdatedVendorRegisteredUser(AmMsuser user) {
        Object[][] params = new Object[][] {
			{"idUser", user.getIdMsUser()}
		};

        StringBuilder query = new StringBuilder();
        query.append("select id_ms_vendor_registered_user from ( ")
            .append("   select id_ms_vendor_registered_user, ")
            .append("       case ")
            .append("           when dtm_upd is null then dtm_crt ")
            .append("           else dtm_upd ")
            .append("       end as max_date ")     
            .append("   from ms_vendor_registered_user ")
            .append("   where id_ms_user = :idUser ")
            .append(") as a ")
            .append("order by max_date desc ")
            .append("limit 1");

        BigInteger idVendorRegisteredUser = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
        if (null == idVendorRegisteredUser) {
            return null;
        }

        return getVendorRegisteredUserById(idVendorRegisteredUser);
    }

    @Override
    public MsVendorRegisteredUser getVendorRegisteredUserById(BigInteger idVendorRegisteredUser) {
        Object[][] queryParams = { 
				{ Restrictions.eq("idMsVendorRegisteredUser", idVendorRegisteredUser.longValue())}};
        return managerDAO.selectOne(MsVendorRegisteredUser.class, queryParams);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<MsVendorRegisteredUser> getDocHSigners(TrDocumentH documentH) {
        Map<String, Object> params = new HashMap<>();
        params.put(TrDocumentH.ID_DOCUMENT_H_HBM, documentH.getIdDocumentH());

        StringBuilder query = new StringBuilder();
        query
            .append("select distinct vru.id_ms_vendor_registered_user ")
            .append("from tr_document_h dh ")
            .append("join tr_document_d dd on dh.id_document_h = dd.id_document_h ")
            .append("join tr_document_d_sign dds on dd.id_document_d = dds.id_document_d ")
            .append("join ms_vendor_registered_user vru on dds.id_ms_user = vru.id_ms_user and dd.id_ms_vendor = vru.id_ms_vendor ")
            .append("where dh.id_document_h = :idDocumentH ");

        List<Map<String, Object>> results = managerDAO.selectAllNativeString(query.toString(), params);
        List<Long> idVendorRegisteredUsers = new ArrayList<>();
        for (Map<String, Object> result : results) {
            BigInteger idVendorRegisteredUser = (BigInteger) result.get("d0");
            idVendorRegisteredUsers.add(idVendorRegisteredUser.longValue());
        }

        return (List<MsVendorRegisteredUser>) managerDAO.list(
            "from MsVendorRegisteredUser vru "
            + "join fetch vru.amMsuser mu "
            + "where vru.idMsVendorRegisteredUser in :idVendorRegisteredUsers",
            new Object[][] {{ "idVendorRegisteredUsers", idVendorRegisteredUsers.toArray() }}).get(Constants.MAP_RESULT_LIST);
    }

    @Override
    public MsVendorRegisteredUser getLatestVendorRegisteredUserNewTran(AmMsuser user) {
        Map<String, Object> param = new HashMap<>();
        param.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());

        StringBuilder query = new StringBuilder();
        query
            .append("select id_ms_vendor_registered_user ")
            .append("from ms_vendor_registered_user ")
            .append("where id_ms_user = :idMsUser ")
            .append("order by id_ms_vendor_registered_user desc limit 1 ");

        BigInteger idMsVendorRegisteredUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), param);
        if (null == idMsVendorRegisteredUser) {
            return null;
        }

        Map<String, Object> finalParam = new HashMap<>();
        finalParam.put("idMsVendorRegisteredUser", idMsVendorRegisteredUser.longValue());

        return managerDAO.selectOne(
            "from MsVendorRegisteredUser vru "
            + "join fetch vru.amMsuser mu "
            + "where vru.idMsVendorRegisteredUser = :idMsVendorRegisteredUser ", finalParam);
    }

    @Override
    public MsVendorRegisteredUser getVendorRegisteredUserByIdNo(String idNo, String vendorCode) {
        if (StringUtils.isBlank(idNo) || StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where mv.isActive ='1' and  mu.hashedIdNo = :hashedIdNo and mv.vendorCode = :vendorCode", 
		new Object[][] {{ AmMsuser.HASHED_IDNO_HBM, Tools.getHashedString(idNo) }, {MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}});
    }

    @Override
    public MsVendorRegisteredUser getVendorRegisteredUserByPhone(String phone, String vendorCode) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where mv.isActive ='1' and  vuser.hashedSignerRegisteredPhone = :hashedPhone and mv.vendorCode = :vendorCode", 
		new Object[][] {{ AmMsuser.HASHED_PHONE_HBM, Tools.getHashedString(phone)}, {MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}});
    }

    @Override
    public MsVendorRegisteredUser getVendorRegisteredUserByEmail(String email, String vendorCode) {
        if (StringUtils.isBlank(email) || StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put("email", StringUtils.upperCase(email));
		params.put(MsVendor.VENDOR_CODE_HBM, vendorCode);
		
		return this.managerDAO.selectOne(
            "from MsVendorRegisteredUser vru "
            + "join fetch vru.msVendor v "
            + "join fetch vru.amMsuser u "
            + "where vru.signerRegisteredEmail = :email and v.vendorCode = :vendorCode ", params);
    }
}
