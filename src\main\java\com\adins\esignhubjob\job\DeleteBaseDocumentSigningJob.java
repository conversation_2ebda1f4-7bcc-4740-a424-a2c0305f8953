package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.aliyun.fc.runtime.Context;

public class DeleteBaseDocumentSigningJob extends BaseJobHandler {

    private static final String AUDIT = "DELETE BASE OSS DOC JOB";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        
        AmGeneralsetting gsSigned = daoFactory.getGeneralSettingDao().getGsObjByCode("KEEP_SIGNED_BASE_DOC_RANGE");
        int range = Integer.parseInt(gsSigned.getGsValue());
        
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -range);
        Date lastKeptDateSigned = cal.getTime();
        
        List<TrDocumentD> docDs = daoFactory.getDocumentDao().getListSignedDocumetDtoDeleteBaseDocument(lastKeptDateSigned);
        logger.info("Signed Based Document to Delete : " + docDs.size());
        
        for (TrDocumentD docD : docDs) {
            logger.info("Deleting document with id " + docD.getDocumentId());
            logicFactory.getAliyunOssCloudStorageLogic().deleteBaseSignDocument(docD, context);
            docD.setDtmUpd(new Date());
            docD.setUsrUpd(AUDIT);
            docD.setDocumentStorageStatus("1");
            daoFactory.getDocumentDao().updateDocumentDetail(docD);
        }
        
        AmGeneralsetting gsUnsigned = daoFactory.getGeneralSettingDao().getGsObjByCode("KEEP_UNSIGNED_BASE_DOC_RANGE");
        range = Integer.parseInt(gsUnsigned.getGsValue());
        
        Calendar cal2 = Calendar.getInstance();
        cal2.add(Calendar.DATE, -range);
        Date lastKeptDateUnsigned = cal2.getTime();
        
        Date startTime = new Date();
        
        List<TrDocumentD> unsignedDocDs = daoFactory.getDocumentDao().getListUnsignedDocumentDtoDeleteBaseDocument(lastKeptDateUnsigned);
        logger.info("Unsigned Based Document to Delete : " + unsignedDocDs.size());
        
        for (TrDocumentD docD : unsignedDocDs) {
            logger.info("Deleting document with id " + docD.getDocumentId());
            logicFactory.getAliyunOssCloudStorageLogic().deleteBaseSignDocument(docD, context);
            
            TrDocumentH docH = docD.getTrDocumentH();
            docH.setDtmUpd(new Date());
            docH.setUsrUpd(AUDIT);
            docH.setIsActive("0");
            daoFactory.getDocumentDao().updateDocumentHeader(docH);
            
            docD.setDocumentStorageStatus("1");
            docD.setDtmUpd(new Date());
            docD.setUsrUpd(AUDIT);
            daoFactory.getDocumentDao().updateDocumentDetail(docD);
        }
        
        long dataProcessed = (long) docDs.size() + unsignedDocDs.size();
        
        MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.LOV_CODE_SCHEDULER_DAILY);
        MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, Constants.LOV_CODE_DEL_BASEDOC_SIGNING_OSS);
        
        TrSchedulerJob insertJob = new TrSchedulerJob();
        insertJob.setDtmCrt(new Date());
        insertJob.setUsrCrt(AUDIT);
        insertJob.setDataProcessed(dataProcessed);
        insertJob.setSchedulerStart(startTime);
        insertJob.setSchedulerEnd(new Date());
        insertJob.setMsLovBySchedulerType(lovSchedulerType);
        insertJob.setMsLovByJobType(lovJobType);
        insertJob.setMailReminderCount((short) 0);
        
        daoFactory.getSchedulerJobDao().insertSchedulerJob(insertJob);
    }
    
}
