package com.adins.esignhubjob.businesslogic.impl.interfacing;

import com.aliyun.fc.runtime.Context;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.BalanceValidatorLogic;
import com.adins.esignhubjob.model.table.MsBalancevendoroftenant;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.webservice.adins.BalanceBean;
import com.adins.esignhubjob.model.webservice.adins.BalanceRequest;
import com.adins.esignhubjob.model.webservice.adins.BalanceResponse;
import com.adins.exceptions.EsignhubJobException;
import org.springframework.stereotype.Component;

@Component
public class GenericBalanceValidatorLogic extends BaseLogic implements BalanceValidatorLogic {
    @Override
	public void validateBalanceAvailabilityWithAmount(String balanceTypeCode, MsTenant tenant, MsVendor vendor, int amount, Context audit) {
		
		MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);

        
        BalanceRequest request = new BalanceRequest();
		request.setBalanceType(balanceType.getCode());
		request.setTenantCode(tenant.getTenantCode());
		request.setVendorCode(vendor.getVendorCode());

		int currentBalance = 0;
		try {
			currentBalance = getBalanceNotSecure(request, audit).getListBalance().get(0).getCurrentBalance().intValue();
		} catch (Exception e) {
			throw new EsignhubJobException(String.format("Balance %1$s not configured", balanceType.getDescription()));
		}

		if (currentBalance < amount) {
			throw new EsignhubJobException(String.format("Balance %1$s not enough", balanceType.getDescription()));
		}

	}

	private BalanceResponse getBalanceNotSecure(BalanceRequest request, Context audit) {
		return this.getBalance(request, audit);
	}

	private BalanceResponse getBalance(BalanceRequest request, Context audit) {
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		
		this.tenantVendorValidation(request.getTenantCode(), request.getVendorCode(), tenant, vendor, audit);

		return this.getBalanceByVendorAndTenant(request.getBalanceType(), tenant, vendor, audit);

	}

	private void tenantVendorValidation(String tenantCode, String vendorCode, MsTenant tenant, MsVendor vendor, 
			Context audit) {
		if (StringUtils.isBlank(tenantCode) || null == tenant) {
			throw new EsignhubJobException("Tenant cannot be null");
		}
		if (StringUtils.isBlank(vendorCode) || null == vendor) {
			throw new EsignhubJobException("Vendor cannot be null");
		}
	}

	private BalanceResponse getBalanceByVendorAndTenant(String balanceTypeCode, MsTenant tenant, MsVendor vendor,
			Context audit) {
		BalanceResponse response = new BalanceResponse();
		List<BalanceBean> listBalanceBean = new ArrayList<>();

		if(StringUtils.isBlank(balanceTypeCode)) {
			List<MsBalancevendoroftenant> listBalance = daoFactory.getVendorDao().getListBalanceByVendorTenant(tenant.getTenantCode(), vendor.getVendorCode());
			for (MsBalancevendoroftenant bvot : listBalance) {
				MsLov balanceTypeLov = bvot.getMsLov();
				BalanceBean balanceBean = new BalanceBean();

				this.setResponseBalanceByType(balanceBean, bvot.getMsTenant(), bvot.getMsVendor(), balanceTypeLov);
				
				listBalanceBean.add(balanceBean);
			}
		}
		else {
			MsLov balanceTypeLov = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);
			BalanceBean balanceBean = new BalanceBean();
			
			this.setResponseBalanceByType(balanceBean, tenant, vendor, balanceTypeLov);
			
			listBalanceBean.add(balanceBean);
		}
		response.setListBalance(listBalanceBean);
		return response;
	}

	private void setResponseBalanceByType(BalanceBean balanceBean, MsTenant tenant, MsVendor vendor,
			MsLov balanceTypeLov) {
		BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenant
				(tenant, vendor, balanceTypeLov);
		
		balanceBean.setLovGroup(balanceTypeLov.getLovGroup());
		balanceBean.setCode(balanceTypeLov.getCode());
		balanceBean.setDescription(balanceTypeLov.getDescription());
		balanceBean.setCurrentBalance(balance);	
	}
}
