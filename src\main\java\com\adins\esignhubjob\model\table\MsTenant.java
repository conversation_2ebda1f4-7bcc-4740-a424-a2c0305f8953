package com.adins.esignhubjob.model.table;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_tenant")
public class MsTenant extends ActivatableEntity  implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	public static final String ID_TENANT_HBM = "idMsTenant";
	public static final String TENANT_CODE_HBM = "tenantCode";
	public static final String TENANT_CODE_SMALL_HBM = "tenantcode";

	private long idMsTenant;
	private String tenantCode;
	private String tenantName;
	private String apiKey;
	private String faceVerifyService;
	private String emailService;
	private String thresholdBalance;
	private MsLov msLov;
	private String livenessKey;
	private String ekycImplClass;
	private String ekycUrl;
	private String ekycToken;
	private String refNumberLabel;
	private String emailReminderDest;
	private String rerunUrl;
	private MsEmailHosting msEmailHosting;
	private String userMustRegister;
	private String uploadUrl;
	private String automaticStampingAfterSign;
	private String meteraiStampingResultUrl;
	private String livenessFaceCompareServices;
	private String signRequestNotification;
	private String activationCallbackUrl;
	private String aesEncryptKey;
	private Integer invitationLinkActiveDuration;
	private String splitLivenessFaceCompareBill;
	private String useStandardUrl;
	private String useCustomSignImage;
	private String needPasswordForSigning;
	private String needOtpForSigning;
	private String useLivenessFacecompareFirst;
	private String registerAsDukcapilCheck;
	private Integer otpActiveDuration;
	private String useWaMessage;
	private String mustUseWaFirst;
	private String clientSigningRedirectUrl;
	private String clientCallbackUrl;
	private String clientActivationRedirectUrl;
	private String sentOtpByEmail;
	private MsLov lovSmsGateway;
	private String sendCertNotifBySms;
	private MsLov lovVendorStamping;
	private MsLov lovDefaultOtpSendingOption;
	
	private Set<TrBalanceDailyRecap> trBalanceDailyRecaps = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);
	private Set<MsDocTemplate> msDocTemplates = new HashSet<>(0);
	private Set<TrDocumentH> trDocumentHs = new HashSet<>(0);
	private Set<MsUseroftenant> msUseroftenants = new HashSet<>(0);
	private Set<AmGeneralsetting> amGeneralsettings = new HashSet<>(0);
	private Set<TrStampDuty> trStampDuties = new HashSet<>(0);
	private Set<MsOffice> msOffices = new HashSet<>(0);
	private Set<TrDocumentD> trDocumentDs = new HashSet<>(0);
	private Set<MsPaymentsigntypeoftenant> msPaymentsigntypeoftenants = new HashSet<>(0);
	private Set<MsVendoroftenant> msVendoroftenants = new HashSet<>(0);
	private Set<AmMsrole> amMsRoles = new HashSet<>(0);
	private Set<MsRegion> msRegions = new HashSet<>(0);
	private Set<MsBusinessLine> msBusinessLines = new HashSet<>(0);
	private Set<TrInvitationLink> trInvitationLinks = new HashSet<>(0);
	private Set<MsBalancevendoroftenant> msBalancevendoroftenants = new HashSet<>(0);
	private Set<TrErrorHistory> trErrorHistories = new HashSet<>(0);
	private Set<TrJobResult> trJobResults = new HashSet<>(0);
	private Set<TrJobUpdatePsreId> trJobUpdatePsreIds = new HashSet<>(0);
	private Set<TrClientCallbackRequest> trClientCallbackRequests = new HashSet<>(0);
	private Set<TrSchedulerJob> trSchedulerJobs = new HashSet<>(0);
	private Set<MsNotificationtypeoftenant> msNotificationtypeoftenants = new HashSet<>(0);
	private Set<MsTenantSettings> msTenantSettings = new HashSet<>(0);
//	private Set<TrRegistrationLivenessResult> trRegistrationLivenessResults = new HashSet<>(0);
	
	public MsTenant() {
	}

	public MsTenant(long idMsTenant, String tenantCode, String tenantName, String usrCrt, Date dtmCrt, String isActive, String apiKey) {
		this.idMsTenant = idMsTenant;
		this.tenantCode = tenantCode;
		this.tenantName = tenantName;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.isActive = isActive;
		this.apiKey = apiKey;
	}

	public MsTenant(long idMsTenant, String tenantCode, String tenantName, String apiKey, String faceVerifyService,
			String emailService, String thresholdBalance, MsLov msLov, String livenessKey, String ekycImplClass,
			String ekycUrl, String ekycToken, String refNumberLabel, String emailReminderDest, String rerunUrl,
			MsEmailHosting msEmailHosting, String userMustRegister, String uploadUrl, String automaticStampingAfterSign,
			String meteraiStampingResultUrl, String livenessFaceCompareServices, String signRequestNotification,
			String activationCallbackUrl, String aesEncryptKey, Integer invitationLinkActiveDuration,
			String splitLivenessFaceCompareBill, String useStandardUrl, String useCustomSignImage,
			String needPasswordForSigning, String needOtpForSigning, String useLivenessFacecompareFirst,
			String registerAsDukcapilCheck, Integer otpActiveDuration, String useWaMessage, String mustUseWaFirst,
			String clientSigningRedirectUrl, String clientCallbackUrl, String clientActivationRedirectUrl,
			String sentOtpByEmail, MsLov lovSmsGateway, Set<TrBalanceDailyRecap> trBalanceDailyRecaps,
			Set<TrBalanceMutation> trBalanceMutations, Set<MsDocTemplate> msDocTemplates, Set<TrDocumentH> trDocumentHs,
			Set<MsUseroftenant> msUseroftenants, Set<AmGeneralsetting> amGeneralsettings,
			Set<TrStampDuty> trStampDuties, Set<MsOffice> msOffices, Set<TrDocumentD> trDocumentDs,
			Set<MsPaymentsigntypeoftenant> msPaymentsigntypeoftenants, Set<MsVendoroftenant> msVendoroftenants,
			Set<AmMsrole> amMsRoles, Set<MsRegion> msRegions, Set<MsBusinessLine> msBusinessLines,
			Set<TrInvitationLink> trInvitationLinks, Set<MsBalancevendoroftenant> msBalancevendoroftenants,
			Set<TrErrorHistory> trErrorHistories, Set<TrJobResult> trJobResults, Set<TrJobUpdatePsreId> trJobUpdatePsreIds, MsLov lovVendorStamping) {
		super();
		this.idMsTenant = idMsTenant;
		this.tenantCode = tenantCode;
		this.tenantName = tenantName;
		this.apiKey = apiKey;
		this.faceVerifyService = faceVerifyService;
		this.emailService = emailService;
		this.thresholdBalance = thresholdBalance;
		this.msLov = msLov;
		this.livenessKey = livenessKey;
		this.ekycImplClass = ekycImplClass;
		this.ekycUrl = ekycUrl;
		this.ekycToken = ekycToken;
		this.refNumberLabel = refNumberLabel;
		this.emailReminderDest = emailReminderDest;
		this.rerunUrl = rerunUrl;
		this.msEmailHosting = msEmailHosting;
		this.userMustRegister = userMustRegister;
		this.uploadUrl = uploadUrl;
		this.automaticStampingAfterSign = automaticStampingAfterSign;
		this.meteraiStampingResultUrl = meteraiStampingResultUrl;
		this.livenessFaceCompareServices = livenessFaceCompareServices;
		this.signRequestNotification = signRequestNotification;
		this.activationCallbackUrl = activationCallbackUrl;
		this.aesEncryptKey = aesEncryptKey;
		this.invitationLinkActiveDuration = invitationLinkActiveDuration;
		this.splitLivenessFaceCompareBill = splitLivenessFaceCompareBill;
		this.useStandardUrl = useStandardUrl;
		this.useCustomSignImage = useCustomSignImage;
		this.needPasswordForSigning = needPasswordForSigning;
		this.needOtpForSigning = needOtpForSigning;
		this.useLivenessFacecompareFirst = useLivenessFacecompareFirst;
		this.registerAsDukcapilCheck = registerAsDukcapilCheck;
		this.otpActiveDuration = otpActiveDuration;
		this.useWaMessage = useWaMessage;
		this.mustUseWaFirst = mustUseWaFirst;
		this.clientSigningRedirectUrl = clientSigningRedirectUrl;
		this.clientCallbackUrl = clientCallbackUrl;
		this.clientActivationRedirectUrl = clientActivationRedirectUrl;
		this.sentOtpByEmail = sentOtpByEmail;
		this.lovSmsGateway = lovSmsGateway;
		this.lovVendorStamping = lovVendorStamping;
		this.trBalanceDailyRecaps = trBalanceDailyRecaps;
		this.trBalanceMutations = trBalanceMutations;
		this.msDocTemplates = msDocTemplates;
		this.trDocumentHs = trDocumentHs;
		this.msUseroftenants = msUseroftenants;
		this.amGeneralsettings = amGeneralsettings;
		this.trStampDuties = trStampDuties;
		this.msOffices = msOffices;
		this.trDocumentDs = trDocumentDs;
		this.msPaymentsigntypeoftenants = msPaymentsigntypeoftenants;
		this.msVendoroftenants = msVendoroftenants;
		this.amMsRoles = amMsRoles;
		this.msRegions = msRegions;
		this.msBusinessLines = msBusinessLines;
		this.trInvitationLinks = trInvitationLinks;
		this.msBalancevendoroftenants = msBalancevendoroftenants;
		this.trErrorHistories = trErrorHistories;
		this.trJobResults = trJobResults;
		this.trJobUpdatePsreIds = trJobUpdatePsreIds;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_tenant", unique = true, nullable = false)
	public long getIdMsTenant() {
		return this.idMsTenant;
	}
	
	public void setIdMsTenant(long idMsTenant) {
		this.idMsTenant = idMsTenant;
	}

	@Column(name = "tenant_code", nullable = false, length = 20)
	public String getTenantCode() {
		return this.tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	@Column(name = "tenant_name", nullable = false, length = 100)
	public String getTenantName() {
		return this.tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	@Column(name = "api_key", length = 80)
	public String getApiKey() {
		return this.apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "notif_type")
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}
	
	@Column(name = "face_verify_service", length = 1)
	public String getFaceVerifyService() {
		return this.faceVerifyService;
	}

	public void setFaceVerifyService(String faceVerifyService) {
		this.faceVerifyService = faceVerifyService;
	}

	@Column(name = "email_service", length = 20)
	public String getEmailService() {
		return this.emailService;
	}

	public void setEmailService(String emailService) {
		this.emailService = emailService;
	}

	@Column(name = "threshold_balance", length = 1000)
	public String getThresholdBalance() {
		return this.thresholdBalance;
	}

	public void setThresholdBalance(String thresholdBalance) {
		this.thresholdBalance = thresholdBalance;
	}
	
	@Column(name = "liveness_key", length = 25)
	public String getLivenessKey() {
		return this.livenessKey;
	}

	public void setLivenessKey(String livenessKey) {
		this.livenessKey = livenessKey;
	}
	@Column(name = "ekyc_impl_class", length = 80)
	public String getEkycImplClass() {
		return this.ekycImplClass;
	}

	public void setEkycImplClass(String ekycImplClass) {
		this.ekycImplClass = ekycImplClass;
	}

	@Column(name = "ekyc_url", length = 80)
	public String getEkycUrl() {
		return this.ekycUrl;
	}

	public void setEkycUrl(String ekycUrl) {
		this.ekycUrl = ekycUrl;
	}

	@Column(name = "ekyc_token", length = 80)
	public String getEkycToken() {
		return this.ekycToken;
	}

	public void setEkycToken(String ekycToken) {
		this.ekycToken = ekycToken;
	}
	
	@Column(name = "ref_number_label", length = 30)
	public String getRefNumberLabel() {
		return refNumberLabel;
	}

	public void setRefNumberLabel(String refNumberLabel) {
		this.refNumberLabel = refNumberLabel;
	}

	@Column(name = "email_reminder_dest", length = 300)
	public String getEmailReminderDest() {
		return this.emailReminderDest;
	}

	public void setEmailReminderDest(String emailReminderDest) {
		this.emailReminderDest = emailReminderDest;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_email_hosting")
	public MsEmailHosting getMsEmailHosting() {
		return this.msEmailHosting;
	}

	public void setMsEmailHosting(MsEmailHosting msEmailHosting) {
		this.msEmailHosting = msEmailHosting;
	}

	@Column(name = "rerun_url", length = 300)
	public String getRerunUrl() {
		return rerunUrl;
	}

	public void setRerunUrl(String rerunUrl) {
		this.rerunUrl = rerunUrl;
	}

	@Column(name = "user_must_register", length = 1)
	public String getUserMustRegister() {
		return userMustRegister;
	}

	public void setUserMustRegister(String userMustRegister) {
		this.userMustRegister = userMustRegister;
	}
	
	@Column(name = "upload_url", length = 100)
	public String getUploadUrl() {
		return uploadUrl;
	}

	public void setUploadUrl(String uploadUrl) {
		this.uploadUrl = uploadUrl;
	}
	
	@Column(name = "meterai_stamping_result_url", length = 100)
	public String getMeteraiStampingResultUrl() {
		return meteraiStampingResultUrl;
	}

	public void setMeteraiStampingResultUrl(String meteraiStampingResultUrl) {
		this.meteraiStampingResultUrl = meteraiStampingResultUrl;
	}

	@Column(name = "sign_request_notification", length = 1)
	public String getSignRequestNotification() {
		return signRequestNotification;
	}
	
	@Column(name = "liveness_facecompare_services", length = 1)
	public String getLivenessFaceCompareServices() {
		return livenessFaceCompareServices;
	}

	public void setLivenessFaceCompareServices(String livenessFaceCompareServices) {
		this.livenessFaceCompareServices = livenessFaceCompareServices;
	}

	public void setSignRequestNotification(String signRequestNotification) {
		this.signRequestNotification = signRequestNotification;
	}
	
	@Column(name = "activation_callback_url", length = 100)
	public String getActivationCallbackUrl() {
		return activationCallbackUrl;
	}

	public void setActivationCallbackUrl(String activationCallbackUrl) {
		this.activationCallbackUrl = activationCallbackUrl;
	}

	@Column(name = "aes_encrypt_key", length = 16)
	public String getAesEncryptKey() {
		return aesEncryptKey;
	}

	public void setAesEncryptKey(String aesEncryptKey) {
		this.aesEncryptKey = aesEncryptKey;
	}

	@Column(name = "invitation_link_active_duration")
	public Integer getInvitationLinkActiveDuration() {
		return invitationLinkActiveDuration;
	}

	public void setInvitationLinkActiveDuration(Integer invitationLinkActiveDuration) {
		this.invitationLinkActiveDuration = invitationLinkActiveDuration;
	}

	@Column(name = "split_liveness_face_compare_bill", length = 1)
	public String getSplitLivenessFaceCompareBill() {
		return splitLivenessFaceCompareBill;
	}

	public void setSplitLivenessFaceCompareBill(String splitLivenessFaceCompareBill) {
		this.splitLivenessFaceCompareBill = splitLivenessFaceCompareBill;
	}

	@Column(name = "use_standard_url", length = 1)
	public String getUseStandardUrl() {
		return useStandardUrl;
	}

	public void setUseStandardUrl(String useStandardUrl) {
		this.useStandardUrl = useStandardUrl;
	}

	@Column(name = "use_custom_sign_image", length = 1)
	public String getUseCustomSignImage() {
		return useCustomSignImage;
	}

	public void setUseCustomSignImage(String useCustomSignImage) {
		this.useCustomSignImage = useCustomSignImage;
	}
	
	@Column(name = "need_password_for_signing", length = 1)
	public String getNeedPasswordForSigning() {
		return needPasswordForSigning;
	}

	public void setNeedPasswordForSigning(String needPasswordForSigning) {
		this.needPasswordForSigning = needPasswordForSigning;
	}
	
	@Column(name = "need_otp_for_signing", length = 1)
	public String getNeedOtpForSigning() {
		return needOtpForSigning;
	}

	public void setNeedOtpForSigning(String needOtpForSigning) {
		this.needOtpForSigning = needOtpForSigning;
	}

	@Column(name = "use_liveness_facecompare_first", length = 1)
	public String getUseLivenessFacecompareFirst() {
		return useLivenessFacecompareFirst;
	}

	public void setUseLivenessFacecompareFirst(String useLivenessFacecompareFirst) {
		this.useLivenessFacecompareFirst = useLivenessFacecompareFirst;
	}
	
	@Column(name = "register_as_dukcapil_check", length = 1)
	public String getRegisterAsDukcapilCheck() {
		return registerAsDukcapilCheck;
	}

	public void setRegisterAsDukcapilCheck(String registerAsDukcapilCheck) {
		this.registerAsDukcapilCheck = registerAsDukcapilCheck;
	}

	@Column(name = "use_wa_message", length = 1)
	public String getUseWaMessage() {
		return useWaMessage;
	}

	public void setUseWaMessage(String useWaMessage) {
		this.useWaMessage = useWaMessage;
	}
	
	@Column(name = "must_use_wa_first", length = 1)
	public String getMustUseWaFirst() {
		return mustUseWaFirst;
	}

	public void setMustUseWaFirst(String mustUseWaFirst) {
		this.mustUseWaFirst = mustUseWaFirst;
	}


	@Column(name = "client_signing_redirect_url", length = 100)
	public String getClientSigningRedirectUrl() {
		return clientSigningRedirectUrl;
	}

	public void setClientSigningRedirectUrl(String clientSigningRedirectUrl) {
		this.clientSigningRedirectUrl = clientSigningRedirectUrl;
	}

	@Column(name = "client_callback_url", length = 100)
	public String getClientCallbackUrl() {
		return clientCallbackUrl;
	}

	public void setClientCallbackUrl(String clientCallbackUrl) {
		this.clientCallbackUrl = clientCallbackUrl;
	}

	@Column(name = "client_activation_redirect_url", length = 100)
	public String getClientActivationRedirectUrl() {
		return clientActivationRedirectUrl;
	}

	public void setClientActivationRedirectUrl(String clientActivationRedirectUrl) {
		this.clientActivationRedirectUrl = clientActivationRedirectUrl;
	}

	@Column(name = "sent_otp_by_email", length = 1)
	public String getSentOtpByEmail() {
		return sentOtpByEmail;
	}

	public void setSentOtpByEmail(String sentOtpByEmail) {
		this.sentOtpByEmail = sentOtpByEmail;
	}
	
	@Column(name = "otp_active_duration")
	public Integer getOtpActiveDuration() {
		return otpActiveDuration;
	}

	public void setOtpActiveDuration(Integer otpActiveDuration) {
		this.otpActiveDuration = otpActiveDuration;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sms_gateway")
	public MsLov getLovSmsGateway() {
		return lovSmsGateway;
	}
	
	@Column(name = "automatic_stamping_after_sign", length = 10)
	public String getAutomaticStampingAfterSign() {
		return automaticStampingAfterSign;
	}

	public void setAutomaticStampingAfterSign(String automaticStampingAfterSign) {
		this.automaticStampingAfterSign = automaticStampingAfterSign;
	}
	
	@Column(name = "send_cert_notif_by_sms", length = 1)
	public String getSendCertNotifBySms() {
		return sendCertNotifBySms;
	}

	public void setSendCertNotifBySms(String sendCertNotifBySms) {
		this.sendCertNotifBySms = sendCertNotifBySms;
	}

	public void setLovSmsGateway(MsLov lovSmsGateway) {
		this.lovSmsGateway = lovSmsGateway;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_vendor_stamping")
	public MsLov getLovVendorStamping() {
		return lovVendorStamping;
	}

	public void setLovVendorStamping(MsLov lovVendorStamping) {
		this.lovVendorStamping = lovVendorStamping;
	}

	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_default_otp_sending_options")
	public MsLov getLovDefaultOtpSendingOption() {
		return lovDefaultOtpSendingOption;
	}

	public void setLovDefaultOtpSendingOption(MsLov lovDefaultOtpSendingOption) {
		this.lovDefaultOtpSendingOption = lovDefaultOtpSendingOption;
	}
	
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrBalanceDailyRecap> getTrBalanceDailyRecaps() {
		return this.trBalanceDailyRecaps;
	}

	public void setTrBalanceDailyRecaps(Set<TrBalanceDailyRecap> trBalanceDailyRecaps) {
		this.trBalanceDailyRecaps = trBalanceDailyRecaps;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return this.trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsDocTemplate> getMsDocTemplates() {
		return this.msDocTemplates;
	}

	public void setMsDocTemplates(Set<MsDocTemplate> msDocTemplates) {
		this.msDocTemplates = msDocTemplates;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrDocumentH> getTrDocumentHs() {
		return this.trDocumentHs;
	}

	public void setTrDocumentHs(Set<TrDocumentH> trDocumentHs) {
		this.trDocumentHs = trDocumentHs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsUseroftenant> getMsUseroftenants() {
		return this.msUseroftenants;
	}

	public void setMsUseroftenants(Set<MsUseroftenant> msUseroftenants) {
		this.msUseroftenants = msUseroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<AmGeneralsetting> getAmGeneralsettings() {
		return this.amGeneralsettings;
	}

	public void setAmGeneralsettings(Set<AmGeneralsetting> amGeneralsettings) {
		this.amGeneralsettings = amGeneralsettings;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrStampDuty> getTrStampDuties() {
		return this.trStampDuties;
	}

	public void setTrStampDuties(Set<TrStampDuty> trStampDuties) {
		this.trStampDuties = trStampDuties;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsOffice> getMsOffices() {
		return this.msOffices;
	}

	public void setMsOffices(Set<MsOffice> msOffices) {
		this.msOffices = msOffices;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrDocumentD> getTrDocumentDs() {
		return this.trDocumentDs;
	}

	public void setTrDocumentDs(Set<TrDocumentD> trDocumentDs) {
		this.trDocumentDs = trDocumentDs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsPaymentsigntypeoftenant> getMsPaymentsigntypeoftenants() {
		return this.msPaymentsigntypeoftenants;
	}

	public void setMsPaymentsigntypeoftenants(Set<MsPaymentsigntypeoftenant> msPaymentsigntypeoftenants) {
		this.msPaymentsigntypeoftenants = msPaymentsigntypeoftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsVendoroftenant> getMsVendoroftenants() {
		return this.msVendoroftenants;
	}

	public void setMsVendoroftenants(Set<MsVendoroftenant> msVendoroftenants) {
		this.msVendoroftenants = msVendoroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<AmMsrole> getAmMsRoles() {
		return amMsRoles;
	}

	public void setAmMsRoles(Set<AmMsrole> amMsRoles) {
		this.amMsRoles = amMsRoles;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsRegion> getMsRegions() {
		return this.msRegions;
	}

	public void setMsRegions(Set<MsRegion> msRegions) {
		this.msRegions = msRegions;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsBusinessLine> getMsBusinessLines() {
		return this.msBusinessLines;
	}

	public void setMsBusinessLines(Set<MsBusinessLine> msBusinessLines) {
		this.msBusinessLines = msBusinessLines;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrInvitationLink> getTrInvitationLinks() {
		return this.trInvitationLinks;
	}

	public void setTrInvitationLinks(Set<TrInvitationLink> trInvitationLinks) {
		this.trInvitationLinks = trInvitationLinks;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsBalancevendoroftenant> getMsBalancevendoroftenants() {
		return this.msBalancevendoroftenants;
	}

	public void setMsBalancevendoroftenants(Set<MsBalancevendoroftenant> msBalancevendoroftenants) {
		this.msBalancevendoroftenants = msBalancevendoroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrErrorHistory> getTrErrorHistories() {
		return trErrorHistories;
	}

	public void setTrErrorHistories(Set<TrErrorHistory> trErrorHistories) {
		this.trErrorHistories = trErrorHistories;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrJobResult> getTrJobResults() {
		return trJobResults;
	}

	public void setTrJobResults(Set<TrJobResult> trJobResults) {
		this.trJobResults = trJobResults;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrJobUpdatePsreId> getTrJobUpdatePsreIds() {
		return trJobUpdatePsreIds;
	}

	public void setTrJobUpdatePsreIds(Set<TrJobUpdatePsreId> trJobUpdatePsreIds) {
		this.trJobUpdatePsreIds = trJobUpdatePsreIds;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrClientCallbackRequest> getTrClientCallbackRequests() {
		return trClientCallbackRequests;
	}

	public void setTrClientCallbackRequests(Set<TrClientCallbackRequest> trClientCallbackRequests) {
		this.trClientCallbackRequests = trClientCallbackRequests;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrSchedulerJob> getTrSchedulerJobs() {
		return trSchedulerJobs;
	}

	public void setTrSchedulerJobs(Set<TrSchedulerJob> trSchedulerJobs) {
		this.trSchedulerJobs = trSchedulerJobs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsNotificationtypeoftenant> getMsNotificationtypeoftenants() {
		return msNotificationtypeoftenants;
	}

	public void setMsNotificationtypeoftenants(Set<MsNotificationtypeoftenant> msNotificationtypeoftenants) {
		this.msNotificationtypeoftenants = msNotificationtypeoftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsTenantSettings> getMsTenantSettings() {
		return msTenantSettings;
	}

	public void setMsTenantSettings(Set<MsTenantSettings> msTenantSettings) {
		this.msTenantSettings = msTenantSettings;
	}
}