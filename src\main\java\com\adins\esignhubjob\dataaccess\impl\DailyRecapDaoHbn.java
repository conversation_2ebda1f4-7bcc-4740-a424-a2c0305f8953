package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.DailyRecapDao;
import com.adins.esignhubjob.model.custom.adins.BalanceTopUpBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceDailyRecap;
import com.adins.util.Tools;

@Component
@Transactional
public class DailyRecapDaoHbn extends BaseDaoHbn implements DailyRecapDao {

	@Override
	public int countQtyDailyRecap(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor) {
		StringBuilder query = new StringBuilder();
		query.append(" SELECT SUM(COALESCE(recap_total_balance,0)+ COALESCE(number_of_use,0)) AS Saldo ")
				.append(" FROM ms_lov ")
				.append(" LEFT JOIN LATERAL ( ")
					.append(" SELECT recap_total_balance, recap_date, id_ms_tenant, id_ms_vendor ")
					.append(" FROM tr_balance_daily_recap bdc ")
					.append(" WHERE bdc.lov_balance_type = ms_lov.id_lov ")
						.append(" AND bdc.recap_date < TO_DATE(:dateRecap ,'"+ Constants.DATE_FORMAT+"')")
						.append(" AND id_ms_tenant = :idMsTenant ")
						.append(" AND id_ms_vendor = :idMsVendor ")
					.append(" ORDER BY bdc.recap_date DESC LIMIT 1 ")
				.append(" ) bdc ON TRUE ")
				.append(" LEFT JOIN LATERAL ( ")
					.append(" SELECT SUM(COALESCE(bm.qty,0)) AS number_of_use ")
					.append(" FROM tr_balance_mutation bm ")
					.append(" JOIN ms_lov mlov ON mlov.id_lov = lov_trx_type ")
					.append(" WHERE bm.lov_balance_type = ms_lov.id_lov ")
						.append(" AND DATE(bm.trx_date)>bdc.recap_date AND DATE(bm.trx_date)<=TO_DATE(:dateRecap ,'"+Constants.DATE_FORMAT+"')")						
						.append(" AND bm.id_ms_tenant=bdc.id_ms_tenant ")
						.append(" AND bm.id_ms_vendor=bdc.id_ms_vendor ")
				.append(" ) bm ON TRUE ")
				.append(" WHERE lov_group= :lovGroupBalanceType ")
				.append(" AND code= :codeLovBalanceType ");
		
		Object[][] queryParams = { 
				{ "dateRecap", dateRecap},
				{ MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant()},
				{ MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor()},
				{ "lovGroupBalanceType", StringUtils.upperCase(balanceType.getLovGroup())},
				{ "codeLovBalanceType", StringUtils.upperCase(balanceType.getCode())}
		};
		return ((BigDecimal) this.managerDAO.selectOneNativeString(query.toString(), queryParams)).intValue();
	}

	@Override
	public TrBalanceDailyRecap getDailyRecap(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor) {
		TrBalanceDailyRecap dailyRecap = new TrBalanceDailyRecap();

		StringBuilder query = new StringBuilder();
		query.append(" SELECT id_balance_daily_recap, recap_date, recap_total_balance ")
				.append(" FROM tr_balance_daily_recap bdc ")
				.append(" WHERE bdc.lov_balance_type = :idLov ")
					.append(" AND bdc.recap_date = TO_DATE(:dateRecap ,'"+Constants.DATE_FORMAT+"') ") //check yesterday recap
					.append(" AND id_ms_tenant = :idMsTenant ")
					.append(" AND id_ms_vendor = :idMsVendor ");
				
		Object[][] queryParams = { 
				{ "dateRecap", dateRecap},
				{ MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant()},
				{ MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor()},
				{ MsLov.ID_LOV_HBM, balanceType.getIdLov()}
		};

		List<Map<String,Object>> resultDailyRecap = this.managerDAO.selectAllNativeString(query.toString(), queryParams);
		if(!resultDailyRecap.isEmpty()) {
			for (Map<String,Object> dailyRecapData : resultDailyRecap) {
				dailyRecap.setIdBalanceDailyRecap(((BigInteger) dailyRecapData.get("d0")).longValue());		
				dailyRecap.setRecapDate((Date) dailyRecapData.get("d1"));
				dailyRecap.setRecapTotalBalance((Integer) dailyRecapData.get("d2"));
				dailyRecap.setMsLov(balanceType);
				dailyRecap.setMsTenant(tenant);
				dailyRecap.setMsVendor(vendor);

			}
		}
		return dailyRecap;
	}

	@Override
	public void updateTrBalanceDailyRecap(TrBalanceDailyRecap dailyRecap){
		dailyRecap.setUsrUpd(Tools.maskData(dailyRecap.getUsrUpd()));
        managerDAO.update(dailyRecap);
	}

	
	@Override
	public void insertTrBalanceDailyRecap(TrBalanceDailyRecap dailyRecap) {
		dailyRecap.setUsrUpd(Tools.maskData(dailyRecap.getUsrUpd()));
		this.managerDAO.insert(dailyRecap);
	}

	@Override 
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<BalanceTopUpBean> getBalanceTopUp (MsLov balanceType , MsTenant tenant, MsVendor vendor){
		StringBuilder query = new StringBuilder();
		List<BalanceTopUpBean> list = new ArrayList<BalanceTopUpBean>();
		query.append("select btu.id_balance_top_up, bm.qty + coalesce(bmuse.totaluse,0) as availableBalance ")
		.append(" from tr_balance_top_up btu ")
		.append(" JOIN TR_balance_mutation  bm on btu.id_balance_mutation = bm.id_balance_mutation")
		.append(" join lateral (select sum(qty) totaluse from tr_balance_mutation bmuse ")
						.append("where bmuse.lov_balance_type = :idBalanceType ")
						.append("and bmuse.id_balance_top_up = btu.id_balance_top_up ")
						.append("and bmuse.id_ms_tenant = :idMsTenant ")
						.append("and bmuse.id_ms_vendor = :idMsVendor ")
						.append(")bmuse on true ")
		.append(" where bm.lov_balance_type  =:idBalanceType ")
		.append(" and btu.is_used ='0'")
		.append(" and bm.id_ms_tenant = :idMsTenant")
		.append(" and bm.id_ms_vendor = :idMsVendor ")
		.append(" order by btu.id_balance_top_up ASC ");
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), 
				new Object[][] {{"idBalanceType", balanceType.getIdLov()},
			{MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant()},
			{MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor()}});
		
		
		Iterator<Map<String, Object>> itr = result.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			BalanceTopUpBean bean = new BalanceTopUpBean();
			bean.setIdBalanceTopUp(((BigInteger) map.get("d0")).longValue());
			bean.setAvailableBalance(((BigInteger) map.get("d1")).longValue());
			list.add(bean);
		}
		return list;
		
	}
    
}
