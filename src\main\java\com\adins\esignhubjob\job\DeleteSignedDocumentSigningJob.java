package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.aliyun.fc.runtime.Context;

public class DeleteSignedDocumentSigningJob extends BaseJobHandler {

    @Autowired AliyunOssCloudStorageLogic cloudStorageLogic;

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {
        AmGeneralsetting gsSigned = daoFactory.getGeneralSettingDao().getGsObjByCode("KEEP_SIGNED_DOC_RANGE");
        int range = Integer.parseInt(gsSigned.getGsValue());

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -range);
        Date lastKeptDate = cal.getTime();

        Date startTime = new Date();

        List<TrDocumentD> signedDocumentDs = daoFactory.getDocumentDao().getListSignedDocumentDToDeleteSignedDocument(lastKeptDate);
        logger.info("Signed Based Document to Delete : " + signedDocumentDs.size());

        for (TrDocumentD docD : signedDocumentDs) {
            logger.info("Deleting document with id " + docD.getDocumentId());
            logicFactory.getAliyunOssCloudStorageLogic().deleteSignedDocument(docD, context);

            docD.setDocumentStorageStatus("2");
            docD.setDtmUpd(new Date());
            docD.setUsrUpd("DELETE SIGNED DOCUMENT OSS JOB");
            daoFactory.getDocumentDao().updateDocumentDetail(docD);
        }

        long dataProcessed = signedDocumentDs.size();

        MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.LOV_CODE_SCHEDULER_WEEKLY);
        MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, Constants.LOV_CODE_DEL_SIGNEDDOC_SIGNING_OSS);

        TrSchedulerJob insertJob = new TrSchedulerJob();
        insertJob.setDtmCrt(new Date());
        insertJob.setUsrCrt("DELETE SIGNED DOCUMENT OSS JOB");
        insertJob.setDataProcessed(dataProcessed);
        insertJob.setSchedulerStart(startTime);
        insertJob.setSchedulerEnd(new Date());
        insertJob.setMsLovBySchedulerType(lovSchedulerType);
        insertJob.setMsLovByJobType(lovJobType);
        insertJob.setMailReminderCount((short) 0);

        daoFactory.getSchedulerJobDao().insertSchedulerJob(insertJob);

    }
    
}
