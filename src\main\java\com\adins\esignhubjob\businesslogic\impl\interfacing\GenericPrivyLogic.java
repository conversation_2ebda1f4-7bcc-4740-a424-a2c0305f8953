package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.PrivyLogic;
import com.adins.esignhubjob.model.custom.adins.RegisterExternalRequestBean;
import com.adins.esignhubjob.model.custom.adins.UserBean;
import com.adins.esignhubjob.model.custom.privy.PrivyDocumentBean;
import com.adins.esignhubjob.model.custom.privy.PrivySignLocation;
import com.adins.esignhubjob.model.custom.privy.PrivySignatureSignRequestBean;
import com.adins.esignhubjob.model.table.AmMemberofrole;
import com.adins.esignhubjob.model.table.AmMsrole;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.AmUserPersonalData;
import com.adins.esignhubjob.model.table.MsEmailHosting;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsOffice;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsUseroftenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.PersonalDataBean;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;
import com.adins.esignhubjob.model.table.custom.ZipcodeCityBean;
import com.adins.esignhubjob.model.webservice.privy.PrivyRegisterStatusV2Response;
import com.adins.esignhubjob.model.webservice.privy.PrivyDocumentStatusResponse;
import com.adins.esignhubjob.model.webservice.privy.PrivyRegisterStatusRequest;
import com.adins.esignhubjob.model.webservice.privy.PrivyRegisterStatusResponse;
import com.adins.esignhubjob.model.webservice.privy.PrivySignRequest;
import com.adins.esignhubjob.model.webservice.privy.PrivySignResponse;
import com.adins.exceptions.EsignhubJobException;
import com.adins.util.PasswordHash;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
public class GenericPrivyLogic extends BaseLogic implements PrivyLogic {

    @Autowired private PersonalDataEncryptionLogic personalDataEncryptionLogic;

    // Privy register reject code
	public static final String REJECT_CODE_PRVS002 = "PRVS002";
	public static final String REJECT_CODE_PRVN002 = "PRVN002";
	public static final String REJECT_CODE_PRVN004 = "PRVN004";
	public static final String REJECT_CODE_PRVN005 = "PRVN005";
	public static final String REJECT_CODE_PRVN009 = "PRVN009";
	public static final String REJECT_CODE_PRVD005 = "PRVD005";
	public static final String REJECT_CODE_PRVD009 = "PRVD009";

    private PrivySignRequest prepareSignRequest(List<TrDocumentDSign> signLocations) {
        PrivySignatureSignRequestBean signature = new PrivySignatureSignRequestBean();
        signature.setVisibility(true);

        List<PrivyDocumentBean> documentBeans = new ArrayList<>();
        for (TrDocumentDSign signLocation : signLocations) {
            String docToken = signLocation.getTrDocumentD().getPsreDocumentId();
            Integer page = signLocation.getSignPage();

            String signLocationJson = signLocation.getPrivySignLocation();
            PrivySignLocation privySignLocation = gson.fromJson(signLocationJson, PrivySignLocation.class);

            PrivyDocumentBean bean = new PrivyDocumentBean();
            bean.setDocToken(docToken);
            bean.setPage(String.valueOf(page));
            bean.setX(String.valueOf(privySignLocation.getX()));
            bean.setY(String.valueOf(privySignLocation.getY()));
            documentBeans.add(bean);
        }

        PrivySignRequest request = new PrivySignRequest();
        request.setSignature(signature);
        request.setDocuments(documentBeans);
        return request;
    }

    @Override
    public PrivySignResponse signDocuments(TrDocumentSigningRequest signingRequest, AmMsuser user, List<TrDocumentDSign> signLocations, MsVendoroftenant vendoroftenant, Context context) throws IOException {
        MsVendor vendor = vendoroftenant.getMsVendor();
        MsVendorRegisteredUser vendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUser(user, vendor);

        String username = vendoroftenant.getClientId();
        String password = vendoroftenant.getClientSecret();
        
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.PRIVY_MERCHANT_KEY, System.getenv(Constants.ENV_VAR_PRIVY_MERCHANT_KEY));
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.buildBasicAuthorization(username, password));
        headerMap.put("Token", vendorRegisteredUser.getVendorAccessToken());
        Headers headers = Headers.of(headerMap);
        
        PrivySignRequest request = prepareSignRequest(signLocations);
        String jsonRequest = gson.toJson(request);
        context.getLogger().info(String.format("Request ID %1$s, Sign Privy request: %2$s", signingRequest.getIdDocumentSigningRequest(), jsonRequest));
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));
        
        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(System.getenv(Constants.ENV_VAR_PRIVY_SIGN_URL))
            .post(body)
            .build();
        
        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(90, TimeUnit.SECONDS)
            .build();
        
        Response okHttResponse = client.newCall(okHttpRequest).execute();
        String jsonResponse = okHttResponse.body().string();
        
        String responseCode = StringUtils.isBlank(okHttResponse.message()) ? String.valueOf(okHttResponse.code()) : okHttResponse.code() + " " + okHttResponse.message();
        context.getLogger().info(String.format("Request ID %1$s, Sign Privy response code: %2$s, body: %3$s", signingRequest.getIdDocumentSigningRequest(), responseCode, jsonResponse));
        
        return gson.fromJson(jsonResponse, PrivySignResponse.class);

    }

    @Override
    public PrivyDocumentStatusResponse checkDocumentStatus(TrDocumentSigningRequest signingRequest, TrDocumentD document, MsVendoroftenant vendoroftenant, Context context) throws IOException {
        TrDocumentH documentH = document.getTrDocumentH();
        String docToken = document.getPsreDocumentId();
        String url = System.getenv(Constants.ENV_VAR_PRIVY_CHECK_DOC_STATUS_URL) + docToken;

        String username = vendoroftenant.getClientId();
        String password = vendoroftenant.getClientSecret();

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.buildBasicAuthorization(username, password));
        headerMap.put(HttpHeaders.PRIVY_MERCHANT_KEY, System.getenv(Constants.ENV_VAR_PRIVY_MERCHANT_KEY));
        Headers headers = Headers.of(headerMap);

        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
            .headers(headers)
            .url(url)
            .build();
        
        context.getLogger().info(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, Check Privy document status with URL: %4$s", signingRequest.getIdDocumentSigningRequest(), documentH.getRefNumber(), document.getDocumentId(), url));
        Response response = client.newCall(request).execute();
        String jsonResponse = response.body().string();
        String responseCode = StringUtils.isBlank(response.message()) ? String.valueOf(response.code()) : response.code() + " " + response.message();
        context.getLogger().info(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, Check Privy document status response code: %4$s, body: %5$s", signingRequest.getIdDocumentSigningRequest(), documentH.getRefNumber(), document.getDocumentId(), responseCode, jsonResponse));
        return gson.fromJson(jsonResponse, PrivyDocumentStatusResponse.class);
    }

    @Override
    public PrivyRegisterStatusResponse checkRegisterStatus(MsVendoroftenant vendoroftenant, String requestId, Context context) throws IOException {
        String username = vendoroftenant.getClientId();
        String password = vendoroftenant.getClientSecret();
        String authToken = HttpHeaders.buildBasicAuthorization(username, password);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, authToken);
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        headerMap.put(HttpHeaders.PRIVY_MERCHANT_KEY, System.getenv(Constants.ENV_VAR_PRIVY_MERCHANT_KEY));
        Headers headers = Headers.of(headerMap);

        PrivyRegisterStatusRequest request = new PrivyRegisterStatusRequest();
        request.setByField("request_id");
		request.setValue(requestId);
		String jsonRequest = gson.toJson(request);
        context.getLogger().info(String.format("Request ID %1$s, check Privy register status request: %2$s", requestId, jsonRequest));
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));
        
        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(System.getenv(Constants.ENV_VAR_PRIVY_CHECK_REG_STATUS_URL))
            .post(body)
            .build();

        OkHttpClient client  = new OkHttpClient.Builder().build();
        Response okHttpResponse = client.newCall(okHttpRequest).execute();
        String jsonResponse = okHttpResponse.body().string();
        context.getLogger().info(String.format("Request ID %1$s, check Privy register status response: %2$s", requestId, jsonResponse));
        return gson.fromJson(jsonResponse, PrivyRegisterStatusResponse.class);
    }

    @Override
    public PrivyRegisterStatusV2Response checkRegisterStatusV2(TrJobCheckRegisterStatus jobCheckRegisterStatus, MsVendoroftenant vendoroftenant, String userToken, Context context) throws IOException {
        String username = vendoroftenant.getClientId();
        String password = vendoroftenant.getClientSecret();
        String authToken = HttpHeaders.buildBasicAuthorization(username, password);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, authToken);
        headerMap.put(HttpHeaders.PRIVY_MERCHANT_KEY, System.getenv(Constants.ENV_VAR_PRIVY_MERCHANT_KEY));
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.MULTIPART_FORMDATA);
        Headers headers = Headers.of(headerMap);

        context.getLogger().info(String.format("Job ID %1$s, Check Privy register status for token: %2$s", jobCheckRegisterStatus.getIdJobCheckRegisterStatus(), userToken));

        RequestBody body = new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("token", userToken)
            .build();

        Request request = new Request.Builder()
            .url(System.getenv(Constants.ENV_VAR_PRIVY_CHECK_REG_STATUS_URL_V2))
            .headers(headers)
            .post(body)
            .build();

        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(10L, TimeUnit.SECONDS)
            .readTimeout(60L, TimeUnit.SECONDS)
            .build();

        Response response = client.newCall(request).execute();
        String jsonResponse = response.body().string();
        context.getLogger().info(String.format("Job ID %1$s, Check Privy register status response: %2$s", jobCheckRegisterStatus.getIdJobCheckRegisterStatus(), jsonResponse));

        return gson.fromJson(jsonResponse, PrivyRegisterStatusV2Response.class);
    }

    private boolean doesTenantAllowEmptyPassword(MsTenant tenant) {
        MsTenantSettings setting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "ALLOW_NO_PASSWORD_FOR_ACTIVATION");
		if (null == setting || StringUtils.isBlank(setting.getSettingValue())) {
			return false;
		}
		
		return "1".equals(setting.getSettingValue());
    }

    @Override
    public AmMsuser insertRegisteredUser(RegisterExternalRequestBean request, String privyId, MsTenant tenant, MsVendor vendor, Context context) {
        
        String hashedPhone = Tools.getHashedString(request.getTlp());
        String hashedIdNo = Tools.getHashedString(request.getIdKtp());
        String emailService = (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) ? "1" :"0";
        MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingByIdNewTrx(request.getIdEmailHosting());
        AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(Constants.ROLE_CUSTOMER, tenant.getTenantCode());

        AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(request.getIdKtp());
        if (null == user) {
            MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCodeNewTran(tenant.getTenantCode());
            String[] separatedName = request.getNama().split(" ");

            user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(request.getEmail()));
			user.setFullName(StringUtils.upperCase(request.getNama()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider("DB");

            if (doesTenantAllowEmptyPassword(tenant)) {
                user.setPassword("noPassword");
            } else {
                user.setPassword(PasswordHash.createHash(request.getPassword()));
            }
			
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("0");
			user.setUsrCrt(context.getRequestId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
        } else {
            user.setIsDormant("0");
            user.setUsrUpd(context.getRequestId());
            user.setDtmUpd(new Date());
            daoFactory.getUserDao().updateUserNewTran(user);
        }

        insertUnregisteredUseroftenant(user, tenant, context);

        MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserNewTran(user, vendor);
        if (null == vendorUser) {
            byte[] phoneBytea = personalDataEncryptionLogic.encryptFromString(request.getTlp());
            Date activatedDate = new Date();

            String expiredTime = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_PRIVY_CERTIFICATE_EXPIRE_TIME);

            if (expiredTime == null) {
                throw new EsignhubJobException("Failed to get general setting Privy expired time ");
            }

            Date expiredDate = DateUtils.addDays(activatedDate, Integer.parseInt(expiredTime));

            vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
			vendorUser.setIsActive("1");
			vendorUser.setUsrCrt(context.getRequestId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(vendor);
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(privyId);
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
        }

        PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false, context);
        if (null == personalData || null == personalData.getUserPersonalData()) {
            Date dob = Tools.formatStringToDate(request.getTglLahir(), Constants.DATE_FORMAT);
            byte[] selfiePhoto = StringUtils.isBlank(request.getSelfPhoto()) ? null : Tools.imageStringToByteArray(request.getSelfPhoto());
            byte[] idPhoto = StringUtils.isBlank(request.getIdPhoto()) ? null : Tools.imageStringToByteArray(request.getIdPhoto());

            ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(request.getProvinsi());
			zipcodeCityBean.setKota(request.getKota());
			zipcodeCityBean.setKecamatan(request.getKecamatan());
			zipcodeCityBean.setKelurahan(request.getKelurahan());
			zipcodeCityBean.setZipcode(request.getKodePos());

            PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(context.getRequestId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(request.getJenisKelamin()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(request.getTmpLahir()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(request.getIdKtp());
			personalDataBean.setPhoneRaw(request.getTlp());
			personalDataBean.setAddressRaw(StringUtils.upperCase(request.getAlamat()));
            daoFactory.getUserDao().insertUserPersonalDataNewTrx(personalDataBean, context);
        }

        insertUnregisteredMemberofrole(user, role, context);

        return user;
    }

    @Override
    public String getCheckRegisterStatusMessage(PrivyRegisterStatusV2Response response, Context context) {
        if (null == response || null == response.getData()) {
			return StringUtils.EMPTY;
		}
		
		if ("waiting".equalsIgnoreCase(response.getData().getStatus())) {
			return response.getMessage();
		}
		
		if ("verified".equalsIgnoreCase(response.getData().getStatus()) || "registered".equalsIgnoreCase(response.getData().getStatus())) {
			return "Registrasi berhasil";
		}
		
		// Get verification result based on error code
		String rejectCode = response.getData().getReject().getCode();
		if (REJECT_CODE_PRVS002.equals(rejectCode)) {
			return "Verifikasi user gagal. Foto Diri tidak sesuai.";
		}
		
		if (REJECT_CODE_PRVN002.equals(rejectCode)
				|| REJECT_CODE_PRVN004.equals(rejectCode)
				|| REJECT_CODE_PRVN009.equals(rejectCode)
				|| REJECT_CODE_PRVD009.equals(rejectCode)) {
			
			return "Verifikasi user gagal. NIK tidak sesuai.";
		}
		
		if (REJECT_CODE_PRVN005.equals(rejectCode)) {
			return "Verifikasi user gagal. Nama Lengkap dan Tanggal Lahir tidak sesuai.";
		}
		
		if (REJECT_CODE_PRVD005.equals(rejectCode)) {
			return "Verifikasi user gagal. Foto Diri tidak sesuai.";
		}
		
        context.getLogger().warn("Unhandled Privy reject code: " + rejectCode);
		return "Verifikasi user gagal. " + response.getData().getReject().getReason();
    }

    @Override
    public AmMsuser insertInvitationRegisteredUser(UserBean userData, String privyId, MsLov lovUserType, MsTenant tenant, MsVendor vendor, Context context) {
        String hashedPhone = Tools.getHashedString(userData.getUserPhone());
        String hashedIdNo = Tools.getHashedString(userData.getIdNo());
        String emailService = (userData.getIdEmailHosting() != null && userData.getIdEmailHosting() != 0) ? "1" :"0";
        MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingByIdNewTrx(userData.getIdEmailHosting());
        AmMsrole role = getRoleFromUserType(lovUserType, tenant);

        AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(userData.getIdNo());
        if (null == user) {
            MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCodeNewTran(tenant.getTenantCode());
            String[] separatedName = userData.getName().split(" ");

            user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(userData.getEmail()));
			user.setFullName(StringUtils.upperCase(userData.getName()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider("DB");
			user.setPassword("newInv");
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("0");
			user.setUsrCrt(context.getRequestId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
        } else {
            user.setIsDormant("0");
            user.setUsrUpd(context.getRequestId());
            user.setDtmUpd(new Date());
            daoFactory.getUserDao().updateUserNewTran(user);
        }

        insertUnregisteredUseroftenant(user, tenant, context);

        MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserNewTran(user, vendor);
        if (null == vendorUser) {
            byte[] phoneBytea = personalDataEncryptionLogic.encryptFromString(userData.getUserPhone());
            Date activatedDate = new Date();
            String expiredTime = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_PRIVY_CERTIFICATE_EXPIRE_TIME);

            if (expiredTime == null) {
                throw new EsignhubJobException("Failed to get general setting Privy expired time ");
            }

            Date expiredDate = DateUtils.addDays(activatedDate, Integer.parseInt(expiredTime));

            vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive("0");
			vendorUser.setUsrCrt(context.getRequestId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(vendor);
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(privyId);
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
        }

        PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false, context);
        if (null == personalData || null == personalData.getUserPersonalData()) {
            Date dob = Tools.formatStringToDate(userData.getUserDob(), Constants.DATE_FORMAT);
            byte[] selfiePhoto = StringUtils.isBlank(userData.getSelfPhoto()) ? null : Tools.imageStringToByteArray(userData.getSelfPhoto());
            byte[] idPhoto = StringUtils.isBlank(userData.getIdPhoto()) ? null : Tools.imageStringToByteArray(userData.getIdPhoto());

            ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(userData.getProvinsi());
			zipcodeCityBean.setKota(userData.getKota());
			zipcodeCityBean.setKecamatan(userData.getKecamatan());
			zipcodeCityBean.setKelurahan(userData.getKelurahan());
			zipcodeCityBean.setZipcode(userData.getZipcode());

            PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(context.getRequestId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(userData.getUserGender()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(userData.getIdNo());
			personalDataBean.setPhoneRaw(userData.getUserPhone());
			personalDataBean.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));
            daoFactory.getUserDao().insertUserPersonalDataNewTrx(personalDataBean, context);
        }

        insertUnregisteredMemberofrole(user, role, context);

        return user;
    }

    private AmMsrole getRoleFromUserType(MsLov lovUserType, MsTenant tenant) {
        if (null != lovUserType && Constants.CODE_LOV_USER_TYPE_EMPLOYEE.equals(lovUserType.getCode())) {
			return daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(Constants.ROLE_BM_MF, tenant.getTenantCode());
		} else {
			return daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(Constants.ROLE_CUSTOMER, tenant.getTenantCode());
		}
    }

    private void insertUnregisteredMemberofrole(AmMsuser user, AmMsrole role, Context context) {
        AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
        if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(context.getRequestId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}
    }

    private void insertUnregisteredUseroftenant(AmMsuser user, MsTenant tenant, Context context) {
        MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
        if (null == useroftenant) {
            useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(context.getRequestId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
        }
    }
    
}
