package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_signing_process_audit_trail_detail")
public class TrSigningProcessAuditTrailDetail extends CreatableAndUpdatableEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private long idSigningProcessAuditTrailDetail;
    private TrSigningProcessAuditTrail signingProcessAuditTrail;
    private TrDocumentD trDocumentD;

    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_signing_process_audit_trail_detail", unique = true, nullable = false)
    public long getIdSigningProcessAuditTrailDetail() {
        return idSigningProcessAuditTrailDetail;
    }

    public void setIdSigningProcessAuditTrailDetail(long idSigningProcessAuditTrailDetail) {
        this.idSigningProcessAuditTrailDetail = idSigningProcessAuditTrailDetail;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_signing_process_audit_trail", nullable = true)
    public TrSigningProcessAuditTrail getSigningProcessAuditTrail() {
        return signingProcessAuditTrail;
    }

    public void setSigningProcessAuditTrail(TrSigningProcessAuditTrail signingProcessAuditTrail) {
        this.signingProcessAuditTrail = signingProcessAuditTrail;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_document_d", nullable = true)
    public TrDocumentD getTrDocumentD() {
        return trDocumentD;
    }

    public void setTrDocumentD(TrDocumentD trDocumentD) {
        this.trDocumentD = trDocumentD;
    }
}
