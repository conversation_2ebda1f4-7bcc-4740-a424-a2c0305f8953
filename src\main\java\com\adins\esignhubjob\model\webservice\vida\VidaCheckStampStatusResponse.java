package com.adins.esignhubjob.model.webservice.vida;

import com.adins.esignhubjob.model.custom.vida.VidaCheckStatusStampDataBean;
import com.google.gson.annotations.SerializedName;

public class VidaCheckStampStatusResponse {
    private int responseCode;
    private String message;
    private String refNum;
    @SerializedName("refund_status") private String refundStatus;
    private VidaCheckStatusStampDataBean data;
    
    public int getResponseCode() {
        return responseCode;
    }
    public void setResponseCode(int responseCode) {
        this.responseCode = responseCode;
    }
    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }
    public String getRefNum() {
        return refNum;
    }
    public void setRefNum(String refNum) {
        this.refNum = refNum;
    }
    public String getRefundStatus() {
        return refundStatus;
    }
    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }
    public VidaCheckStatusStampDataBean getData() {
        return data;
    }
    public void setData(VidaCheckStatusStampDataBean data) {
        this.data = data;
    }
    
}
