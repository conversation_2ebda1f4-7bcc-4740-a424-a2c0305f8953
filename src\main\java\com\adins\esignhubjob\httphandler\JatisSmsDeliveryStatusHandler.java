package com.adins.esignhubjob.httphandler;

import java.io.IOException;
import java.util.Date;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.BaseHttpHandler;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrMessageDeliveryReport;
import com.adins.exceptions.EsignhubHttpException;
import com.adins.exceptions.Status;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class JatisSmsDeliveryStatusHandler extends BaseHttpHandler {

    @Override
    public void doHandleRequest(HttpServletRequest request, HttpServletResponse response, Context context) throws IOException, ServletException {
        
        validateUserIdAndPassword(request, context);

        String messageId = request.getParameter("messageId");
        String deliverystatus = request.getParameter("deliverystatus");
        String dateReceived = request.getParameter("datereceived");
        context.getLogger().info(String.format("Processing message ID: %1$s, delivery status: %2$s, receive date: %3$s", messageId, deliverystatus, dateReceived));

        TrBalanceMutation balanceMutation = daoFactory.getBalanceMutationDao().getBalanceMutationByVendorTrxNo(messageId);
        MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);
        MsLov lovMessageMedia = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_MESSAGE_MEDIA, Constants.LOV_CODE_MESSAGE_MEDIA_SMS);
        Date receiveDate = Tools.formatStringToDate(dateReceived, "MM/d/yyyy h:mm:ss a");
        MsLov lovMessageGateway = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SMS_GATEWAY, Constants.CODE_LOV_SMS_GATEWAY_JATIS);
        String credentialTypeCode = determineCredentialType(balanceMutation.getMsTenant());
		MsLov credentialType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_CREDENTIAL_TYPE, credentialTypeCode);
        if (credentialType == null) {
            context.getLogger().error("Credential type not found for tenant: " + balanceMutation.getMsTenant().getTenantName());
            credentialType = null;
        }

         // Remove all non-numeric characters from notes to get the phone number
        String phoneNumber = balanceMutation.getNotes().replaceAll("[^\\d]", "");

        TrMessageDeliveryReport deliveryReport = daoFactory.getMessageDeliveryReportDao().getMessageDeliveryReportNewTrx(balanceMutation.getVendorTrxNo(), receiveDate);
        if (null != deliveryReport) {
            String deliveryLogDate = Tools.formatDateToStringIn(receiveDate, Constants.DATE_TIME_FORMAT_SEC);
            context.getLogger().info(String.format("Report for vendor trx no %1$s with delivery date %2$s existed (Skip inserting new report)", balanceMutation.getVendorTrxNo(), deliveryLogDate));
            
            Status responseStatus = new Status(0, "Success");
            response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            response.getWriter().write(gson.toJson(responseStatus));
            return;
        }

        deliveryReport = new TrMessageDeliveryReport();
        deliveryReport.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
        deliveryReport.setDtmCrt(new Date());
        deliveryReport.setMsVendor(vendor);
        deliveryReport.setMsTenant(balanceMutation.getMsTenant());
        deliveryReport.setReportTime(receiveDate);
        deliveryReport.setRequestTime(balanceMutation.getTrxDate());
        deliveryReport.setRecipientDetail(phoneNumber);
        deliveryReport.setTrxNo(balanceMutation.getTrxNo());
        deliveryReport.setVendorTrxNo(balanceMutation.getVendorTrxNo());
        deliveryReport.setMsLov(lovMessageMedia);
        deliveryReport.setDeliveryStatus(getFormattedDeliveryStatus(deliverystatus));
        deliveryReport.setMsLovMessageGateway(lovMessageGateway);
        deliveryReport.setMsLovCredentialType(credentialType);
        daoFactory.getMessageDeliveryReportDao().insertMessageDeliveryReportNewTrx(deliveryReport);
        
        Status responseStatus = new Status(0, "Success");
        response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        response.getWriter().write(gson.toJson(responseStatus));

    }

    private void validateUserIdAndPassword(HttpServletRequest request, Context context) {
        String userId = request.getParameter("userid");
        String password = request.getParameter("password");

        AmGeneralsetting idGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GS_JATIS_SMS_USERID);
        if (null == idGs || !idGs.getGsValue().equals(userId)) {
            context.getLogger().error("User ID mismatch! UserID from request: " + userId);
            throw new EsignhubHttpException("Credentials mismatch");
        }

        AmGeneralsetting passwordGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GS_JATIS_SMS_PASSWORD);
        if (null == passwordGs || !passwordGs.getGsValue().equals(password)) {
            context.getLogger().error("Password mismatch! Password from request: " + password);
            throw new EsignhubHttpException("Credentials mismatch");
        }
    }

    private String getFormattedDeliveryStatus(String originalDeliveryStatus) {
        
        /*
         * Jatis delivery status
         * 1 success
         * 2 undelivered
         * 3 destination unknown / invalid
         * 4 failed
         */
        
        /*
         * eSignHub delivery status
         * 1 sent
         * 2 failed
         * 3 delivered
         * 4 read
         */

        if ("1".equals(originalDeliveryStatus)) {
            return "3";
        }

        return "2";
    }

    private String determineCredentialType(MsTenant tenant) {
		MsTenantSettings usernameSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_JATIS_USERNAME");
		MsTenantSettings passwordSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_JATIS_PASSWORD");
		MsTenantSettings senderSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_JATIS_SENDER");

		if (usernameSettings != null && passwordSettings != null && senderSettings != null) {
			return Constants.LOV_CODE_CREDENTIAL_TYPE_TENANT;
		} else {
			return Constants.LOV_CODE_CREDENTIAL_TYPE_ESIGN;
		}
	}
    
}
