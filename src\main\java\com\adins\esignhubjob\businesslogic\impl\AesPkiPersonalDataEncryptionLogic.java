package com.adins.esignhubjob.businesslogic.impl;

import java.io.FileNotFoundException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;

import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.params.KeyParameter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.exceptions.EncryptionException;
import com.adins.util.AESKeyGenerator;
import com.adins.util.AESManager;
import com.adins.util.JksEncryptionUtils;

@Transactional
@Component
public class AesPkiPersonalDataEncryptionLogic implements PersonalDataEncryptionLogic, InitializingBean {

    private String keyStoreUrlString = System.getenv(Constants.ENV_VAR_KEYSTORE_URL);
	private String keyStorePassword = System.getenv(Constants.ENV_VAR_KEYSTORE_PASSWORD);
    private String entryAlias = System.getenv(Constants.ENV_VAR_KEYSTORE_KEYPAIR_ALIAS);
	private String entryPassword = System.getenv(Constants.ENV_VAR_KEYSTORE_KEYPAIR_PASSWORD);
	private KeyStore keyStore;

    @Override
    public void afterPropertiesSet() throws Exception {
        this.keyStore = this.loadKeyStore();
    }

    private KeyStore loadKeyStore() {
		try {
			URL keyStoreUrl = ResourceUtils.getURL(keyStoreUrlString);
			return JksEncryptionUtils.loadKeyStore(keyStoreUrl, keyStorePassword, JksEncryptionUtils.KEY_STORE_TYPE_JKS);
		}
		catch (FileNotFoundException e) {
			throw new EncryptionException("Cannot locate keyStore " + keyStoreUrlString, e);
		}		
	}

    @Override
	public byte[] encrypt(byte[] plainBytes) {		
		//1.Generate aes256 key
		AESKeyGenerator aesKeyGen = new AESKeyGenerator();
		byte[] seeds = aesKeyGen.generateNewSeeds();
		KeyParameter aesKey256 = aesKeyGen.generateNewAESKey(seeds);
		
		//2. Encrypt plain bytes
		AESManager aesMgr = new AESManager();
		try {
			byte[] encryptedData = aesMgr.encryptAES256(plainBytes, aesKey256);
			
			//3. Encrypt key with Public Key
			byte[] encryptedKey = JksEncryptionUtils.encryptWithKeyStore(this.keyStore, entryAlias, aesKey256.getKey());						
			
			//4. Append encryptedKey, encryptedData			
			byte[] result = new byte[encryptedKey.length + encryptedData.length];			
			System.arraycopy(encryptedKey, 0, result, 0, encryptedKey.length);
			System.arraycopy(encryptedData, 0, result, encryptedKey.length, encryptedData.length);
			
			return result;
		}
		catch (InvalidCipherTextException e) {
			throw new EncryptionException("Failed to encrypt", e);
		}
	}
	
	@Override
	public byte[] decrypt(byte[] cipheredBytes) {
		// 1. split EncryptedKey + EncryptedData
		final int keyLength = 256;
		byte[] encryptedKey = new byte[keyLength];
		int dataSize = cipheredBytes.length - keyLength;
		byte[] encryptedData = new byte[dataSize];
		
		System.arraycopy(cipheredBytes, 0, encryptedKey, 0, encryptedKey.length);
		System.arraycopy(cipheredBytes, keyLength, encryptedData, 0, encryptedData.length);
		
		// 2. decrypt key with Private Key
		byte[] aesKey256 = JksEncryptionUtils.decryptWithKeyStore(this.keyStore, this.entryAlias, this.entryPassword, encryptedKey);
		
		// 3. decrypt data with Decrypted Key
		AESManager aesmg = new AESManager();
		try {
			return aesmg.decryptAES256(encryptedData, aesKey256);			
		}
		catch (InvalidCipherTextException e) {
			throw new EncryptionException("Failed to decrypt", e);
		}
	}

	@Override
	public byte[] encryptFromString(String plainText) {
		return this.encrypt(plainText.getBytes(StandardCharsets.UTF_8));
	}

	@Override
	public String decryptToString(byte[] cipheredBytes) {
		return new String(this.decrypt(cipheredBytes), StandardCharsets.UTF_8);
	}
    
}
