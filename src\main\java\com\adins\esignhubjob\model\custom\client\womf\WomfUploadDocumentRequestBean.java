package com.adins.esignhubjob.model.custom.client.womf;

import com.google.gson.annotations.SerializedName;

public class WomfUploadDocumentRequestBean {
    @SerializedName("FileName") private String fileName;
    @SerializedName("DocTypeTc") private String docTypeTc;
    @SerializedName("Content") private String content;
    @SerializedName("DisplayName") private String displayName;

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDocTypeTc() {
        return this.docTypeTc;
    }

    public void setDocTypeTc(String docTypeTc) {
        this.docTypeTc = docTypeTc;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

}
