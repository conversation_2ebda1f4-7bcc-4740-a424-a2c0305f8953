package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.ClientCallbackRequestDao;
import com.adins.esignhubjob.model.table.TrClientCallbackRequest;
import com.adins.util.Tools;

@Component
@Transactional
public class ClientCallbackRequestDaoHbn extends BaseDaoHbn implements ClientCallbackRequestDao {

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrClientCallbackRequest getCallbackRequestNewTrx(Long idClientCallbackRequest) {
        Map<String, Object> params = new HashMap<>();
        params.put("idClientCallbackRequest", idClientCallbackRequest);

        return managerDAO.selectOne(
            "from TrClientCallbackRequest ccr "
            + "join fetch ccr.msTenant mt "
            + "where ccr.idClientCallbackRequest = :idClientCallbackRequest ", params);

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateCallbackRequestNewTrx(TrClientCallbackRequest callbackRequest) {
        callbackRequest.setUsrUpd(Tools.maskData(callbackRequest.getUsrUpd()));
        managerDAO.update(callbackRequest);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertClientCallbackRequestNewTrx(TrClientCallbackRequest request) {
        request.setUsrCrt(Tools.maskData(request.getUsrCrt()));
        managerDAO.insert(request);
    }
    
}
