package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_scheduler_job")
public class TrSchedulerJob extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	private long idSchedulerJob;
	private MsLov msLovByJobType;
	private MsLov msLovByBalanceType;
	private MsLov msLovBySchedulerType;
	private Date schedulerStart;
	private Date schedulerEnd;
	private Long dataProcessed;
	private String notes;
	private Short mailReminderCount;
	private MsTenant msTenant;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_scheduler_job", unique = true, nullable = false)
	public long getIdSchedulerJob() {
		return this.idSchedulerJob;
	}

	public void setIdSchedulerJob(long idSchedulerJob) {
		this.idSchedulerJob = idSchedulerJob;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_job_type")
	public MsLov getMsLovByJobType() {
		return this.msLovByJobType;
	}

	public void setMsLovByJobType(MsLov msLovByJobType) {
		this.msLovByJobType = msLovByJobType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_balance_type")
	public MsLov getMsLovByBalanceType() {
		return this.msLovByBalanceType;
	}

	public void setMsLovByBalanceType(MsLov msLovByBalanceType) {
		this.msLovByBalanceType = msLovByBalanceType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_scheduler_type")
	public MsLov getMsLovBySchedulerType() {
		return this.msLovBySchedulerType;
	}

	public void setMsLovBySchedulerType(MsLov msLovBySchedulerType) {
		this.msLovBySchedulerType = msLovBySchedulerType;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "scheduler_start", length = 29)
	public Date getSchedulerStart() {
		return this.schedulerStart;
	}

	public void setSchedulerStart(Date schedulerStart) {
		this.schedulerStart = schedulerStart;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "scheduler_end", length = 29)
	public Date getSchedulerEnd() {
		return this.schedulerEnd;
	}

	public void setSchedulerEnd(Date schedulerEnd) {
		this.schedulerEnd = schedulerEnd;
	}

	@Column(name = "data_processed")
	public Long getDataProcessed() {
		return this.dataProcessed;
	}

	public void setDataProcessed(Long dataProcessed) {
		this.dataProcessed = dataProcessed;
	}

	@Column(name = "notes", length = 200)
	public String getNotes() {
		return this.notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}
	
	@Column(name = "mail_reminder_count")
	public Short getMailReminderCount() {
		return (null == this.mailReminderCount)? 0 : this.mailReminderCount;
	}

	public void setMailReminderCount(Short mailReminderCount) {
		this.mailReminderCount = (null == mailReminderCount)? 0 : mailReminderCount;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
}
