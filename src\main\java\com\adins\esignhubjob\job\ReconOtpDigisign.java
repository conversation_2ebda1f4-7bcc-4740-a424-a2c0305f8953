package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esignhubjob.model.custom.JobResultParamBean;
import com.adins.esignhubjob.model.custom.digisign.DigisignTransactionBean;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsBalancevendoroftenant;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrJobResult;
import com.adins.esignhubjob.model.webservice.digisign.DigisignReconcileRequest;
import com.adins.esignhubjob.model.webservice.digisign.DigisignReconcileResponse;
import com.adins.exceptions.ReconException;
import com.adins.util.IOUtils;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class ReconOtpDigisign extends BaseJobHandler {

    private static final String USR_UPD = "FC Job";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String input = IOUtils.toString(inputStream);
        Long idJobResult = Long.valueOf(input);

        TrJobResult jobResult = daoFactory.getJobResultDao().getJobResultByIdJobResultNewTran(idJobResult);
        if (null == jobResult) {
            context.getLogger().warn("Job result with id " + idJobResult + " not found");
            return;
        }

        try {
            // Set process start time
            jobResult.setProcessStartTime(new Date());
            jobResult.setUsrUpd(USR_UPD);
            jobResult.setDtmUpd(new Date());
            daoFactory.getJobResultDao().updateJobResultNewTran(jobResult);
            validateJobResult(jobResult);

            // Set status to in progress
            jobResult.setProcessStatus(Constants.JOB_RESULT_IN_PROGRESS);
            jobResult.setUsrUpd(USR_UPD);
            jobResult.setDtmUpd(new Date());
            daoFactory.getJobResultDao().updateJobResultNewTran(jobResult);

            doReconcileOtpDigisign(jobResult, context);

            // Set finish time
            jobResult.setProcessFinishTime(new Date());
            jobResult.setProcessStatus(Constants.JOB_RESULT_COMPLETED);
            jobResult.setUsrUpd(USR_UPD);
            jobResult.setDtmUpd(new Date());
            daoFactory.getJobResultDao().updateJobResultNewTran(jobResult);

        } catch (Exception e) {

            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);

            jobResult.setErrorMessage(e.getLocalizedMessage());
            jobResult.setUsrUpd(USR_UPD);
            jobResult.setDtmUpd(new Date());
            jobResult.setProcessStatus(Constants.JOB_RESULT_FAILED);
            jobResult.setProcessFinishTime(new Date());
            daoFactory.getJobResultDao().updateJobResultNewTran(jobResult);

        }
    }

    private void validateJobResult(TrJobResult jobResult) {
        if (!Constants.JOB_RESULT_NEW.equals(jobResult.getProcessStatus())) {
            throw new ReconException("Cannot process request (Request already processed)");
        }
        JobResultParamBean param = gson.fromJson(jobResult.getRequestParams(), JobResultParamBean.class);
        if (!Constants.VENDOR_CODE_DIGISIGN.equals(param.getVendorCode())) {
            throw new ReconException("Cannot process vendor: " + param.getVendorCode());
        }
        if (!Constants.BALANCE_TYPE_CODE_OTP.equals(param.getBalanceType())) {
            throw new ReconException("Cannot process balance type: " + param.getBalanceType());
        }
    }

    private void doReconcileOtpDigisign(TrJobResult jobResult, Context context) {
        this.logger.info("Reading param: " + jobResult.getRequestParams());
        JobResultParamBean param = gson.fromJson(jobResult.getRequestParams(), JobResultParamBean.class);

        if (StringUtils.isNotBlank(param.getTenantCode())) {
            MsBalancevendoroftenant balancevendoroftenant = daoFactory.getBalancevendoroftenantDao().getBalancevendoroftenant(param.getTenantCode(), param.getVendorCode(), param.getBalanceType());
            if (null == balancevendoroftenant || !"1".equals(balancevendoroftenant.getIsActive())) {
                throw new ReconException("Tenant " + param.getTenantCode() + " does not have access to " + param.getVendorCode() + "'s " + param.getBalanceType() + " balance");
            }
            doReconcilePerTenant(balancevendoroftenant, param, jobResult.getAmMsuser(), context);
            return;
        }
        
        List<MsBalancevendoroftenant> balancevendoroftenants = daoFactory.getBalancevendoroftenantDao().getListBalancevendoroftenant(param.getVendorCode(), param.getBalanceType());
        for (MsBalancevendoroftenant balancevendoroftenant : balancevendoroftenants) {
            doReconcilePerTenant(balancevendoroftenant, param, jobResult.getAmMsuser(), context);
        }
    }

    private void doReconcilePerTenant(MsBalancevendoroftenant balancevendoroftenant, JobResultParamBean param, AmMsuser requester, Context context) {
        
        MsVendor vendor = balancevendoroftenant.getMsVendor();
        MsTenant tenant = balancevendoroftenant.getMsTenant();

        MsVendoroftenant vendoroftenant = daoFactory.getVendoroftenantDao().getVendoroftenant(tenant.getTenantCode(), vendor.getVendorCode());
        if (null == vendoroftenant) {
            throw new ReconException("Vendor " + vendor.getVendorName() + " is not mapped for tenant " + tenant.getTenantName());
        }
        if (StringUtils.isBlank(vendoroftenant.getToken())) {
            throw new ReconException("Token for tenant " + tenant.getTenantName() + " and vendor " + vendor.getVendorName() + " is empty");
        }

        MsLov lovBalanceType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_OTP);
        MsLov lovTrxType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_UOTP);

        String startTime = Tools.convertDateString(param.getTransactionDateStart(), "yyyy-MM-dd", "dd-MM-yyyy") + " 00:00:00";
        String endTime = Tools.convertDateString(param.getTransactionDateEnd(), "yyyy-MM-dd", "dd-MM-yyyy") + " 23:59:59";
        String token = vendoroftenant.getToken();

        DigisignReconcileRequest request = new DigisignReconcileRequest();
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setTransactionType(Constants.DIGISIGN_TRX_TYPE_SMS);

        // Get Digisign transaction list
        DigisignLogic digisignLogic = appContext.getBean(DigisignLogic.class);
        DigisignReconcileResponse response = digisignLogic.getReconcileData(request, token, context);
        if (!Constants.DIGISIGN_SUCCESS_CODE.equals(response.getResult())) {
            throw new ReconException(response.getInformation());
        }
        
        Date start = Tools.formatStringToDate(startTime, "dd-MM-yyyy HH:mm:ss");
        Date end = Tools.formatStringToDate(endTime, "dd-MM-yyyy HH:mm:ss");
        daoFactory.getBalanceMutationDao().deleteBalanceMutationNewTran(tenant, vendor, lovBalanceType, lovTrxType, start, end);
        context.getLogger().info("Balance mutation deleted for tenant " + tenant.getTenantName() + ", vendor " + vendor.getVendorName() + ", balance " + lovBalanceType.getCode() + ", " + startTime + " until " + endTime);
        
        for (DigisignTransactionBean transactionBean : response.getTransactions()) {
            if (!"sms".equals(transactionBean.getType()) || transactionBean.getAmount() == 0) {
                continue;
            }

            String phone = StringUtils.replace(transactionBean.getPhoneNumber(), "+62", "0");
            AmMsuser user = daoFactory.getUserDao().getUserByPhone(phone);

            String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
            String dateFormat = StringUtils.isBlank(System.getenv(Constants.ENV_VAR_RECON_DATE_FORMAT)) ? "yyyy-MM-dd HH:mm:ss" : System.getenv(Constants.ENV_VAR_RECON_DATE_FORMAT);
            Date trxDate = Tools.formatStringToDate(transactionBean.getTransactionTime(), dateFormat);
            String usrCrt = StringUtils.left(requester.getLoginId(), 36);
            
            if ("1".equals(System.getenv(Constants.ENV_VAR_LOG_RECON_INSERT))) {
                context.getLogger().info("Digisign transaction date: " + transactionBean.getTransactionTime());
                context.getLogger().info("Insert balmut for phone " + phone);
            }

            TrBalanceMutation balanceMutation = new TrBalanceMutation();
            balanceMutation.setTrxNo(trxNo);
            balanceMutation.setTrxDate(trxDate);
            balanceMutation.setRefNo(null);
            balanceMutation.setQty(-transactionBean.getAmount());
            balanceMutation.setMsLovByLovBalanceType(lovBalanceType);
            balanceMutation.setMsLovByLovTrxType(lovTrxType);
            balanceMutation.setUsrCrt(usrCrt);
            balanceMutation.setDtmCrt(new Date());
            balanceMutation.setMsTenant(tenant);
            balanceMutation.setMsVendor(vendor);
            balanceMutation.setAmMsuser(user);
            balanceMutation.setNotes(phone);
            balanceMutation.setVendorTrxNo(transactionBean.getTransactionId());
            daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(balanceMutation);
        }

        // Recalculate tr_balance_daily_recap
        String recalcResponse = daoFactory.getCommonDao().recalculateBalanceMutation(tenant.getTenantCode(), vendor.getVendorCode(), lovBalanceType.getCode(), start, end);
        context.getLogger().info("Recalculate tr_balance_daily_recap output: " + recalcResponse);
    }
    
}
