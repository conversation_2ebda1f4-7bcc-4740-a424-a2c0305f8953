package com.adins.constants;

public abstract class Constants {

	private Constants() {
	}

	// Manager DAO Constants
	public static final String MAP_RESULT_LIST = "resultList";

	public static final String ENV_VAR_DATA_PROCESSED_LIMIT = "DATA_PROCESSED_LIMIT";

	// Character pools to generate random string
	public static final String CHRS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	public static final String LOWER_CHRS = "abcdefghijklmnopqrstuvwxyz";
	public static final String UPPER_CHRS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	public static final String NUMBER = "0123456789";

	// Common eSignHub environment variable
	public static final String ENV_VAR_REGIS_INV_BASE_URL 	= "REGIS_INV_BASE_URL";
	public static final String ENV_VAR_ESIGNHUB_LOGIN_URL 	= "ESIGNHUB_LOGIN_URL";
	public static final String ENV_VAR_MAX_RETRY_STAMP_VIDA = "MAX_RETRY_STAMP_VIDA";

	// SMS VFirst environment variable
	public static final String ENV_VAR_VFIRST_USERNAME = "VFIRST_USERNAME";
	public static final String ENV_VAR_VFIRST_PASSWORD = "VFIRST_PASSWORD";
	public static final String ENV_VAR_VFIRST_SENDER = "VFIRST_SENDER";
	public static final String ENV_VAR_VFIRST_VERSION = "VFIRST_VERSION";
	public static final String ENV_VAR_VFIRST_TAG = "VFIRST_TAG";
	public static final String ENV_VAR_VFIRST_SEND_SMS_URL = "VFIRST_SEND_SMS_URL";
	public static final String ENV_VAR_VFIRST_GENERATE_TOKEN_URL = "VFIRST_GENERATE_TOKEN_URL";

	// SMS Jatis environment variables
	public static final String ENV_VAR_JATIS_SMS_ID = "JATIS_SMS_ID";
	public static final String ENV_VAR_JATIS_SMS_PASSWORD = "JATIS_SMS_PASSWORD";
	public static final String ENV_VAR_JATIS_SMS_SENDER = "JATIS_SMS_SENDER";
	public static final String ENV_VAR_JATIS_SMS_DIVISION = "JATIS_SMS_DIVISION";
	public static final String ENV_VAR_JATIS_SMS_URL = "JATIS_SMS_URL";

	// WhatsApp Jatis environment variables
	public static final String ENV_VAR_JATIS_WA_ACCOUNT_ID = "JATIS_WA_ACCOUNT_ID";
	public static final String ENV_VAR_JATIS_WA_TOKEN = "JATIS_WA_TOKEN";
	public static final String ENV_VAR_JATIS_WA_BASE_URL = "JATIS_WA_BASE_URL";
	public static final String ENV_VAR_JATIS_WA_TEMPLATE_SUFFIX = "JATIS_WA_TEMPLATE_SUFFIX";

	// WhatsApp Halosis environment variables
	public static final String ENV_VAR_HALOSIS_EMAIL = "HALOSIS_EMAIL";
	public static final String ENV_VAR_HALOSIS_PASSWORD = "HALOSIS_PASSWORD";
	public static final String ENV_VAR_HALOSIS_BASE_URL = "HALOSIS_BASE_URL";
	public static final String ENV_VAR_HALOSIS_LOGIN_URL = "HALOSIS_LOGIN_URL";
	public static final String ENV_VAR_HALOSIS_ACCESS_TOKEN_URL = "HALOSIS_ACCESS_TOKEN_URL";
	public static final String ENV_VAR_HALOSIS_SEND_WA_URL = "HALOSIS_SEND_WA_URL";

	// Email sender environment variable constants
	public static final String ENV_VAR_EMAIL_HOST = "EMAIL_HOST";
	public static final String ENV_VAR_EMAIL_PORT = "EMAIL_PORT";
	public static final String ENV_VAR_EMAIL_USERNAME = "EMAIL_USERNAME";
	public static final String ENV_VAR_EMAIL_PASSWORD = "EMAIL_PASSWORD";
	public static final String ENV_VAR_EMAIL_TIMEOUT = "EMAIL_TIMEOUT";

	public static final String ENV_VAR_DIGI_RECON_URL = "DIGI_RECON_URL";
	public static final String ENV_VAR_LOG_RECON_INSERT = "LOG_RECON_INSERT";
	public static final String ENV_VAR_RECON_DATE_FORMAT = "RECON_DATE_FORMAT";

	// Sign VIDA environment variable constants
	public static final String ENV_VAR_VIDA_GET_TOKEN_URI = "VIDA_GET_TOKEN_URI";
	public static final String ENV_VAR_VIDA_SIGN_URI = "VIDA_SIGN_URI";
	public static final String ENV_VAR_VIDA_DOC_STATUS_URI = "VIDA_DOC_STATUS_URI";
	public static final String ENV_VAR_VIDA_SIGN_CONN_TIMEOUT = "VIDA_SIGN_CONN_TIMEOUT";
	public static final String ENV_VAR_VIDA_SIGN_READ_TIMEOUT = "VIDA_SIGN_READ_TIMEOUT";
	public static final String ENV_VAR_VIDA_GET_MATERAI_TOKEN_URI = "VIDA_GET_MATERAI_TOKEN_URI";
	public static final String ENV_VAR_VIDA_GET_CLIENT_ID = "CLIENT_ID";
	public static final String ENV_VAR_VIDA_GET_CLIENT_TOKEN = "CLIENT_TOKEN";
	public static final String ENV_VAR_VIDA_GET_PERURI_DOCUMENT_TYPE_URL = "PERURI_DOCUMENT_TYPE_URL";
	public static final String ENV_VAR_VIDA_GET_PARTNER_ID = "PARTNER_ID";
	public static final String ENV_VAR_VIDA_LOG_ON_URL = "VIDA_LOG_ON_URL";
	public static final String ENV_VAR_VIDA_GET_CLIENT_SECRET = "CLIENT_SECRET";
	public static final String ENV_VAR_VIDA_UPLOAD_STAMP_URL = "VIDA_UPLOAD_STAMP_URL";
	public static final String ENV_VAR_VIDA_STAMP_CONN_TIMEOUT = "VIDA_STAMP_CONN_TIMEOUT";
	public static final String ENV_VAR_VIDA_STAMP_READ_TIMEOUT = "VIDA_STAMP_READ_TIMEOUT";
	public static final String ENV_VAR_VIDA_CHECK_STAMP_STATUS_URL = "VIDA_CHECK_STAMP_STATUS_URL";

	// Stamping PRIVY environment variables
	public static final String ENV_VAR_PRIVY_STAMP_ITERATION = "PRIVY_STAMP_ITERATION";
	public static final String ENV_VAR_PRIVY_STAMP_DELAY = "PRIVY_STAMP_DELAY";

	// Personal data encryption environment variable constants
	public static final String ENV_VAR_KEYSTORE_URL = "KEYSTORE_URL";
	public static final String ENV_VAR_KEYSTORE_PASSWORD = "KEYSTORE_PASSWORD";
	public static final String ENV_VAR_KEYSTORE_KEYPAIR_ALIAS = "KEYSTORE_KEYPAIR_ALIAS";
	public static final String ENV_VAR_KEYSTORE_KEYPAIR_PASSWORD = "KEYSTORE_KEYPAIR_PASSWORD";

	// Alibaba Cloud properties variable constants
	public static final String ENV_VAR_ALICLOUD_REGION = "ALICLOUD_REGION";
	public static final String ENV_VAR_ALICLOUD_UID = "ALICLOUD_UID";
	public static final String ENV_VAR_ALICLOUD_SERVICE_NAME = "ALICLOUD_SERVICE_NAME";
	public static final String ENV_OSS_ACCESSKEYID = "ENV_OSS_ACCESSKEYID";
	public static final String ENV_OSS_ACCESSKEYSECRET = "ENV_OSS_ACCESSKEYSECRET";
	public static final String ENV_OSS_BUCKET = "ENV_OSS_BUCKET";
	public static final String ENV_OSS_ARCHIVE_LIMIT_DATA = "ARCHIVE_LIMIT_DATA";

	public static final String ENV_VAR_EMETERAI_PAJAKKU_USER = "EMETERAI_PAJAKKU_USER";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_PASSWORD = "EMETERAI_PAJAKKU_PASSWORD";
	public static final String ENV_VAR_EMETERAI_DEFAULT_DOCNAME = "EMETERAI_DEFAULT_DOCNAME";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_LOGIN = "EMETERAI_PAJAKKU_LOGIN";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_GEN_SDT = "EMETERAI_PAJAKKU_GEN_SDT";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_STM_SDT_URL = "EMETERAI_PAJAKKU_STM_SDT_URL";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_CERT_LVL = "EMETERAI_PAJAKKU_CERT_LVL";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_PROFILE_NAME = "EMETERAI_PAJAKKU_PROFILE_NAME";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_REASON = "EMETERAI_PAJAKKU_REASON";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_REASON2 = "EMETERAI_PAJAKKU_REASON2";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_DEST = "EMETERAI_PAJAKKU_ON_PREM_DEST";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_SPECIMEN = "EMETERAI_PAJAKKU_ON_PREM_SPECIMEN";
	public static final String ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_STAMP_SOURCE = "EMETERAI_PAJAKKU_ON_PREM_STAMP_SOURCE";
	public static final String ENV_VAR_EMETERAI_DOCUMENT_TYPE_URL = "EMETERAI_DOCUMENT_TYPE_URL";
	public static final String ENV_VAR_UPL_DOC_METHOD = "UPL_DOC_METHOD";

	// Privy Core properties variable constants
	public static final String ENV_VAR_PRIVY_MERCHANT_KEY = "PRIVY_MERCHANT_KEY";
	public static final String ENV_VAR_PRIVY_SIGN_URL = "PRIVY_SIGN_URL";
	public static final String ENV_VAR_PRIVY_CHECK_DOC_STATUS_URL = "PRIVY_CHECK_DOC_STATUS_URL";
	public static final String ENV_VAR_PRIVY_CHECK_REG_STATUS_URL = "PRIVY_CHECK_REG_STATUS_URL";
	public static final String ENV_VAR_PRIVY_CHECK_REG_STATUS_URL_V2 = "PRIVY_CHECK_REG_STATUS_URL_V2";
	public static final String ENV_VAR_PRIVY_CHECK_REG_STATUS_ITERATION = "PRIVY_CHECK_REG_STATUS_ITERATION";
	public static final String ENV_VAR_PRIVY_CHECK_REG_STATUS_DELAY_MS = "PRIVY_CHECK_REG_STATUS_DELAY_MS";

	// Privy General properties variable constants
	public static final String ENV_VAR_PRIVY_GENERAL_CHANNEL_ID = "PRIVY_GENERAL_CHANNEL_ID";
	public static final String ENV_VAR_PRIVY_GENERAL_GET_TOKEN_URL = "PRIVY_GENERAL_GET_TOKEN_URL";
	public static final String ENV_VAR_PRIVY_GENERAL_CHECK_REGISTER_URL = "PRIVY_GENERAL_CHECK_REGISTER_URL";
	public static final String ENV_VAR_PRIVY_GENERAL_DOC_HISTORY_URL = "PRIVY_GENERAL_DOC_HISTORY_URL";
	public static final String ENV_VAR_PRIVY_GENERAL_DOC_STATUS_URL = "PRIVY_GENERAL_DOC_STATUS_URL";
	public static final String ENV_VAR_PRIVY_GENERAL_DOC_OWNER_ID = "PRIVY_GENERAL_DOC_OWNER_ID";
	public static final String ENV_VAR_PRIVY_GENERAL_DOC_OWNER_TOKEN = "PRIVY_GENERAL_DOC_OWNER_TOKEN";
	public static final String ENV_VAR_PRIVY_GENERAL_UPLOAD_DOC_URL = "PRIVY_GENERAL_UPLOAD_DOC_URL";
	public static final String ENV_VAR_PRIVY_GENERAL_DOC_CATEGORY = "RIVY_GENERAL_DOC_CATEGORY";

	public static final String ENV_VAR_ADINS_LIVENESS_URL = "ADINS_LIVENESS_URL";

	// Flag to skip sending SMS / WA
	public static final String ENV_VAR_SKIP_SEND_SMS = "SKIP_SEND_SMS";
	public static final String ENV_VAR_SKIP_SEND_WA = "SKIP_SEND_WA";

	// Hit Function in Database environment variables constants
	public static final String ENV_VAR_FUNCTION_NAME = "FUNCTION_NAME";
	public static final String ENV_VAR_FUNCTION_PARAMETER = "FUNCTION_PARAMETER";

	// Vendor codes
	public static final String VENDOR_CODE_ADINS = "ESG";
	public static final String VENDOR_CODE_DIGISIGN = "DIGI";
	public static final String VENDOR_CODE_VIDA = "VIDA";
	public static final String VENDOR_CODE_PRIVY = "PRIVY";

	// Tenant codes
	public static final String TENANT_CODE_WOMF = "WOMF";
	public static final String TENANT_CODE_CFI = "CFI";

	// LOV groups
	public static final String LOV_GROUP_BALANCE_TYPE = "BALANCE_TYPE";
	public static final String LOV_GROUP_TRX_TYPE = "TRX_TYPE";
	public static final String LOV_GROUP_JOB_TYPE = "JOB_TYPE";
	public static final String LOV_GROUP_SCHEDULER_TYPE = "SCHEDULER_TYPE";
	public static final String LOV_GROUP_SIGN_STATUS = "LOV_SIGN_STATUS";
	public static final String LOV_GROUP_PROCESS_RESULT = "PROCESS_RESULT";
	public static final String LOV_GROUP_CALLBACK_TYPE = "CALLBACK_TYPE";
	public static final String LOV_GROUP_MESSAGE_MEDIA = "MESSAGE_MEDIA";
	public static final String LOV_GROUP_TENANT_SETTING_TYPE = "TENANT_SETTING_TYPE";
	public static final String LOV_GROUP_WA_GATEWAY = "WA_GATEWAY";
	public static final String LOV_GROUP_STAMP_DUTY_STATUS = "STAMP_DUTY_STATUS";
	public static final String LOV_GROUP_NOTIF_SENDING_POINT = "NOTIF_SENDING_POINT";
	public static final String LOV_GROUP_SIGNING_PROCESS_TYPE = "SIGNING_PROCESS_TYPE";
	public static final String LOV_GROUP_SMS_GATEWAY = "SMS_GATEWAY";
	public static final String LOV_GROUP_CREDENTIAL_TYPE = "CREDENTIAL_TYPE";

	public static final String LOV_CODE_STAMP_DUTY_STATUS_GO_LIVE 	= "GO LIVE";
	public static final String LOV_CODE_STAMP_DUTY_STATUS_AVAILABLE	= "AVAILABLE";
	public static final String LOV_CODE_STAMP_DUTY_STATUS_USED		= "USED";
	public static final String LOV_CODE_STAMP_DUTY_STATUS_FAILED	= "STAMP_FAILED";

	public static final String LOV_CODE_MESSAGE_MEDIA_WA = "WA";
	public static final String LOV_CODE_MESSAGE_MEDIA_SMS = "SMS";

	public static final String LOV_CODE_SMS_GATEWAY_VFIRST = "VFIRST";

	public static final String LOV_CODE_CALLBACK_TYPE_SIGN_COMPLETE = "SIGNING_COMPLETE";
	public static final String LOV_CODE_CALLBACK_TYPE_ACTIVATION_COMPLETE = "ACTIVATION_COMPLETE";
	public static final String LOV_CODE_CALLBACK_TYPE_VERIFICATION_FAILED = "VERIFICATION_FAILED";
	public static final String LOV_CODE_CALLBACK_TYPE_DOCUMENT_SIGN_COMPLETE = "DOCUMENT_SIGN_COMPLETE";
	public static final String LOV_CODE_CALLBACK_TYPE_ALL_DOCUMENT_SIGN_COMPLETE = "ALL_DOCUMENT_SIGN_COMPLETE";

	public static final String LOV_CODE_TENANT_SETTING_ACTIVATION_CALLBACK = "ACTIVATION_CALLBACK";
	public static final String LOV_CODE_TENANT_SETTING_VERIFICATION_FAILED_CALLBACK = "VERIFICATION_FAILED_CALLBACK";
	public static final String LOV_CODE_TENANT_SETTING_SIGNER_COMPLETE_CALLBACK = "SIGNER_COMPLETE_CALLBACK";
	public static final String LOV_CODE_TENANT_SETTING_DOCUMENT_COMPLETE_CALLBACK = "DOCUMENT_COMPLETE_CALLBACK";
	public static final String LOV_CODE_TENANT_SETTING_ALL_DOCUMENT_COMPLETE_CALLBACK = "ALL_DOCUMENT_COMPLETE_CALLBACK";
	public static final String LOV_CODE_TENANT_SETTING_VFIRST_TOKEN_VALUE = "VFIRST_TOKEN_VALUE";
	public static final String LOV_CODE_TENANT_SETTING_VFIRST_TOKEN_EXPIRED_DATE = "VFIRST_TOKEN_EXPIRED_DATE";
	public static final String LOV_CODE_TENANT_SETTING_SMS_JATIS_ID = "SMS_JATIS_ID";
	public static final String LOV_CODE_TENANT_SETTING_SMS_JATIS_PASSWORD = "SMS_JATIS_PASSWORD";
	public static final String LOV_CODE_TENANT_SETTING_SMS_JATIS_SENDER = "SMS_JATIS_SENDER";
	public static final String LOV_CODE_TENANT_SETTING_SMS_JATIS_DIVISION = "SMS_JATIS_DIVISION";
	public static final String LOV_CODE_TENANT_SETTING_SEND_NOTIF_DOC_COMPLETE_SIGN = "SEND_NOTIF_DOC_COMPLETE_SIGN";
	public static final String LOV_CODE_TENANT_SETTING_SEND_PRIVY_VERIF_RESULT_NOTIF = "SEND_PRIVY_VERIF_RESULT_NOTIF";
	public static final String LOV_CODE_TENANT_SETTING_HALOSIS_TOKEN_VALUE = "HALOSIS_TOKEN_VALUE";
	public static final String LOV_CODE_TENANT_SETTING_HALOSIS_TOKEN_EXPIRY_DATE = "HALOSIS_TOKEN_EXPIRY_DATE";
	public static final String LOV_CODE_TENANT_SETTING_DOCUMENT_NOT_COMPLETE_STAMP_FILE_DURATION = "DOCUMENT_NOT_COMPLETE_STAMP_FILE_DURATION";
	public static final String LOV_CODE_TENANT_SETTING_DOCUMENT_NOT_COMPLETE_SIGN_FILE_DURATION = "DOCUMENT_NOT_COMPLETE_SIGN_FILE_DURATION";
	public static final String LOV_CODE_TENANT_SETTING_DOCUMENT_COMPLETED_SIGN_FILE_DURATION = "DOCUMENT_COMPLETED_SIGN_FILE_DURATION";
	public static final String LOV_CODE_TENANT_SETTING_DOCUMENT_COMPLETED_STAMP_FILE_DURATION = "DOCUMENT_COMPLETED_STAMP_FILE_DURATION";
	public static final String LOV_CODE_TENANT_SETTING_DOCUMENT_RETENTION_DURATION = "DOCUMENT_RETENTION_DURATION";

	public static final String LOV_CODE_DELETE_STAMP_RESULT = "DELETE_STAMP_RESULT_OSS";

	public static final String LOV_CODE_SCHEDULER_MONTHLY = "MONTHLY";
	public static final String LOV_CODE_SCHEDULER_DAILY = "DAILY";
	public static final String LOV_CODE_SCHEDULER_WEEKLY = "WEEKLY";

	public static final String LOV_CODE_DEL_RESULTSTAMP_ONPREM = "DEL_RESULTSTAMP_ONPREM";
	public static final String LOV_CODE_SIGN_STATUS_COMPLETE = "CP";

	public static final String LOV_CODE_DEL_SIGNEDDOC_SIGNING_OSS = "DEL_SIGNEDDOC_SIGNING_OSS";
	public static final String LOV_CODE_DEL_BASEDOC_SIGNING_OSS = "DEL_BASEDOC_SIGNING_OSS";

	public static final String LOV_CODE_PROCESS_RESULT_SUCCESS = "SUCCESS";
	public static final String LOV_CODE_PROCESS_RESULT_FAILED = "FAILED";

	public static final String LOV_CODE_CREDENTIAL_TYPE_TENANT = "TENANT";
	public static final String LOV_CODE_CREDENTIAL_TYPE_ESIGN = "ESIGN";

	public static final String BALANCE_TYPE_CODE_OTP = "OTP";
	public static final String TRX_TYPE_CODE_UOTP = "UOTP";
	public static final String TRX_TYPE_CODE_TOTP = "TOTP";

	public static final String BALANCE_TYPE_CODE_SGN = "SGN";
	public static final String TRX_TYPE_CODE_USGN = "USGN";
	public static final String TRX_TYPE_CODE_TSGN = "TSGN";

	public static final String BALANCE_TYPE_CODE_SDT = "SDT";
	public static final String TRX_TYPE_CODE_USDT = "USDT";
	public static final String TRX_TYPE_CODE_TSDT = "TSDT";

	public static final String BALANCE_TYPE_CODE_SDT_POSTPAID = "SDT_POSTPAID";
	public static final String TRX_TYPE_CODE_USDT_POSTPAID = "USDT_POSTPAID";
	public static final String TRX_TYPE_CODE_TSDT_POSTPAID = "TSDT_POSTPAID";

	public static final String BALANCE_TYPE_CODE_WA = "WA";
	public static final String TRX_TYPE_UWA = "UWA";
	public static final String TRX_TYPE_TWA = "TWA";

	public static final String BALANCE_TYPE_CODE_SMS = "SMS";
	public static final String TRX_TYPE_CODE_USMS = "USMS";
	public static final String TRX_TYPE_CODE_TSMS = "TSMS";

	public static final String CODE_LOV_USER_TYPE_CUST = "CUST";
	public static final String CODE_LOV_USER_TYPE_EMPLOYEE = "EMPLOYEE";

	public static final String DIGISIGN_SUCCESS_CODE = "00";
	public static final String DIGISIGN_FAIL_CODE = "01";

	public static final String DIGISIGN_TRX_TYPE_SMS = "SMS";

	// tr_job_result.process_result value
	public static final Short JOB_RESULT_NEW = (short) 0;
	public static final Short JOB_RESULT_IN_PROGRESS = (short) 1;
	public static final Short JOB_RESULT_COMPLETED = (short) 2;
	public static final Short JOB_RESULT_CANCELLED = (short) 3;
	public static final Short JOB_RESULT_FAILED = (short) 4;
	public static final Short JOB_RESULT_DELETED = (short) 5;

	// Path File OSS Document Related
	public static final String OSS_PREFIX_ENCRYPTED = "encrypted_";
	public static final String DELETE_STAMPED_DOCUMENT_RESULT = "stamping_result/%1$s/%2$s"; // Year / Month
	public static final String BASE_SIGN_DOCUMENT_FORMAT = "signing_document/%1$s/%2$s/%3$s.pdf";
	public static final String SIGNED_DOCUMENT_FORMAT = "sign_complete/%1$s/%2$s/%3$s.pdf";
	public static final String DOCUMENT_STAMPINGDOC_FORMAT = "stamping_eMaterai/%1$s/%2$s/%3$s.pdf"; // tenantCode, refNumber, documentId
	public static final String MANUAL_STAMP_FORMAT = "stamping_ematerai/%1$s/%2$s/%3$s.pdf"; // year, month, documentId
	public static final String STAMPED_DOCUMENT_FORMAT = "stamping_result/%1$s/%2$s/%3$s.pdf"; // year, month, documentId

	// Path File OSS Others
	public static final String AUTOSIGN_IMPORT_FORMAT = "import_bm/%1$s/%2$s/%3$s-%4$s-%5$s"; // year, month, tenant_code, id_process_autosign_bm_h, file_name
	public static final String USER_SELFIE_FORMAT = "selfie_user/%1$s/%2$s/%3$s.jpeg";
	public static final String REGISTER_REQUEST_FORMAT = "register_request/%1$s/%2$s/%3$s.txt";
	public static final String PERSONAL_SELFIE_FILENAME_FORMAT = "pd/%1$s/%1$s_selfie.jpg"; // nik,nik
	public static final String PERSONAL_KTP_FILENAME_FORMAT = "pd/%1$s/%1$s_ktp"; // nik,nik
	public static final String AUDIT_TRAIL_API_LOG_FORMAT		= "audit_log/%1$s/%2$s.zip";

	// Date Format
	public static final String DATE_FORMAT = "yyyy-MM-dd";
	public static final String DATE_TIME_FORMAT_SEC = "yyyy-MM-dd HH:mm:ss";
	public static final String DATE_TIME_FORMAT_MIL_SEC = "yyyy-MM-dd HH:mm:ss.SSS";
	public static final String DATE_TIME_FORMAT_SEQ = "yyyyMMddHHmmsss";

	public static final String SOD_TIME_MILL_SEC = " 00:00:00.000"; // start of day
	public static final String EOD_TIME_MILL_SEC = " 23:59:59.999"; // end of day

	// Role code
	public static final String ROLE_CUSTOMER = "CUST";
	public static final String ROLE_BM_MF = "MF";

	// Base 64 prefix
	public static final String IMG_PNG_PREFIX = "data:image/png;base64,";
	public static final String IMG_JPEG_PREFIX = "data:image/jpeg;base64,";
	public static final String IMG_JPG_PREFIX = "data:image/jpg;base64,";
	public static final String PDF_PREFIX = "data:application/pdf;base64,";

	// Document sdt_process value
	public static final String STEP_STAMPING_NOT_STR = "NOT_STR";
	public static final String STEP_STAMPING_GEN_SDT = "GEN_SDT";
	public static final String STEP_STAMPING_UPL_DOC = "UPL_DOC";
	public static final String STEP_STAMPING_STM_SDT = "STM_SDT";
	public static final String STEP_STAMPING_UPL_OSS = "UPL_OSS";
	public static final String STEP_STAMPING_UPL_CON = "UPL_CON";
	public static final String STEP_STAMPING_SDT_FIN = "SDT_FIN";

	// Scheduler Type LOV
	public static final String CODE_LOV_SCHED_TYPE_DAILY = "DAILY";
	public static final String CODE_LOV_SCHED_TYPE_MONTHLY = "MONTHLY";
	public static final String CODE_LOV_SCHED_TYPE_NON_SCHED = "NON";

	// Job Type LOV
	public static final String CODE_LOV_JOB_TYPE_UNFINISHDOCUMENT = "UNFINISHDOCUMENT";
	public static final String CODE_LOV_JOB_TYPE_BALRECAP = "BALRECAP";
	public static final String CODE_LOV_JOB_TYPE_BALREM = "BALREM";
	public static final String CODE_LOV_JOB_TYPE_SYNCREGTEKEN = "SYNCREGTEKEN";
	public static final String CODE_LOV_JOB_TYPE_DELETE_EXPIRED_URL_FORWARDER = "DEL_EXPIRED_URL_FORWARDER";
	public static final String CODE_LOV_JOB_TYPE_UPDATE_DORMANT_INACTIVE_USER = "UPDATE_DORMANT_INACTIVE_USER";
	public static final String CODE_LOV_JOB_TYPE_PROCESS_IMPORT_AUTOSIGN_BM = "PROCESS_IMPORT_AUTOSIGN_BM";
	public static final String CODE_LOV_JOB_TYPE_PROCESS_SEND_EMAIL_REMINDER_RETRY_STAMPING = "SEND_EMAIL_REMINDER_RETRY_STAMPING";
	public static final String CODE_LOV_JOB_TYPE_UPDATE_EMATERAI_DOCUMENT_TYPE_VIDA = "UPDATE_EMATERAI_DOCUMENT_TYPE_VIDA";
	public static final String CODE_LOV_JOB_TYPE_DELETE_EXPIRED_FILE = "DELETE_EXPIRED_FILE";
	public static final String CODE_LOV_JOB_TYPE_DOCUMENT_HOUSEKEEPING = "DOCUMENT_HOUSEKEEPING";
	public static final String CODE_LOV_JOB_TYPE_DOCUMENT_ARCHIVE = "DOCUMENT_ARCHIVE";
	public static final String CODE_LOV_JOB_TYPE_CHECK_EXPIRED_BALANCE = "CHECK_EXPIRED_BALANCE";
	public static final String CODE_LOV_JOB_TYPE_CHECK_RESTORE_EXPIRED_DOCUMENT = "CHECK_RESTORE_EXPIRED_DOCUMENT";

	// SMS Gateway LOV
	public static final String CODE_LOV_SMS_GATEWAY_JATIS = "JATIS";
	public static final String CODE_LOV_SMS_GATEWAY_VFIRST = "VFIRST";

	// WA Gateway LOV
	public static final String CODE_LOV_WA_GATEWAY_JATIS = "WHATSAPP"; // WhatsApp JATIS
	public static final String CODE_LOV_WA_GATEWAY_HALOSIS = "WHATSAPP_HALOSIS";

	// Message template types
	public static final String TEMPLATE_TYPE_WHATSAPP = "WHATSAPP";
	public static final String TEMPLATE_TYPE_EMAIL = "EMAIL";
	public static final String TEMPLATE_TYPE_SMS = "SMS";

	// Message template codes
	public static final String TEMPLATE_INSUFFICIENT_BAL = "INSUFFICIENT_BAL";
	public static final String TEMPLATE_ATTACH_EMETERAI_ERROR = "ATTACH_SDT_ERROR";
	public static final String NEW_PERURI_DOC_TYPE = "NEW_PERURI_DOC_TYPE";
	public static final String TEMPLATE_SEND_EMAIL_REMINDER_FAIL_STAMPING = "SEND_EMAIL_REMAINDER_FAIL_RETRY_STAMPING";
	public static final String TEMPLATE_ERROR_DELETE_FILE = "ERROR_DELETE_FILE";
	public static final String TEMPLATE_SIGNER_SIGN_COMPLETE_SMS = "SIGNER_SIGN_COMPLETE_SMS";
	public static final String TEMPLATE_DOC_OWNER_SIGN_COMPLETE_SMS = "DOC_OWNER_SIGN_COMPLETE_SMS";
	public static final String TEMPLATE_SIGNER_SIGN_COMPLETE_EMAIL = "SIGNER_SIGN_COMPLETE_EMAIL";
	public static final String TEMPLATE_DOC_OWNER_SIGN_COMPLETE_EMAIL = "DOC_OWNER_SIGN_COMPLETE_EMAIL";
	public static final String TEMPLATE_SIGNER_SIGN_COMPLETE_WA = "signer_sign_complete_wa";
	public static final String TEMPLATE_DOC_OWNER_SIGN_COMPLETE_WA = "doc_owner_sign_complete_wa";
	public static final String TEMPLATE_PRIVY_VERIF_SUCCESS_INV_EMAIL = "PRIVY_VERIF_SUCCESS_INV_EMAIL";
	public static final String TEMPLATE_PRIVY_VERIF_SUCCESS_EXT_EMAIL = "PRIVY_VERIF_SUCCESS_EXT_EMAIL";
	public static final String TEMPLATE_PRIVY_VERIF_SUCCESS_INV_SMS = "PRIVY_VERIF_SUCCESS_INV_SMS";
	public static final String TEMPLATE_PRIVY_VERIF_SUCCESS_EXT_SMS = "PRIVY_VERIF_SUCCESS_EXT_SMS";
	public static final String TEMPLATE_PRIVY_VERIF_SUCCESS_INV_WA = "privy_verif_success_inv_wa";
	public static final String TEMPLATE_PRIVY_VERIF_SUCCESS_EXT_WA = "privy_verif_success_ext_wa";
	public static final String TEMPLATE_PRIVY_VERIF_FAILED_INV_EMAIL = "PRIVY_VERIF_FAILED_INV_EMAIL";
	public static final String TEMPLATE_PRIVY_VERIF_FAILED_EXT_EMAIL = "PRIVY_VERIF_FAILED_EXT_EMAIL";
	public static final String TEMPLATE_PRIVY_VERIF_FAILED_INV_SMS = "PRIVY_VERIF_FAILED_INV_SMS";
	public static final String TEMPLATE_PRIVY_VERIF_FAILED_EXT_SMS = "PRIVY_VERIF_FAILED_EXT_SMS";
	public static final String TEMPLATE_PRIVY_VERIF_FAILED_INV_WA = "privy_verif_failed_inv_wa";
	public static final String TEMPLATE_PRIVY_VERIF_FAILED_EXT_WA = "privy_verif_failed_ext_wa";
	public static final String TEMPLATE_SEND_EMAIL_REMINDER_EXPIRED_BALANCE = "EMAIL_LIST_EXPIRING_BALANCE";
	public static final String TEMPLATE_SEND_EMAIL_FAIL = "SEND_EMAIL_FAIL";
	public static final String TEMPLATE_SEND_EMAIL_EXPIRED_BALANCE = "EMAIL_LIST_EXPIRED_BALANCE";
	public static final String TEMPLATE_SEND_VERIFY_DOCUMENT_KOMDIGI = "EMAIL_VERIF_KOMDIGI";

	public static final String TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD = "<td style=\"border: 2px solid #000; padding: 0; text-align: left; padding: 8px;\">";
	public static final String TEMPLATE_HTML_TAG_START_TR = "<tr>";

	public static final String TEMPLATE_HTML_TAG_END_TD = "</td>";
	public static final String TEMPLATE_HTML_TAG_END_TR = "</tr>";

	public static final String NOTIF_TYPE_EMAIL = "EMAIL";
	public static final String NOTIF_TYPE_SMS = "SMS";
	public static final String NOTIF_TYPE_WA = "WA";

	public static final String SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS = "CHECK_REGIS_STATUS";
	public static final String SIGNING_PROCESS_TYPE_REGISTRATION = "REGISTRATION";
	public static final String SIGNING_PROCESS_TYPE_SIGNING_SUCCESS = "SIGNING_SUCCESS";
	public static final String SIGNING_PROCESS_TYPE_SIGNING_REQUESTED = "SIGNING_REQUESTED";

	// Audit trail log subfolder names
	public static final String AUDIT_TRAIL_SUBFOLDER_REGISTER = "registration_data";
	public static final String AUDIT_TRAIL_SUBFOLDER_SIGNING_PROCESS = "signing_process";

	// Notiication sending points
	public static final String NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB = "SIGN_COMPLETE_NOTIF_JOB";
	public static final String NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER = "SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER";

	// Signing process type
	public static final String SIGNING_PROCESS_TYPE_DOC_SIGN_COMPLETE_NOTIF = "DOC_SIGN_COMPLETE_NOTIF";


	// Pajakku
	public static final String PERURI_SUCCESS_CODE	= "00";
	public static final String PERURI_ERROR_CODE	= "01";
	public static final String PERURI_STAMPING_SUCCESS_STATUS	= "True";
	public static final String PAJAKKU_NILAI_METERAI_LUNAS		= "10000";
}
