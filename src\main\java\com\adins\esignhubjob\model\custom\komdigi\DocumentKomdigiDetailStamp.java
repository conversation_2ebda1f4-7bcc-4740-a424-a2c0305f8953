package com.adins.esignhubjob.model.custom.komdigi;

import com.google.gson.annotations.SerializedName;

public class DocumentKomdigiDetailStamp {
    @SerializedName("chainLevel") private String chainLevel;
    @SerializedName("identity") private String identity;
    @SerializedName("issuer") private String issuer;
    @SerializedName("location") private String location;
    @SerializedName("ltv") private String ltv;
    @SerializedName("reason") private String reason;
    @SerializedName("serialNumber") private String serialNumber;
    @SerializedName("signature") private String signature;
    @SerializedName("stampingTime") private String stampingTime;
    @SerializedName("trusted") private String trusted;


    public String getChainLevel() {
        return this.chainLevel;
    }

    public void setChainLevel(String chainLevel) {
        this.chainLevel = chainLevel;
    }

    public String getIdentity() {
        return this.identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getIssuer() {
        return this.issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLtv() {
        return this.ltv;
    }

    public void setLtv(String ltv) {
        this.ltv = ltv;
    }

    public String getReason() {
        return this.reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getSerialNumber() {
        return this.serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getSignature() {
        return this.signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getStampingTime() {
        return this.stampingTime;
    }

    public void setStampingTime(String stampingTime) {
        this.stampingTime = stampingTime;
    }

    public String getTrusted() {
        return this.trusted;
    }

    public void setTrusted(String trusted) {
        this.trusted = trusted;
    }
    
}
