package com.adins.esignhubjob.model.webservice.privygeneral;

import java.util.List;

import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralUploadDocumentBlockReasonBean;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralUploadDocumentErrorBean;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralUploadDocumentRecepientResponseBean;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralUploadDocumentResponseDataBean;
import com.google.gson.annotations.SerializedName;

public class PrivyGeneralUploadDocumentResponse {
    
    private String message;
	private PrivyGeneralUploadDocumentResponseDataBean data;
    @SerializedName("reference_number") private String referenceNumber;
	@SerializedName("channel_id") private String channelId;
	private String info;
	private String status;
	@SerializedName("signed_document") private String signedDocument;
	@SerializedName("unsigned_document") private String unsignedDocument;
	@SerializedName("block_reason") private PrivyGeneralUploadDocumentBlockReasonBean blockReason;
	private List<PrivyGeneralUploadDocumentRecepientResponseBean> recepients;
	private PrivyGeneralUploadDocumentErrorBean error;

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public PrivyGeneralUploadDocumentResponseDataBean getData() {
        return this.data;
    }

    public void setData(PrivyGeneralUploadDocumentResponseDataBean data) {
        this.data = data;
    }

    public String getReferenceNumber() {
        return this.referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getChannelId() {
        return this.channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getInfo() {
        return this.info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSignedDocument() {
        return this.signedDocument;
    }

    public void setSignedDocument(String signedDocument) {
        this.signedDocument = signedDocument;
    }

    public String getUnsignedDocument() {
        return this.unsignedDocument;
    }

    public void setUnsignedDocument(String unsignedDocument) {
        this.unsignedDocument = unsignedDocument;
    }

    public PrivyGeneralUploadDocumentBlockReasonBean getBlockReason() {
        return this.blockReason;
    }

    public void setBlockReason(PrivyGeneralUploadDocumentBlockReasonBean blockReason) {
        this.blockReason = blockReason;
    }

    public List<PrivyGeneralUploadDocumentRecepientResponseBean> getRecepients() {
        return this.recepients;
    }

    public void setRecepients(List<PrivyGeneralUploadDocumentRecepientResponseBean> recepients) {
        this.recepients = recepients;
    }

    public PrivyGeneralUploadDocumentErrorBean getError() {
        return this.error;
    }

    public void setError(PrivyGeneralUploadDocumentErrorBean error) {
        this.error = error;
    }

}
