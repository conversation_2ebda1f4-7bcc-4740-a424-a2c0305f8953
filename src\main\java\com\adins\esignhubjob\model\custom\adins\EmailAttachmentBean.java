package com.adins.esignhubjob.model.custom.adins;

import java.io.Serializable;

public class EmailAttachmentBean implements Serializable {
	private static final long serialVersionUID = -6388097585214801788L;

    private byte[] binary;
	private String fileName;

	public EmailAttachmentBean() {}
	
	public EmailAttachmentBean(byte[] binary, String fileName) {
		super();
		this.binary = binary;
		this.fileName = fileName;
	}
	
	public byte[] getBinary() {
		return binary;
	}
	public void setBinary(byte[] binary) {
		this.binary = binary;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
}