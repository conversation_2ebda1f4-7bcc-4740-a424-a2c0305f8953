package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_job")
public class Ms<PERSON>ob extends ActivatableEntity implements Serializable{
    private static final long serialVersionUID = 1L;
	public static final String ID_JOB_HBM = "idMsJob";
	public static final String JOB_CODE_HBM = "jobCode";
	
	private long idMsJob;
	private String jobCode;
	private String jobType;
	private String jobName;
	private String resultUploadLocation;
	private String resultFileFormat;
	
	private Set<TrJobResult> trJobResults = new HashSet<>(0);
	
	public MsJob() {
	}

	public MsJob(long idMsJob, String jobCode, String jobType, String jobName, String resultUploadLocation, String resultFileFormat,
			String isActive, String usrCrt, String usrUpd, Date dtmCrt,	Date dtmUpd) {
		this.idMsJob = idMsJob;
		this.jobCode = jobType;
		this.jobType = jobCode;
		this.jobName = jobName;
		this.resultUploadLocation = resultUploadLocation;
		this.resultFileFormat = resultFileFormat;
		this.isActive = isActive;
		this.usrCrt = usrCrt;
		this.usrUpd = usrUpd;
		this.dtmCrt = dtmCrt;
		this.dtmUpd = dtmUpd;
		
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_job", unique = true, nullable = false)
	public long getIdMsJob() {
		return idMsJob;
	}

	public void setIdMsJob(long idMsJob) {
		this.idMsJob = idMsJob;
	}

	@Column(name = "job_code", nullable = false, length = 20)
	public String getJobCode() {
		return jobCode;
	}

	public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}

	@Column(name = "job_type", nullable = false, length = 100)
	public String getJobType() {
		return jobType;
	}

	public void setJobType(String jobType) {
		this.jobType = jobType;
	}

	@Column(name = "job_name", nullable = false, length = 100)
	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	@Column(name = "result_upload_location", length = 200)
	public String getResultUploadLocation() {
		return resultUploadLocation;
	}

	public void setResultUploadLocation(String resultUploadLocation) {
		this.resultUploadLocation = resultUploadLocation;
	}

	@Column(name = "result_file_format", length = 200)
	public String getResultFileFormat() {
		return resultFileFormat;
	}

	public void setResultFileFormat(String resultFileFormat) {
		this.resultFileFormat = resultFileFormat;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msJob")
	public Set<TrJobResult> getTrJobResults() {
		return trJobResults;
	}

	public void setTrJobResults(Set<TrJobResult> trJobResults) {
		this.trJobResults = trJobResults;
	}
}
