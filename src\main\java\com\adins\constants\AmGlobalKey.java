package com.adins.constants;

public class AmGlo<PERSON><PERSON>ey {
    protected AmGlobalKey() {
        throw new IllegalStateException(
                "AmGlobalKey class shall not be instantiated! Class=" + this.getClass().getName());
    }

    public static final String GS_AES_KEY = "AES_KEY";
    public static final String GS_VIDA_JOB_SIGN_WAIT_TIME = "VIDA_JOB_SIGN_WAIT_TIME";
    public static final String GS_VIDA_JOB_SIGN_ITERATION = "VIDA_JOB_SIGN_ITERATION";
    public static final String GS_REMINDER_TOPUP_FREQ = "REMINDER_TOPUP_FREQ";
    public static final String GS_ATTACH_SDT_MAX_REPEATED_ERROR_COUNT = "ATTACH_SDT_MAX_REPEATED_ERROR_COUNT";
    public static final String GS_ATTACH_SDT_ERROR_EMAIL_RECEIVER = "ATTACH_SDT_ERROR_EMAIL_RECEIVER";
    public static final String GS_TOKEN_CLIENT_URL_UPLOAD = "TOKEN_CLIENT_URL_UPLOAD";
    public static final String GS_DMS_USERNAME = "DMS_USERNAME";
    public static final String GS_DOCUMENT_MAX_RETRY_STAMPING = "DOCUMENT_MAX_RETRY_STAMPING";
    public static final String GS_SEND_EMAIL_REMAINDER_FAIL_RETRY_STAMPING_RECEPIENT = "SEND_EMAIL_REMAINDER_FAIL_RETRY_STAMPING_RECEPIENT";
    public static final String GS_SEND_EMAIL_NEW_PERURI_DOC_TYPE = "SEND_EMAIL_NEW_PERURI_DOC_TYPE";
    public static final String GS_SEND_EMAIL_PIC_OPERATION_ADINS = "EMAIL_PIC_ESIGN";
    public static final String GS_PRIVY_CERTIFICATE_EXPIRE_TIME = "PRIVY_CERTIFICATE_EXPIRE_TIME";
    public static final String GS_VIDA_CERTIFICATE_EXPIRE_TIME = "VIDA_CERTIFICATE_EXPIRE_TIME";
    public static final String GS_DIGISIGN_CERTIFICATE_EXPIRE_TIME = "DIGISIGN_CERTIFICATE_EXPIRE_TIME";
    public static final String GS_PERURI_CONN_TIMEOUT 	= "PERURI_CONN_TIMEOUT";
	public static final String GS_PERURI_READ_TIMEOUT 	= "PERURI_READ_TIMEOUT";
	public static final String GS_PAYMENT_RECEIPT_PERURI_CONN_TIMEOUT 	= "PAYMENT_RECEIPT_PERURI_CONN_TIMEOUT";
	public static final String GS_PAYMENT_RECEIPT_PERURI_READ_TIMEOUT 	= "PAYMENT_RECEIPT_PERURI_READ_TIMEOUT";
    public static final String GS_PAJAKKU_USERNAME	= "PERURI_ACCOUNT_NAME";
	public static final String GS_PAJAKKU_PASSWORD	= "PERURI_ACCOUNT_PASSWORD";
    public static final String GS_NILAI_METERAI_LUNAS 		= "NILAI_METERAI_LUNAS";
    public static final String GS_STAMPED_DOC_CHECK_ATTEMPTS = "STAMPED_DOC_CHECK_ATTEMPTS";
	public static final String GS_STAMPED_DOC_CHECK_DELAY = "STAMPED_DOC_CHECK_DELAY";
    public static final String GS_LIMIT_RETRY_CALLBACK_CLIENT = "LIMIT_RETRY_CALLBACK_CLIENT";
    public static final String GS_REMINDER_DURATION_BEFORE_BALANCE_EXPIRED = "REMINDER_DURATION_BEFORE_BALANCE_EXPIRED";

    // Jatis SMS general setting
    public static final String GS_JATIS_SMS_USERID = "JATIS_SMS_USERID";
    public static final String GS_JATIS_SMS_PASSWORD = "JATIS_SMS_PASSWORD";

    // Database query constants
    public static final String MAP_RESULT_LIST = "resultList";
    public static final String DB_PARAM_RANGE = "range";

}
