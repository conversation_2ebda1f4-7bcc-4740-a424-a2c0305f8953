package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_document_d_restore")
public class TrDocumentDRestore extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    private long idDocumentDRestore;
    private TrDocumentD trDocumentD;
    private MsLov lovProcessRestore;
    private Date restoreExpiredDate;
    private String isActive;

    @Id @GeneratedValue(strategy=GenerationType.IDENTITY)
    @Column(name = "id_document_d_restore", unique = true, nullable = false)
    public long getIdDocumentDRestore() {
        return this.idDocumentDRestore;
    }

    public void setIdDocumentDRestore(long idDocumentDRestore) {
        this.idDocumentDRestore = idDocumentDRestore;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_document_d")
    public TrDocumentD getTrDocumentD() {
        return this.trDocumentD;
    }

    public void setTrDocumentD(TrDocumentD trDocumentD) {
        this.trDocumentD = trDocumentD;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lov_process_restore")
    public MsLov getLovProcessRestore() {
        return this.lovProcessRestore;
    }

    public void setLovProcessRestore(MsLov lovProcessRestore) {
        this.lovProcessRestore = lovProcessRestore;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "restore_expired_date")
    public Date getRestoreExpiredDate() {
        return this.restoreExpiredDate;
    }

    public void setRestoreExpiredDate(Date restoreExpiredDate) {
        this.restoreExpiredDate = restoreExpiredDate;
    }

    @Column(name = "is_active", length = 1)
    public String getIsActive() {
        return this.isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }
}
