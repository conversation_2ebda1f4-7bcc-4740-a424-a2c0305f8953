package com.adins.esignhubjob.businesslogic.impl;

import java.net.HttpURLConnection;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.CallbackLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.model.custom.adins.TenantCallbackBean;
import com.adins.esignhubjob.model.custom.adins.TenantCallbackRequestBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrClientCallbackRequest;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;
import com.aliyuncs.fc.client.FunctionComputeClient;
import com.aliyuncs.fc.constants.Const;
import com.aliyuncs.fc.request.InvokeFunctionRequest;
import com.aliyuncs.fc.response.InvokeFunctionResponse;

@Component
public class GenericCallbackLogic extends BaseLogic implements CallbackLogic {

    @Autowired private PersonalDataEncryptionLogic encryptionLogic;

    @Override
    public void executeCallbackToClient(MsTenant tenant, MsLov lovCallbackType, MsVendorRegisteredUser vendorUser, TrDocumentD document, TrDocumentH documentH, String callbackMessage, Context context) {
        if (StringUtils.isBlank(tenant.getClientCallbackUrl())) {
			return;
		}

		if (!needToExecuteCallback(tenant, lovCallbackType)) {
			context.getLogger().info("Skipping callback for " + tenant.getTenantCode() + " with callback type " + lovCallbackType.getCode());
			return;
		}
        
        TenantCallbackBean data = prepareCallbackData(lovCallbackType, vendorUser, document);
		
		TenantCallbackRequestBean request = new TenantCallbackRequestBean();
		request.setCallbackType(lovCallbackType.getCode());
		request.setTimeStamp(Tools.formatDateToStringIn(new Date(), "yyyy-MM-dd HH:mm:ss"));
		request.setData(data);
		request.setMessage(callbackMessage);
		
		String jsonRequest = gson.toJson(request);
		
		TrClientCallbackRequest callbackRequest = new TrClientCallbackRequest();
		callbackRequest.setRequestStatus((short) 1);
		callbackRequest.setRequestDate(new Date());
		callbackRequest.setMsTenant(tenant);
		callbackRequest.setLovCallbackType(lovCallbackType);

		if (null != vendorUser) {
			callbackRequest.setAmMsuser(vendorUser.getAmMsuser());
		}
		
		if (null != document) {
			callbackRequest.setTrDocumentD(document);
		}
		
		callbackRequest.setCallbackRequest(jsonRequest);
		callbackRequest.setUsrCrt(context.getRequestId());
		callbackRequest.setDtmCrt(new Date());
		daoFactory.getClientCallbackRequestDao().insertClientCallbackRequestNewTrx(callbackRequest);
		
		// Trigger FC callback
        String region = System.getenv(Constants.ENV_VAR_ALICLOUD_REGION);
        String uid = System.getenv(Constants.ENV_VAR_ALICLOUD_UID);
        String accessKeyId = System.getenv(Constants.ENV_OSS_ACCESSKEYID);
        String accessKeySecret = System.getenv(Constants.ENV_OSS_ACCESSKEYSECRET);
        String serviceName = System.getenv(Constants.ENV_VAR_ALICLOUD_SERVICE_NAME);
        String functionName = "execute_client_callback";
        String payload = String.valueOf(callbackRequest.getIdClientCallbackRequest());

        InvokeFunctionRequest invReq = new InvokeFunctionRequest(serviceName, functionName);
        invReq.setInvocationType(Const.INVOCATION_TYPE_ASYNC);
        invReq.setPayload(payload.getBytes());

        FunctionComputeClient fcClient = new FunctionComputeClient(region, uid, accessKeyId, accessKeySecret);
        InvokeFunctionResponse invRes = fcClient.invokeFunction(invReq);
        logFunctionComputeInvocation(invRes, context);
    }

	@Override
	public void executeFailedVerificationCallbackToClient(MsTenant tenant, String email, String phone, String rejectMessage, Context context) {
		if (StringUtils.isBlank(tenant.getClientCallbackUrl())) {
			return;
		}

		MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_VERIFICATION_FAILED_CALLBACK);

		boolean needCallback = settings != null && "1".equals(settings.getSettingValue());
		if (!needCallback) {
			context.getLogger().info("Skipping callback for " + tenant.getTenantCode() + " with callback type VERIFICATION_FAILED");
			return;
		}

		TenantCallbackBean data = new TenantCallbackBean();
		data.setPhone(phone);
		if (StringUtils.isNotBlank(email)) {
			data.setEmail(email);
		}

		MsLov lovCallbackType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_CALLBACK_TYPE, Constants.LOV_CODE_CALLBACK_TYPE_VERIFICATION_FAILED);

		TenantCallbackRequestBean request = new TenantCallbackRequestBean();
		request.setCallbackType(lovCallbackType.getCode());
		request.setTimeStamp(Tools.formatDateToStringIn(new Date(), "yyyy-MM-dd HH:mm:ss"));
		request.setData(data);
		request.setMessage(rejectMessage);

		String jsonRequest = gson.toJson(request);

		TrClientCallbackRequest callbackRequest = new TrClientCallbackRequest();
		callbackRequest.setRequestStatus((short) 1);
		callbackRequest.setRequestDate(new Date());
		callbackRequest.setMsTenant(tenant);
		callbackRequest.setLovCallbackType(lovCallbackType);		
		callbackRequest.setCallbackRequest(jsonRequest);
		callbackRequest.setUsrCrt(context.getRequestId());
		callbackRequest.setDtmCrt(new Date());
		daoFactory.getClientCallbackRequestDao().insertClientCallbackRequestNewTrx(callbackRequest);

		// Trigger FC callback
        String region = System.getenv(Constants.ENV_VAR_ALICLOUD_REGION);
        String uid = System.getenv(Constants.ENV_VAR_ALICLOUD_UID);
        String accessKeyId = System.getenv(Constants.ENV_OSS_ACCESSKEYID);
        String accessKeySecret = System.getenv(Constants.ENV_OSS_ACCESSKEYSECRET);
        String serviceName = System.getenv(Constants.ENV_VAR_ALICLOUD_SERVICE_NAME);
        String functionName = "execute_client_callback";
        String payload = String.valueOf(callbackRequest.getIdClientCallbackRequest());

        InvokeFunctionRequest invReq = new InvokeFunctionRequest(serviceName, functionName);
        invReq.setInvocationType(Const.INVOCATION_TYPE_ASYNC);
        invReq.setPayload(payload.getBytes());

        FunctionComputeClient fcClient = new FunctionComputeClient(region, uid, accessKeyId, accessKeySecret);
        InvokeFunctionResponse invRes = fcClient.invokeFunction(invReq);
        logFunctionComputeInvocation(invRes, context);

	}

	private TenantCallbackBean prepareCallbackData(MsLov lovCallbackType, MsVendorRegisteredUser vendorUser, TrDocumentD document) {
		TenantCallbackBean data = new TenantCallbackBean();
		
		if (Constants.LOV_CODE_CALLBACK_TYPE_ACTIVATION_COMPLETE.equals(lovCallbackType.getCode())) {
			String phoneNumber = encryptionLogic.decryptToString(vendorUser.getPhoneBytea());
			data.setPhone(phoneNumber);
			data.setEmail(vendorUser.getSignerRegisteredEmail());
		}
		
		if (Constants.LOV_CODE_CALLBACK_TYPE_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			data.setEmail(vendorUser.getSignerRegisteredEmail());
			data.setDocumentId(document.getDocumentId());
		}
		
		if (Constants.LOV_CODE_CALLBACK_TYPE_DOCUMENT_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			data.setDocumentId(document.getDocumentId());
		}
		
		if (Constants.LOV_CODE_CALLBACK_TYPE_ALL_DOCUMENT_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			data.setRefNo(document.getTrDocumentH().getRefNumber());
		}
		
		return data;
	}

	private boolean needToExecuteCallback(MsTenant tenant, MsLov lovCallbackType) {

		if (Constants.LOV_CODE_CALLBACK_TYPE_VERIFICATION_FAILED.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_VERIFICATION_FAILED_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}

		if (Constants.LOV_CODE_CALLBACK_TYPE_ACTIVATION_COMPLETE.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_ACTIVATION_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}
		
		if (Constants.LOV_CODE_CALLBACK_TYPE_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_SIGNER_COMPLETE_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}
		
		if (Constants.LOV_CODE_CALLBACK_TYPE_DOCUMENT_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_COMPLETE_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}
		
		if (Constants.LOV_CODE_CALLBACK_TYPE_ALL_DOCUMENT_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_ALL_DOCUMENT_COMPLETE_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}
		
		return false;
	}

	@Override
	public void executeSendSignCompleteNotification(TrDocumentH documentH, Context context) {
		MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(documentH.getMsTenant(), Constants.LOV_CODE_TENANT_SETTING_SEND_NOTIF_DOC_COMPLETE_SIGN);
        boolean needToSendNotif = settings != null && "1".equals(settings.getSettingValue());

        if (!needToSendNotif) {
            return;
        }

        String region = System.getenv(Constants.ENV_VAR_ALICLOUD_REGION);
        String uid = System.getenv(Constants.ENV_VAR_ALICLOUD_UID);
        String accessKeyId = System.getenv(Constants.ENV_OSS_ACCESSKEYID);
        String accessKeySecret = System.getenv(Constants.ENV_OSS_ACCESSKEYSECRET);
        String serviceName = System.getenv(Constants.ENV_VAR_ALICLOUD_SERVICE_NAME);
        String functionName = "send_sign_complete_notif";
        String payload = String.valueOf(documentH.getIdDocumentH());

        InvokeFunctionRequest invReq = new InvokeFunctionRequest(serviceName, functionName);
        invReq.setInvocationType(Const.INVOCATION_TYPE_ASYNC);
        invReq.setPayload(payload.getBytes());

        FunctionComputeClient fcClient = new FunctionComputeClient(region, uid, accessKeyId, accessKeySecret);
        InvokeFunctionResponse invRes = fcClient.invokeFunction(invReq);
        if (HttpURLConnection.HTTP_ACCEPTED == invRes.getStatus()) {
			context.getLogger().info("Asynchronous invocation accepted, request ID: " + invRes.getRequestId());
		} else {
			context.getLogger().warn("Asynchronous invocation failed");
		}
	}

	private void logFunctionComputeInvocation(InvokeFunctionResponse invRes, Context context) {
		if (HttpURLConnection.HTTP_ACCEPTED == invRes.getStatus()) {
			context.getLogger().info("Asynchronous invocation accepted, request ID: " + invRes.getRequestId());
		} else {
			context.getLogger().warn("Asynchronous invocation failed");
		}
	}
    
}
