package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.ActivatableAndDeletableEntity;

@Entity
@Table(name = "am_msuser")
public class AmMsuser extends ActivatableAndDeletableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	public static final String ID_MS_USER_HBM = "idMsUser";
	public static final String LOGIN_ID_HBM = "loginId";
	public static final String FULLNAME_HBM = "fullName";
	public static final String HASHED_PHONE_HBM = "hashedPhone";
	public static final String OTP_CODE_HBM = "otpCode";
	public static final String HASHED_IDNO_HBM = "hashedIdNo";
	public static final String PASSWORD_HBM = "password";

	private long idMsUser;
	private MsOffice msOffice;
	private MsEmailHosting msEmailHosting;
	private String loginId;
	private String fullName;
	private String initialName;
	private String loginProvider;
	private String password;
	private Integer failCount;
	private String isLoggedIn;
	private String isLocked;
	private String isDormant;
	private Date lastLoggedIn;
	private Date lastLocked;
	private Date lastExpired;
	private Date lastDormant;
	private Date lastLoggedFail;
	private Date lastRequestOut;
	private Date prevLoggedIn;
	private Date prevLoggedFail;
	private String changePwdLogin;
	private String resetCode;
	private String otpCode;
	private String emailService;
	private Date resetCodeRequestDate;
	private Short resetCodeRequestNum;
	private String hashedPhone;
	private String hashedIdNo;
	private String vendorResetPassLink;
	private String dataChangeRequest;
	private String activationLink;
	private String reregistrationLink;
	private Date livenessFacecompareRequestDate;
	private Short livenessFacecompareRequestNum;
	private Date dormantDate;

	private Set<TrDocumentDSign> trDocumentDSigns = new HashSet<>(0);
	private Set<TrDocumentH> trDocumentHsForIdMsuserCustomer = new HashSet<>(0);
	private Set<AmUserpwdhistory> amUserpwdhistories = new HashSet<>(0);
	private Set<MsVendorRegisteredUser> msVendorRegisteredUsers = new HashSet<>(0);
	private Set<MsUseroftenant> msUseroftenants = new HashSet<>(0);
	private Set<TrFeedback> trFeedbacks = new HashSet<>(0);
	private Set<AmMemberofrole> amMemberofroles = new HashSet<>(0);
	private Set<AmAuditlog> amAuditlogs = new HashSet<>(0);
	private Set<AmUserPersonalData> amUserPersonalDatas = new HashSet<>(0);
	private Set<TrDocumentH> trDocumentHsForIdMsuserRequestBy = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);
	private Set<TrFaceVerify> trFaceVerifies = new HashSet<>(0);
	private Set<TrInvitationLink> trInvitationLinks = new HashSet<>(0);
	private Set<TrJobResult> trJobResults = new HashSet<>(0);
	private Set<TrDocumentSigningRequest> trDocumentSigningRequests = new HashSet<>(0);
	private Set<TrJobUpdatePsreId> trJobUpdatePsreIds = new HashSet<>(0);
	private Set<TrClientCallbackRequest> trClientCallbackRequests = new HashSet<>(0);

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_user", unique = true, nullable = false)
	public long getIdMsUser() {
		return this.idMsUser;
	}

	public void setIdMsUser(long idMsUser) {
		this.idMsUser = idMsUser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_office")
	public MsOffice getMsOffice() {
		return this.msOffice;
	}

	public void setMsOffice(MsOffice msOffice) {
		this.msOffice = msOffice;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_email_hosting")
	public MsEmailHosting getMsEmailHosting() {
		return msEmailHosting;
	}

	public void setMsEmailHosting(MsEmailHosting msEmailHosting) {
		this.msEmailHosting = msEmailHosting;
	}

	@Column(name = "login_id", length = 80)
	public String getLoginId() {
		return this.loginId;
	}

	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	@Column(name = "full_name", nullable = false, length = 80)
	public String getFullName() {
		return this.fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	@Column(name = "initial_name", length = 15)
	public String getInitialName() {
		return this.initialName;
	}

	public void setInitialName(String initialName) {
		this.initialName = initialName;
	}

	@Column(name = "login_provider", length = 6)
	public String getLoginProvider() {
		return this.loginProvider;
	}

	public void setLoginProvider(String loginProvider) {
		this.loginProvider = loginProvider;
	}

	@Column(name = "password", nullable = false, length = 200)
	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Column(name = "fail_count")
	public Integer getFailCount() {
		return this.failCount;
	}

	public void setFailCount(Integer failCount) {
		this.failCount = failCount;
	}

	@Column(name = "is_logged_in", length = 1)
	public String getIsLoggedIn() {
		return this.isLoggedIn;
	}

	public void setIsLoggedIn(String isLoggedIn) {
		this.isLoggedIn = isLoggedIn;
	}

	@Column(name = "is_locked", length = 1)
	public String getIsLocked() {
		return this.isLocked;
	}

	public void setIsLocked(String isLocked) {
		this.isLocked = isLocked;
	}

	@Column(name = "is_dormant", length = 1)
	public String getIsDormant() {
		return this.isDormant;
	}

	public void setIsDormant(String isDormant) {
		this.isDormant = isDormant;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "dormant_date", length = 29)
	public Date getDormantDate() {
		return dormantDate;
	}

	public void setDormantDate(Date dormantDate) {
		this.dormantDate = dormantDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_logged_in", length = 29)
	public Date getLastLoggedIn() {
		return this.lastLoggedIn;
	}

	public void setLastLoggedIn(Date lastLoggedIn) {
		this.lastLoggedIn = lastLoggedIn;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_locked", length = 29)
	public Date getLastLocked() {
		return this.lastLocked;
	}

	public void setLastLocked(Date lastLocked) {
		this.lastLocked = lastLocked;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_expired", length = 29)
	public Date getLastExpired() {
		return this.lastExpired;
	}

	public void setLastExpired(Date lastExpired) {
		this.lastExpired = lastExpired;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_dormant", length = 29)
	public Date getLastDormant() {
		return this.lastDormant;
	}

	public void setLastDormant(Date lastDormant) {
		this.lastDormant = lastDormant;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_logged_fail", length = 29)
	public Date getLastLoggedFail() {
		return this.lastLoggedFail;
	}

	public void setLastLoggedFail(Date lastLoggedFail) {
		this.lastLoggedFail = lastLoggedFail;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_request_out", length = 29)
	public Date getLastRequestOut() {
		return this.lastRequestOut;
	}

	public void setLastRequestOut(Date lastRequestOut) {
		this.lastRequestOut = lastRequestOut;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "prev_logged_in", length = 29)
	public Date getPrevLoggedIn() {
		return this.prevLoggedIn;
	}

	public void setPrevLoggedIn(Date prevLoggedIn) {
		this.prevLoggedIn = prevLoggedIn;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "prev_logged_fail", length = 29)
	public Date getPrevLoggedFail() {
		return this.prevLoggedFail;
	}

	public void setPrevLoggedFail(Date prevLoggedFail) {
		this.prevLoggedFail = prevLoggedFail;
	}

	@Column(name = "change_pwd_login", length = 1)
	public String getChangePwdLogin() {
		return this.changePwdLogin;
	}

	public void setChangePwdLogin(String changePwdLogin) {
		this.changePwdLogin = changePwdLogin;
	}

	@Column(name = "reset_code", length = 40)
	public String getResetCode() {
		return this.resetCode;
	}

	public void setResetCode(String resetCode) {
		this.resetCode = resetCode;
	}
	
	@Column(name = "otp_code", length = 10)
	public String getOtpCode() {
		return otpCode;
	}

	public void setOtpCode(String otpCode) {
		this.otpCode = otpCode;
	}


	@Column(name = "email_service", length = 20)
	public String getEmailService() {
		return this.emailService;
	}

	public void setEmailService(String emailService) {
		this.emailService = emailService;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "reset_code_request_date", length = 29)
	public Date getResetCodeRequestDate() {
		return this.resetCodeRequestDate;
	}

	public void setResetCodeRequestDate(Date resetCodeRequestDate) {
		this.resetCodeRequestDate = resetCodeRequestDate;
	}

	@Column(name = "reset_code_request_num")
	public Short getResetCodeRequestNum() {
		return this.resetCodeRequestNum;
	}

	public void setResetCodeRequestNum(Short resetCodeRequestNum) {
		this.resetCodeRequestNum = resetCodeRequestNum;
	}

	@Column(name = "hashed_phone", length = 200)
	public String getHashedPhone() {
		return this.hashedPhone;
	}

	public void setHashedPhone(String hashedPhone) {
		this.hashedPhone = hashedPhone;
	}

	@Column(name = "hashed_id_no", length = 200)
	public String getHashedIdNo() {
		return this.hashedIdNo;
	}

	public void setHashedIdNo(String hashedIdNo) {
		this.hashedIdNo = hashedIdNo;
	}

	@Column(name = "vendor_reset_pass_link", length = 200)
	public String getVendorResetPassLink() {
		return vendorResetPassLink;
	}

	public void setVendorResetPassLink(String vendorResetPassLink) {
		this.vendorResetPassLink = vendorResetPassLink;
	}
	
	@Column(name = "data_change_request", length = 20)
	public String getDataChangeRequest() {
		return this.dataChangeRequest;
	}

	public void setDataChangeRequest(String dataChangeRequest) {
		this.dataChangeRequest = dataChangeRequest;
	}
	
	@Column(name = "activation_link", length = 300)
	public String getActivationLink() {
		return activationLink;
	}

	public void setActivationLink(String activationLink) {
		this.activationLink = activationLink;
	}
	
	@Column(name = "reregistration_link", length = 300)
	public String getReregistrationLink() {
		return reregistrationLink;
	}

	public void setReregistrationLink(String reregistrationLink) {
		this.reregistrationLink = reregistrationLink;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "liveness_facecompare_request_date", length = 29)
	public Date getLivenessFacecompareRequestDate() {
		return livenessFacecompareRequestDate;
	}

	public void setLivenessFacecompareRequestDate(Date livenessFacecompareRequestDate) {
		this.livenessFacecompareRequestDate = livenessFacecompareRequestDate;
	}

	@Column(name = "liveness_facecompare_request_num")
	public Short getLivenessFacecompareRequestNum() {
		return livenessFacecompareRequestNum;
	}

	public void setLivenessFacecompareRequestNum(Short livenessFacecompareRequestNum) {
		this.livenessFacecompareRequestNum = livenessFacecompareRequestNum;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrDocumentDSign> getTrDocumentDSigns() {
		return this.trDocumentDSigns;
	}

	public void setTrDocumentDSigns(Set<TrDocumentDSign> trDocumentDSigns) {
		this.trDocumentDSigns = trDocumentDSigns;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuserByIdMsuserCustomer")
	public Set<TrDocumentH> getTrDocumentHsForIdMsuserCustomer() {
		return this.trDocumentHsForIdMsuserCustomer;
	}

	public void setTrDocumentHsForIdMsuserCustomer(Set<TrDocumentH> trDocumentHsForIdMsuserCustomer) {
		this.trDocumentHsForIdMsuserCustomer = trDocumentHsForIdMsuserCustomer;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmUserpwdhistory> getAmUserpwdhistories() {
		return this.amUserpwdhistories;
	}

	public void setAmUserpwdhistories(Set<AmUserpwdhistory> amUserpwdhistories) {
		this.amUserpwdhistories = amUserpwdhistories;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<MsVendorRegisteredUser> getMsVendorRegisteredUsers() {
		return this.msVendorRegisteredUsers;
	}

	public void setMsVendorRegisteredUsers(Set<MsVendorRegisteredUser> msVendorRegisteredUsers) {
		this.msVendorRegisteredUsers = msVendorRegisteredUsers;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<MsUseroftenant> getMsUseroftenants() {
		return this.msUseroftenants;
	}

	public void setMsUseroftenants(Set<MsUseroftenant> msUseroftenants) {
		this.msUseroftenants = msUseroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrFeedback> getTrFeedbacks() {
		return this.trFeedbacks;
	}

	public void setTrFeedbacks(Set<TrFeedback> trFeedbacks) {
		this.trFeedbacks = trFeedbacks;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmMemberofrole> getAmMemberofroles() {
		return this.amMemberofroles;
	}

	public void setAmMemberofroles(Set<AmMemberofrole> amMemberofroles) {
		this.amMemberofroles = amMemberofroles;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmAuditlog> getAmAuditlogs() {
		return this.amAuditlogs;
	}

	public void setAmAuditlogs(Set<AmAuditlog> amAuditlogs) {
		this.amAuditlogs = amAuditlogs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmUserPersonalData> getAmUserPersonalDatas() {
		return this.amUserPersonalDatas;
	}

	public void setAmUserPersonalDatas(Set<AmUserPersonalData> amUserPersonalDatas) {
		this.amUserPersonalDatas = amUserPersonalDatas;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuserByIdMsuserRequestBy")
	public Set<TrDocumentH> getTrDocumentHsForIdMsuserRequestBy() {
		return this.trDocumentHsForIdMsuserRequestBy;
	}

	public void setTrDocumentHsForIdMsuserRequestBy(Set<TrDocumentH> trDocumentHsForIdMsuserRequestBy) {
		this.trDocumentHsForIdMsuserRequestBy = trDocumentHsForIdMsuserRequestBy;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return this.trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrFaceVerify> getTrFaceVerifies() {
		return this.trFaceVerifies;
	}

	public void setTrFaceVerifies(Set<TrFaceVerify> trFaceVerifies) {
		this.trFaceVerifies = trFaceVerifies;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrInvitationLink> getTrInvitationLinks() {
		return this.trInvitationLinks;
	}

	public void setTrInvitationLinks(Set<TrInvitationLink> trInvitationLinks) {
		this.trInvitationLinks = trInvitationLinks;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrJobResult> getTrJobResults() {
		return trJobResults;
	}

	public void setTrJobResults(Set<TrJobResult> trJobResults) {
		this.trJobResults = trJobResults;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrDocumentSigningRequest> getTrDocumentSigningRequests() {
		return trDocumentSigningRequests;
	}

	public void setTrDocumentSigningRequests(Set<TrDocumentSigningRequest> trDocumentSigningRequests) {
		this.trDocumentSigningRequests = trDocumentSigningRequests;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrJobUpdatePsreId> getTrJobUpdatePsreIds() {
		return trJobUpdatePsreIds;
	}

	public void setTrJobUpdatePsreIds(Set<TrJobUpdatePsreId> trJobUpdatePsreIds) {
		this.trJobUpdatePsreIds = trJobUpdatePsreIds;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrClientCallbackRequest> getTrClientCallbackRequests() {
		return trClientCallbackRequests;
	}

	public void setTrClientCallbackRequests(Set<TrClientCallbackRequest> trClientCallbackRequests) {
		this.trClientCallbackRequests = trClientCallbackRequests;
	}
}
