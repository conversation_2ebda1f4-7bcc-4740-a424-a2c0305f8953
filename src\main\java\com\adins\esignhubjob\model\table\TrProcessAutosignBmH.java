package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_process_autosign_bm_h")
public class TrProcessAutosignBmH extends CreatableAndUpdatableEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    private long idProcessAutosignBmH;
    private Date trxDate;
    private String executeType;
    private MsTenant msTenant;
    private MsVendor msVendor;
    private String fileName;
    private String status;

    @Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_process_autosign_bm_h", unique = true, nullable = false)
    public long getIdProcessAutosignBmH() {
        return this.idProcessAutosignBmH;
    }

    public void setIdProcessAutosignBmH(long idProcessAutosignBmH) {
        this.idProcessAutosignBmH = idProcessAutosignBmH;
    }

    @Temporal(TemporalType.TIMESTAMP)
	@Column(name = "trx_date", length = 29)
    public Date getTrxDate() {
        return this.trxDate;
    }

    public void setTrxDate(Date trxDate) {
        this.trxDate = trxDate;
    }

    @Column(name = "execute_type", length = 36)
    public String getExecuteType() {
        return this.executeType;
    }

    public void setExecuteType(String executeType) {
        this.executeType = executeType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
    public MsTenant getMsTenant() {
        return this.msTenant;
    }

    public void setMsTenant(MsTenant msTenant) {
        this.msTenant = msTenant;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
    public MsVendor getMsVendor() {
        return this.msVendor;
    }

    public void setMsVendor(MsVendor msVendor) {
        this.msVendor = msVendor;
    }

    @Column(name = "file_name", length = 255)
    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Column(name = "status", length = 1)
    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    
}
