package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.AmGlobalKey;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.BalancevendoroftenantDao;
import com.adins.esignhubjob.model.table.MsBalancevendoroftenant;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.framework.persistence.dao.api.ManagerDAO;

@Component
@Transactional
public class BalancevendoroftenantDaoHbn extends BaseDaoHbn implements BalancevendoroftenantDao {

    @Override
    public MsBalancevendoroftenant getBalancevendoroftenant(String tenantCode, String vendorCode, String lovBalanceTypeCode) {
        Map<String, Object> params = new HashMap<>();
        params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
        params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
        params.put(MsLov.CODE_HBM, StringUtils.upperCase(lovBalanceTypeCode));

        return managerDAO.selectOne(
            "from MsBalancevendoroftenant bvot "
            + "join fetch bvot.msTenant mt "
            + "join fetch bvot.msVendor mv "
            + "join fetch bvot.msLov ml "
            + "where mt.tenantCode = :tenantCode "
            + "and mv.vendorCode = :vendorCode "
            + "and ml.code = :code ", params);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<MsBalancevendoroftenant> getListBalancevendoroftenant(String vendorCode, String lovBalanceTypeCode) {
        Map<String, Object> params = new HashMap<>();
        params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
        params.put(MsLov.CODE_HBM, StringUtils.upperCase(lovBalanceTypeCode));

        return (List<MsBalancevendoroftenant>) managerDAO.list(
            "from MsBalancevendoroftenant bvot "
            + "join fetch bvot.msTenant mt "
            + "join fetch bvot.msVendor mv "
            + "join fetch bvot.msLov ml "
            + "where mv.vendorCode = :vendorCode "
            + "and ml.code = :code ", params).get(ManagerDAO.MAP_RESULT_LIST);
    }

    @SuppressWarnings("unchecked")
	@Override
	public List<MsBalancevendoroftenant> getListMsBalancevendoroftenant() {
		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsBalancevendoroftenant bvot "
				+ "join fetch bvot.msVendor mv "
				+ "join fetch mv.msLovVendorType mlv "
				+ "join fetch bvot.msTenant mt "
				+ "join fetch bvot.msLov mlbvot ", 
				new Object[][] {});
		return (List<MsBalancevendoroftenant>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
	}
    
}
