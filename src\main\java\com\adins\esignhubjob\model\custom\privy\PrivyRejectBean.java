package com.adins.esignhubjob.model.custom.privy;

import java.util.List;

public class PrivyRejectBean {
    private String code;
    private String reason;
    private List<PrivyRejectHandlerBean> handler;

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getReason() {
        return this.reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<PrivyRejectHandlerBean> getHandler() {
        return this.handler;
    }

    public void setHandler(List<PrivyRejectHandlerBean> handler) {
        this.handler = handler;
    }


}
