package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_useroftenant")
public class MsUseroftenant extends CreatableAndUpdatableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	private long idMsUseroftenant;
	private AmMsuser amMsuser;
	private MsTenant msTenant;

	public MsUseroftenant() {
	}

	public MsUseroftenant(long idMsUseroftenant, AmMsuser amMsuser, MsTenant msTenant, String usrCrt, Date dtmCrt) {
		this.idMsUseroftenant = idMsUseroftenant;
		this.amMsuser = amMsuser;
		this.msTenant = msTenant;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}

	public MsUseroftenant(long idMsUseroftenant, AmMsuser amMsuser, MsTenant msTenant, String usrCrt, Date dtmCrt,
			String usrUpd, Date dtmUpd) {
		this.idMsUseroftenant = idMsUseroftenant;
		this.amMsuser = amMsuser;
		this.msTenant = msTenant;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_useroftenant", unique = true, nullable = false)
	public long getIdMsUseroftenant() {
		return this.idMsUseroftenant;
	}

	public void setIdMsUseroftenant(long idMsUseroftenant) {
		this.idMsUseroftenant = idMsUseroftenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

}