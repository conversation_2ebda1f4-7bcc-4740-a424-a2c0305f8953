package com.adins.esignhubjob.model.table.custom;

import javax.persistence.Column;
import javax.persistence.Embeddable;

@Embeddable
public class ZipcodeCityBean {
    private String provinsi;
	private String kota;
	private String kecamatan;
    private String kelurahan;
    private String zipcode;
	
    @Column(name = "provinsi", length = 50)
	public String getProvinsi() {
		return provinsi;
	}

	public void setProvinsi(String provinsi) {
		this.provinsi = provinsi;
	}
	
    @Column(name = "kelurahan", length = 50)
    public String getKelurahan() {
        return kelurahan;
    }

    public void setKelurahan(String kelurahan) {
        this.kelurahan = kelurahan;
    }

    @Column(name = "kecamatan", length = 50)
    public String getKecamatan() {
        return kecamatan;
    }

    public void setKecamatan(String kecamatan) {
        this.kecamatan = kecamatan;
    }

    @Column(name = "kota", length = 50)
	public String getKota() {
		return kota;
	}

	public void setKota(String kota) {
		this.kota = kota;
	}

    @Column(name = "zip_code", length = 10)
    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }
}
