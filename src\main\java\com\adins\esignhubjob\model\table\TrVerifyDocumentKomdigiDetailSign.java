package com.adins.esignhubjob.model.table;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;
@Entity
@Table(name = "tr_verify_document_komdigi_detail_sign")
public class TrVerifyDocumentKomdigiDetailSign extends CreatableAndUpdatableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	private long idVerifyDocumentKomdigiDetailSign;
	private TrVerifyDocumentKomdigi trVerifyDocumentKomdigi;
	private String signedBy;
	private String signReason;
	private Date signTimestamp;
	private String signStatus;
	private String signLocation;
	private String signCertifIdentity;
	private String signLtv;
	private String signTrusted;
	private String signIssuer;
	private String signChainLevel;
	public TrVerifyDocumentKomdigiDetailSign() {
	}
	public TrVerifyDocumentKomdigiDetailSign(long idVerifyDocumentKomdigiDetailSign, TrVerifyDocumentKomdigi trVerifyDocumentKomdigi, 
			String signedBy, String signReason, Date signTimestamp, String signStatus, String signLocation, 
			String signCertifIdentity, String signLtv, String signTrusted, String signIssuer, String signChainLevel, 
			String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd) {
		this.idVerifyDocumentKomdigiDetailSign = idVerifyDocumentKomdigiDetailSign;
		this.trVerifyDocumentKomdigi = trVerifyDocumentKomdigi;
		this.signedBy = signedBy;
		this.signReason = signReason;
		this.signTimestamp = signTimestamp;
		this.signStatus = signStatus;
		this.signLocation = signLocation;
		this.signCertifIdentity = signCertifIdentity;
		this.signLtv = signLtv;
		this.signTrusted = signTrusted;
		this.signIssuer = signIssuer;
		this.signChainLevel = signChainLevel;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_verify_document_komdigi_detail_sign", unique = true, nullable = false)
	public long getIdVerifyDocumentKomdigiDetailSign() {
		return this.idVerifyDocumentKomdigiDetailSign;
	}
	public void setIdVerifyDocumentKomdigiDetailSign(long idVerifyDocumentKomdigiDetailSign) {
		this.idVerifyDocumentKomdigiDetailSign = idVerifyDocumentKomdigiDetailSign;
	}
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_verify_document_komdigi")
	public TrVerifyDocumentKomdigi getTrVerifyDocumentKomdigi() {
		return this.trVerifyDocumentKomdigi;
	}
	public void setTrVerifyDocumentKomdigi(TrVerifyDocumentKomdigi trVerifyDocumentKomdigi) {
		this.trVerifyDocumentKomdigi = trVerifyDocumentKomdigi;
	}
	@Column(name = "signed_by", length = 80)
	public String getSignedBy() {
		return this.signedBy;
	}
	public void setSignedBy(String signedBy) {
		this.signedBy = signedBy;
	}
	@Column(name = "sign_reason", length = 100)
	public String getSignReason() {
		return this.signReason;
	}
	public void setSignReason(String signReason) {
		this.signReason = signReason;
	}
	@Column(name = "sign_timestamp", length = 29)
	public Date getSignTimestamp() {
		return this.signTimestamp;
	}
	public void setSignTimestamp(Date signTimestamp) {
		this.signTimestamp = signTimestamp;
	}
	@Column(name = "sign_status", length = 20)
	public String getSignStatus() {
		return this.signStatus;
	}
	public void setSignStatus(String signStatus) {
		this.signStatus = signStatus;
	}
	@Column(name = "sign_location")
	public String getSignLocation() {
		return this.signLocation;
	}
	public void setSignLocation(String signLocation) {
		this.signLocation = signLocation;
	}
	@Column(name = "sign_certif_identity", length = 1)
	public String getSignCertifIdentity() {
		return this.signCertifIdentity;
	}
	public void setSignCertifIdentity(String signCertifIdentity) {
		this.signCertifIdentity = signCertifIdentity;
	}
	@Column(name = "sign_ltv")
	public String getSignLtv() {
		return this.signLtv;
	}
	public void setSignLtv(String signLtv) {
		this.signLtv = signLtv;
	}
	@Column(name = "sign_trusted", length = 1)
	public String getSignTrusted() {
		return this.signTrusted;
	}
	public void setSignTrusted(String signTrusted) {
		this.signTrusted = signTrusted;
	}
	@Column(name = "sign_issuer")
	public String getSignIssuer() {
		return this.signIssuer;
	}
	public void setSignIssuer(String signIssuer) {
		this.signIssuer = signIssuer;
	}
	@Column(name = "sign_chain_level", length = 1)
	public String getSignChainLevel() {
		return this.signChainLevel;
	}
	public void setSignChainLevel(String signChainLevel) {
		this.signChainLevel = signChainLevel;
	}
}