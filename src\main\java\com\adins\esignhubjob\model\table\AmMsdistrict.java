package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "am_msdistrict")
public class AmMsdistrict extends CreatableAndUpdatableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	
    private Long idMsdistrict;
	private String districtName;
	private Long districtId;
	private AmMsprovince amMsprovince;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_msdistrict", unique = true, nullable = false)
	public Long getIdMsdistrict() {
		return idMsdistrict;
	}

	public void setIdMsdistrict(Long idMsdistrict) {
		this.idMsdistrict = idMsdistrict;
	}
	
	@Column(name = "district_name", length = 70)
	public String getDistrictName() {
		return districtName;
	}

	public void setDistrictName(String districtName) {
		this.districtName = districtName;
	}
	
	@Column(name = "district_id")
	public Long getDistrictId() {
		return districtId;
	}

	public void setDistrictId(Long districtId) {
		this.districtId = districtId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_msprovince", nullable = false)
	public AmMsprovince getAmMsprovince() {
		return amMsprovince;
	}

	public void setAmMsprovince(AmMsprovince amMsprovince) {
		this.amMsprovince = amMsprovince;
	}
}
