package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_doc_template_sign_loc")
public class MsDocTemplateSignLoc extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	public static final String ID_MS_DOC_TEMP_SIGN_LOC_HBM = "idMsDocTemplateSignLoc";
	
	private long idMsDocTemplateSignLoc;
	private MsDocTemplate msDocTemplate;
	private MsLov msLovByLovSignType;
	private MsLov msLovByLovSignerType;
	private String signLocation;
	private Integer signPage;
	private Short seqNo;
	private String transform;
	private String tekenAjaSignLocation;
	private String vidaSignLocation;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_doc_template_sign_loc", unique = true, nullable = false)
	public long getIdMsDocTemplateSignLoc() {
		return this.idMsDocTemplateSignLoc;
	}

	public void setIdMsDocTemplateSignLoc(long idMsDocTemplateSignLoc) {
		this.idMsDocTemplateSignLoc = idMsDocTemplateSignLoc;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_doc_template", nullable = false)
	public MsDocTemplate getMsDocTemplate() {
		return this.msDocTemplate;
	}

	public void setMsDocTemplate(MsDocTemplate msDocTemplate) {
		this.msDocTemplate = msDocTemplate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sign_type")
	public MsLov getMsLovByLovSignType() {
		return this.msLovByLovSignType;
	}

	public void setMsLovByLovSignType(MsLov msLovByLovSignType) {
		this.msLovByLovSignType = msLovByLovSignType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_signer_type")
	public MsLov getMsLovByLovSignerType() {
		return this.msLovByLovSignerType;
	}

	public void setMsLovByLovSignerType(MsLov msLovByLovSignerType) {
		this.msLovByLovSignerType = msLovByLovSignerType;
	}

	@Column(name = "sign_location", length = 250)
	public String getSignLocation() {
		return this.signLocation;
	}

	public void setSignLocation(String signLocation) {
		this.signLocation = signLocation;
	}

	@Column(name = "sign_page")
	public Integer getSignPage() {
		return this.signPage;
	}

	public void setSignPage(Integer signPage) {
		this.signPage = signPage;
	}

	@Column(name = "seq_no")
	public Short getSeqNo() {
		return this.seqNo;
	}

	public void setSeqNo(Short seqNo) {
		this.seqNo = seqNo;
	}
	
	@Column(name = "transform", length = 250)
	public String getTransform() {
		return this.transform;
	}

	public void setTransform(String transform) {
		this.transform = transform;
	}

	@Column(name = "tekenaj_sign_location", length = 100)
	public String getTekenAjaSignLocation() {
		return tekenAjaSignLocation;
	}

	public void setTekenAjaSignLocation(String tekenAjaSignLocation) {
		this.tekenAjaSignLocation = tekenAjaSignLocation;
	}

	@Column(name = "vida_sign_location", length = 100)
	public String getVidaSignLocation() {
		return vidaSignLocation;
	}

	public void setVidaSignLocation(String vidaSignLocation) {
		this.vidaSignLocation = vidaSignLocation;
	}
}
