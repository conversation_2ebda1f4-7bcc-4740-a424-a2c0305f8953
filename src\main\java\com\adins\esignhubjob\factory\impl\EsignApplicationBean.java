package com.adins.esignhubjob.factory.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esignhubjob.businesslogic.factory.api.LogicFactory;
import com.adins.esignhubjob.dataaccess.factory.api.DaoFactory;
import com.adins.esignhubjob.factory.api.ApplicationBean;

@Component
public class EsignApplicationBean implements ApplicationBean {

    @Autowired private DaoFactory daoFactory;
    @Autowired private LogicFactory logicFactory;

    @Override
    public DaoFactory getDaoFactory() {
        return daoFactory;
    }

    @Override
    public LogicFactory getLogicFactory() {
        return logicFactory;
    }
    
}
