package com.adins.esignhubjob.model.webservice.jatis;

import java.util.List;

import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppContactBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppErrorBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppMessageBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppMetaBean;

public class JatisSendWhatsAppMessageResponse {
    private JatisWhatsAppErrorBean error;
	private JatisWhatsAppMetaBean meta;
	private List<JatisWhatsAppContactBean> contacts;
	private List<JatisWhatsAppMessageBean> messages;
	
	public JatisWhatsAppErrorBean getError() {
		return error;
	}
	public void setError(JatisWhatsAppErrorBean error) {
		this.error = error;
	}
	public JatisWhatsAppMetaBean getMeta() {
		return meta;
	}
	public void setMeta(JatisWhatsAppMetaBean meta) {
		this.meta = meta;
	}
	public List<JatisWhatsAppContactBean> getContacts() {
		return contacts;
	}
	public void setContacts(List<JatisWhatsAppContactBean> contacts) {
		this.contacts = contacts;
	}
	public List<JatisWhatsAppMessageBean> getMessages() {
		return messages;
	}
	public void setMessages(List<JatisWhatsAppMessageBean> messages) {
		this.messages = messages;
	}
}
