package com.adins.esignhubjob.model.custom.halosis;

import java.util.List;

import com.google.gson.annotations.SerializedName;

public class HalosisWhatsAppTemplateComponent {
    private String type;
	@SerializedName("sub_type") private String subType;
	private String index;
	private List<HalosisWhatsAppTemplateComponentParameter> parameters;
	
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getSubType() {
		return subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	public String getIndex() {
		return index;
	}
	public void setIndex(String index) {
		this.index = index;
	}
	public List<HalosisWhatsAppTemplateComponentParameter> getParameters() {
		return parameters;
	}
	public void setParameters(List<HalosisWhatsAppTemplateComponentParameter> parameters) {
		this.parameters = parameters;
	}
}
