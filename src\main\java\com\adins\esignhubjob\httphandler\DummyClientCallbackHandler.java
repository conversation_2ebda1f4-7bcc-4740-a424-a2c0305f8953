package com.adins.esignhubjob.httphandler;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.BaseHttpHandler;
import com.adins.exceptions.Status;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

public class DummyClientCallbackHandler extends BaseHttpHandler {

    @Override
    public void doHandleRequest(HttpServletRequest request, HttpServletResponse response, Context context) throws IOException, ServletException {
        this.validateApiKey(request);
        String input = IOUtils.toString(request.getInputStream());
        context.getLogger().info("Callback JSON request: " + input);

        Status responseStatus = new Status(0, "Success");
        response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        response.getWriter().write(gson.toJson(responseStatus));
    }
    
}
