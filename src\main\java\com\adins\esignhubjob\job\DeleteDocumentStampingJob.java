package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.Date;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.aliyun.fc.runtime.Context;

public class DeleteDocumentStampingJob extends BaseJobHandler {


    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode("DELETE_STAMPING_DOCUMENT");
        context.getLogger().info("Deleting documents stamped " + gsValue + " month(s) before today");
        
        Date startTime = new Date();
        int monthValue  = Integer.parseInt(gsValue);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.MONTH, -monthValue);
        
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

        String filePath = String.format(Constants.DELETE_STAMPED_DOCUMENT_RESULT, year, month);

        MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.LOV_CODE_SCHEDULER_MONTHLY);
        MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, Constants.LOV_CODE_DELETE_STAMP_RESULT);

        Long dataProcessed  = logicFactory.getAliyunOssCloudStorageLogic().deleteFileFromOss(filePath, context);
        context.getLogger().info("Total deleted data: " + dataProcessed);

        TrSchedulerJob insertJob = new TrSchedulerJob();
        insertJob.setDtmCrt(new Date());
        insertJob.setUsrCrt("DELETE STAMPED DOCUMENT OSS");
        insertJob.setDataProcessed(dataProcessed);
        insertJob.setSchedulerStart(startTime);
        insertJob.setSchedulerEnd(new Date());
        insertJob.setMsLovBySchedulerType(lovSchedulerType);
        insertJob.setMsLovByJobType(lovJobType);
        insertJob.setMailReminderCount((short) 0);

        daoFactory.getSchedulerJobDao().insertSchedulerJob(insertJob);
    }
    
}
