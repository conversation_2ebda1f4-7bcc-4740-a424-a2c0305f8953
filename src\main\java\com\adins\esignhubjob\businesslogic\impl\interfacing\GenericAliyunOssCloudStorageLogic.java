package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;
import com.adins.esignhubjob.model.table.TrProcessAutosignBmH;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.exceptions.EsignhubJobException;
import com.aliyun.fc.runtime.Context;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.internal.OSSUtils;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.DeleteObjectsResult;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;

@Component
public class GenericAliyunOssCloudStorageLogic implements AliyunOssCloudStorageLogic {

	@Autowired private PersonalDataEncryptionLogic encryptionLogic;

    String endPoint         = System.getenv("ENV_OSS_ENDPOINT");
    String ossBucket        = System.getenv("ENV_OSS_BUCKET");
    String accessKeyId      = System.getenv("ENV_OSS_ACCESSKEYID");
    String accessKeySecret  = System.getenv("ENV_OSS_ACCESSKEYSECRET");
	String logDeleteActive  = System.getenv("ENV_OSS_DELETE_LOG_ACTIVE");
    OSS ossClient = new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret);	

    private Long deleteFromOss(String fileName, Context context) {
        String nextMarker = null;
		Long objectCount = 0L;
		ObjectListing objectListing = null;
		
		if (null == fileName) {
			return objectCount;
		}
		context.getLogger().info(String.format("Deleting %1$s from OSS", fileName));
		do {
			ListObjectsRequest listObjectsRequest = new ListObjectsRequest(ossBucket)
				.withPrefix(fileName)
				.withMarker(nextMarker);	
				
			objectListing = ossClient.listObjects(listObjectsRequest);
			if (objectListing.getObjectSummaries().isEmpty()) {
				context.getLogger().info("No object(s) to delete from OSS");
				break;
			}
			
			List<String> keys = new ArrayList<>();
			for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
				if ("1".equalsIgnoreCase(logDeleteActive)) {
					context.getLogger().info("Key Name: " + s.getKey());
				}
				keys.add(s.getKey());
			}
					
			DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(ossBucket).withKeys(keys);
			DeleteObjectsResult  deleteObjectsResult = ossClient.deleteObjects(deleteObjectsRequest);
			List<String> deletedObjects = deleteObjectsResult.getDeletedObjects();
			for (String obj : deletedObjects) {
				if ("1".equalsIgnoreCase(logDeleteActive)) {
					context.getLogger().info(obj + " deleted from OSS");
				}
				objectCount += 1;
			}
			nextMarker = objectListing.getNextMarker();

		} while (objectListing.isTruncated());
			
		context.getLogger().info("Delete " + fileName + " from OSS finished");
		return objectCount;
    }

	private byte[] downloadFromOss(String fileName, Context context) {

		if (!OSSUtils.validateObjectKey(fileName)) {
			return null;
		}
		context.getLogger().info("Downloading " + fileName + " from OSS");
		try {
			OSSObject object = ossClient.getObject(ossBucket, fileName);		
			InputStream is = object.getObjectContent();
			
			byte[] bytearr = com.aliyun.oss.common.utils.IOUtils.readStreamAsByteArray(is);
			String downloadedObject = "Downloaded oss: " + bytearr.length;	  
			context.getLogger().info(downloadedObject);
			return bytearr;
		}
		catch (IOException e) {
			context.getLogger().error("Fail download object from OSS, key= " + fileName);
			return null;
		}
		catch (OSSException osse) {
			String exceptionOss = "Exception on downloading from oss: " + fileName + " reason: " + osse.getLocalizedMessage();	  
			context.getLogger().info(exceptionOss);
			return null;
		}
	}

	private void uploadToOss(String fileName, byte[] bytearr, Context context) {
		context.getLogger().info("Storing " + fileName + " to OSS");
		ossClient.putObject(ossBucket, fileName, new ByteArrayInputStream(bytearr));
	}
	
	private void moveToArchiveBucket(String sourceFileName, Context context) {
		String archiveBucket = "esignhub-bucket2-archive";
		try {
			context.getLogger().info("Moving " + sourceFileName + " to archive bucket (" + archiveBucket + ")");
			boolean sourceExists = ossClient.doesObjectExist(ossBucket, sourceFileName);
			if (!sourceExists) {
				context.getLogger().warn("Source file " + sourceFileName + " does not exist in esignhub-bucket2");
				return;
			}
			ossClient.copyObject(ossBucket, sourceFileName, archiveBucket, sourceFileName);
			deleteFromOss(sourceFileName, context);
			context.getLogger().info("Successfully moved " + sourceFileName + " to " + archiveBucket);
		} catch (Exception e) {
			context.getLogger().error("Error moving file from " + sourceFileName + " to " + archiveBucket);
			context.getLogger().error(ExceptionUtils.getStackTrace(e));
		}
	}
	
	@Override
	public Long deleteFileFromOss(String path, Context context) {
		context.getLogger().info("Deleting " + path + " from OSS");
		return this.deleteFromOss(path, context);
	}

	@Override
	public byte[] getBaseSignDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.BASE_SIGN_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename, context));

		return decryptFile;
	}

	@Override
	public String storeBaseSignDocument(TrDocumentD document, byte[] documentByteArray, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.BASE_SIGN_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] encryptBytearr = encryptionLogic.encrypt(documentByteArray);

		uploadToOss(filename, encryptBytearr, context);
		return filename;
	}

	@Override
	public String storeSignedDocument(TrDocumentD document, byte[] documentByteArray, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.SIGNED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] encryptBytearr = encryptionLogic.encrypt(documentByteArray);

		uploadToOss(filename, encryptBytearr, context);
		return filename;
	}

	@Override
	public void deleteBaseSignDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.BASE_SIGN_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		deleteFromOss(filename, context);
	}

	@Override
	public void deleteSignedDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.SIGNED_DOCUMENT_FORMAT, year, month, document.getDocumentId());

		deleteFromOss(filename, context);
	}

	@Override
	public byte[] getUserSelfie(String trxNo, Date trxDate, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(trxDate);
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.USER_SELFIE_FORMAT, year, month, trxNo);
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename, context));

		return decryptFile;
	}

	@Override
	public byte[] getRegisterRequest(TrJobCheckRegisterStatus jobCheckRegisterStatus, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(jobCheckRegisterStatus.getDtmCrt());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		String filename = String.format(Constants.REGISTER_REQUEST_FORMAT, year, month, jobCheckRegisterStatus.getIdJobCheckRegisterStatus());
		
		return downloadFromOss(filename, context);
	}

	@Override
	public String storeRegistrationSelfie(String nik, byte[] photo, Context context) {
		String fileName = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.PERSONAL_SELFIE_FILENAME_FORMAT, nik);
		byte[] encryptBytearr = encryptionLogic.encrypt(photo);

		uploadToOss(fileName, encryptBytearr, context);
		return fileName;
	}

	@Override
	public String storeRegistrationKtp(String nik, byte[] photo, Context context) {
		String fileName = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.PERSONAL_KTP_FILENAME_FORMAT, nik);
		byte[] cipheredBytes = encryptionLogic.encrypt(photo);
		uploadToOss(fileName, cipheredBytes, context);
		return fileName;
	}

	@Override
	public byte[] getContentKtp(String key, Context context) {
		byte[] cipheredBytes = downloadFromOss(key, context);
		if (null == cipheredBytes) {
			return null;
		}
		return encryptionLogic.decrypt(cipheredBytes);
	}


	@Override
	public byte[] getContentNonKtp(String key, Context context) {
		return downloadFromOss(key, context);
	}

	@Override
	public byte[] getStampingDocument(String tenantCode, String refNumber, String documentId, Context context) {
		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.DOCUMENT_STAMPINGDOC_FORMAT, tenantCode, refNumber, documentId);
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename, context));

		return decryptFile;
	}

	@Override
	public byte[] getManualStamp(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getDtmCrt());

		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.MANUAL_STAMP_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename, context));

		return decryptFile;
	}

	@Override
	public byte[] getSignedDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.SIGNED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename, context));

		return decryptFile;
	}

	@Override
	public String storeStampedDocument(TrDocumentD document, byte[] documentByteArray, Context context) {
		
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.STAMPED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] cipheredBytes = encryptionLogic.encrypt(documentByteArray);
		uploadToOss(filename, cipheredBytes, context);
		return filename;
	}

	@Override
	public byte[] getStampedDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.STAMPED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename, context));

		return decryptFile;
	}

	@Override
	public byte[] getAutosignImportExcel(TrProcessAutosignBmH processAutosignBmH, Context context) {
		
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(processAutosignBmH.getTrxDate());

		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		MsTenant tenant = processAutosignBmH.getMsTenant();

		String filename = String.format(Constants.AUTOSIGN_IMPORT_FORMAT, year, month, tenant.getTenantCode(), processAutosignBmH.getIdProcessAutosignBmH(), processAutosignBmH.getFileName());

		return downloadFromOss(filename, context);

	}

	@Override
	public String storeZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail, byte[] zippedTextFile, Context context) {
		String filename = String.format(Constants.AUDIT_TRAIL_API_LOG_FORMAT, subfolderName, trail.getIdSigningProcessAuditTrail());
		byte[] cipheredBytes = encryptionLogic.encrypt(zippedTextFile);
		uploadToOss(filename, cipheredBytes, context);
		return filename;
	}

	@Override
	public byte[] getZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail, Context context) {
		String filename = String.format(Constants.AUDIT_TRAIL_API_LOG_FORMAT, subfolderName, trail.getIdSigningProcessAuditTrail());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename, context));

		return decryptFile;
	}

	@Override
	public void deleteZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail, Context context) {
		if (null == trail) {
			return;
		}

		String filename = String.format(Constants.AUDIT_TRAIL_API_LOG_FORMAT, subfolderName, trail.getIdSigningProcessAuditTrail());
		deleteFromOss(filename, context);
	}

	@Override
	public List<String> getListExpiredFolder(String folderPath) {

		List<String> files = new ArrayList<>();
		
		ObjectListing objectListing = ossClient.listObjects(ossBucket, folderPath);
		List<OSSObjectSummary> objectSummaries = objectListing.getObjectSummaries();

		for (OSSObjectSummary objectSummary : objectSummaries) {
			files.add(objectSummary.getKey());
		}

		return files;
	}

	@Override
	public void deleteExpiredRegistrationFolder(String pathKey, Context context) {
		ObjectListing objectListing = null;
		String nextMarker = null;

		if (pathKey == null) {
			throw new EsignhubJobException("No Path");
		}

		context.getLogger().info(String.format("Deleting %1$s from OSS", pathKey));

		ListObjectsRequest listObjectsRequest = new ListObjectsRequest(ossBucket)
			.withPrefix(pathKey)
			.withDelimiter("/")
			.withMarker(nextMarker);

		do {
			objectListing = ossClient.listObjects(listObjectsRequest);
			
			if (objectListing.getObjectSummaries().isEmpty()) {
				context.getLogger().info("No folder to delete from OSS");
				break;
			}
			
			List<String> keys = new ArrayList<>();
			for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
				if ("1".equalsIgnoreCase(logDeleteActive)) {
					context.getLogger().info("Key Name: " + s.getKey());
				}
				keys.add(s.getKey());
			}
			
			// Delete the objects
			DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(ossBucket).withKeys(keys);
			DeleteObjectsResult deleteObjectsResult = ossClient.deleteObjects(deleteObjectsRequest);
			List<String> deletedObjects = deleteObjectsResult.getDeletedObjects();
			for (String obj : deletedObjects) {
				if ("1".equalsIgnoreCase(logDeleteActive)) {
					context.getLogger().info(obj + " deleted from OSS");
				}
			}
			
			nextMarker = objectListing.getNextMarker();
			listObjectsRequest.setMarker(nextMarker);

		} while (objectListing.isTruncated());
		
		context.getLogger().info("Delete " + pathKey + " from OSS finished");
	}

	@Override
	public void deleteStampingDocument(String tenantCode, String refNumber, String documentId, Context context) {
		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.DOCUMENT_STAMPINGDOC_FORMAT, tenantCode, refNumber, documentId);
		deleteFromOss(filename, context);
	}

	@Override
	public void deleteManualStamp(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getDtmCrt());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.MANUAL_STAMP_FORMAT, year, month, document.getDocumentId());
		deleteFromOss(filename, context);
	}

	@Override
	public void deleteStampedDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.STAMPED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		deleteFromOss(filename, context);
	}

	@Override
	public void storeArchiveStampedDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String sourceFilename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.STAMPED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		moveToArchiveBucket(sourceFilename, context);
	}
	@Override
	public void storeArchiveManualStamp(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getDtmCrt());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String sourceFilename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.MANUAL_STAMP_FORMAT, year, month, document.getDocumentId());
		moveToArchiveBucket(sourceFilename, context);
	}
	@Override
	public void storeArchiveStampingDocument(String tenantCode, String refNumber, String documentId, Context context) {		
		String sourceFilename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.DOCUMENT_STAMPINGDOC_FORMAT, tenantCode, refNumber, documentId);
		moveToArchiveBucket(sourceFilename, context);
	}
	@Override
	public void storeArchiveSignedDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String sourceFilename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.SIGNED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		moveToArchiveBucket(sourceFilename, context);
	}
	@Override
	public void storeArchiveBaseSignDocument(TrDocumentD document, Context context) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String sourceFilename = Constants.OSS_PREFIX_ENCRYPTED + String.format(Constants.BASE_SIGN_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		moveToArchiveBucket(sourceFilename, context);
	}
}
