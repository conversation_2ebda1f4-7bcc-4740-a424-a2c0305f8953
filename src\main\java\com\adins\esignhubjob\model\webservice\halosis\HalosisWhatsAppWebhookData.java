package com.adins.esignhubjob.model.webservice.halosis;

import com.google.gson.annotations.SerializedName;

public class HalosisWhatsAppWebhookData {
    @SerializedName("from_phone_number") private String fromPhoneNumber;
    @SerializedName("to_phone_number") private String toPhoneNumber;
    private String id;
    @SerializedName("template_name") private String templateName;
    @SerializedName("conversation_id") private String conversationId;
    private String status;
    private long timestamp;
    private String error;

    public String getFromPhoneNumber() {
        return this.fromPhoneNumber;
    }

    public void setFromPhoneNumber(String fromPhoneNumber) {
        this.fromPhoneNumber = fromPhoneNumber;
    }

    public String getToPhoneNumber() {
        return this.toPhoneNumber;
    }

    public void setToPhoneNumber(String toPhoneNumber) {
        this.toPhoneNumber = toPhoneNumber;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTemplateName() {
        return this.templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getConversationId() {
        return this.conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getTimestamp() {
        return this.timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getError() {
        return this.error;
    }

    public void setError(String error) {
        this.error = error;
    }

}
