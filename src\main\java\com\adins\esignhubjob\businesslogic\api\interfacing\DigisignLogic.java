package com.adins.esignhubjob.businesslogic.api.interfacing;

import com.adins.esignhubjob.model.webservice.digisign.DigisignReconcileRequest;
import com.adins.esignhubjob.model.webservice.digisign.DigisignReconcileResponse;
import com.aliyun.fc.runtime.Context;

public interface DigisignLogic {
    DigisignReconcileResponse getReconcileData(DigisignReconcileRequest request, String token, Context context);
}
