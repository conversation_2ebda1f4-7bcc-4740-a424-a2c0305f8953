package com.adins.esignhubjob.dataaccess.api;

import java.util.List;

import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequestDetail;

public interface DocumentSigningRequestDao {

    // tr_document_signing_request
    TrDocumentSigningRequest getDocumentSigningRequestNewTran(long idDocumentSigningRequest);
    void insertDocumentSigningRequestNewTran(TrDocumentSigningRequest documentSigningRequest);
    void updateDocumentSigningRequestNewTran(TrDocumentSigningRequest documentSigningRequest);

    // tr_document_signign_request_detail
    void insertDocumentSigningRequestDetailNewTran(TrDocumentSigningRequestDetail requestDetail);
    List<TrDocumentSigningRequestDetail> getDocumentSigningRequestDetailsNewTran(TrDocumentSigningRequest documentSigningRequest);
}
