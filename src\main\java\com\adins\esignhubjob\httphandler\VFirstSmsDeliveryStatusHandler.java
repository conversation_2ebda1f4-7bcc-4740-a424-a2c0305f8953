package com.adins.esignhubjob.httphandler;

import java.io.IOException;
import java.util.Date;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.BaseHttpHandler;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrMessageDeliveryReport;
import com.adins.exceptions.Status;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class VFirstSmsDeliveryStatusHandler extends BaseHttpHandler {

    @Override
    public void doHandleRequest(HttpServletRequest request, HttpServletResponse response, Context context) throws IOException, ServletException {
        String recipient = request.getParameter("TO");
        String reasonCode = request.getParameter("REASON_CODE");
        String deliveredDate = request.getParameter("DELIVERED_DATE");
        String statusError = request.getParameter("STATUS_ERROR");
        String clientGuid = request.getParameter("CLIENT_GUID");
        String msgStatus = request.getParameter("MSG_STATUS");

        context.getLogger().info(String.format("Processing recipient: %1$s, reason code: %2$s, delivered date: %3$s, status error: %4$s, client GUID: %5$s, message status: %6$s", recipient, reasonCode, deliveredDate, statusError, clientGuid, msgStatus));

        TrBalanceMutation balanceMutation = daoFactory.getBalanceMutationDao().getBalanceMutationByVendorTrxNo(clientGuid);

        MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);
        
        Date deliveryDate = Tools.formatStringToDate(deliveredDate, Constants.DATE_TIME_FORMAT_SEC);
        // Tambah 1.5h untuk menyesuaikan dengan GMT+7
        deliveryDate = DateUtils.addHours(deliveryDate, 1);
        deliveryDate = DateUtils.addMinutes(deliveryDate, 30);

        TrMessageDeliveryReport report = daoFactory.getMessageDeliveryReportDao().getMessageDeliveryReport(balanceMutation.getVendorTrxNo(), deliveryDate);
        if (null != report) {
            String deliveryLogDate = Tools.formatDateToStringIn(deliveryDate, Constants.DATE_TIME_FORMAT_SEC);
            context.getLogger().info(String.format("Report for vendor trx no %1$s with delivery date %2$s existed (Skip inserting new report)", balanceMutation.getVendorTrxNo(), deliveryLogDate));
            
            Status responseStatus = new Status(0, "Success");
            response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            response.getWriter().write(gson.toJson(responseStatus));
            return;
        }

        TrMessageDeliveryReport existingReport = daoFactory.getMessageDeliveryReportDao().getMessageDeliveryReportByTrxNo(balanceMutation.getTrxNo());
        MsLov lovSendingPoint = existingReport.getMsLovSendingPoint();
        MsLov lovCredentialType = existingReport.getMsLovCredentialType();
        MsLov lovMessageMedia = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_MESSAGE_MEDIA, Constants.LOV_CODE_MESSAGE_MEDIA_SMS);
        MsLov lovMessageGateway = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SMS_GATEWAY, Constants.LOV_CODE_SMS_GATEWAY_VFIRST);
        
        report = new TrMessageDeliveryReport();
        report.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
        report.setDtmCrt(new Date());
        report.setMsVendor(vendor);
        report.setMsTenant(balanceMutation.getMsTenant());
        report.setReportTime(deliveryDate);
        report.setRequestTime(balanceMutation.getTrxDate());
        report.setRecipientDetail(recipient.replaceFirst("^62", "0"));
        report.setTrxNo(balanceMutation.getTrxNo());
        report.setVendorTrxNo(balanceMutation.getVendorTrxNo());
        report.setMsLov(lovMessageMedia);
        report.setMsLovMessageGateway(lovMessageGateway);
        report.setMsLovCredentialType(lovCredentialType);
        report.setMsLovSendingPoint(lovSendingPoint);
        report.setDeliveryStatus(getFormattedDeliveryStatus(reasonCode));
        daoFactory.getMessageDeliveryReportDao().insertMessageDeliveryReport(report);

        Status responseStatus = new Status(0, "Success");
        response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        response.getWriter().write(gson.toJson(responseStatus));

    }

    private String getFormattedDeliveryStatus(String originalDeliveryStatus) {
        /*
         * eSignHub delivery status
         * 1 sent
         * 2 failed
         * 3 delivered
         * 4 read
         */
        
        switch (originalDeliveryStatus) {
            case "402":
                return "3";
            case "173":
                return "3";
            case "000":
                return "3";
            default:
                return "2";
        }
    }
    
}
