package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_job_update_psre_id")
public class TrJobUpdatePsreId extends CreatableAndUpdatableEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private long idJobUpdatePsreId;
	private String requestId;
	private AmMsuser amMsuser;
	private MsVendor msVendor;
	private MsTenant msTenant;
	private Short requestStatus;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_job_update_psre_id", unique = true, nullable = false)
	public long getIdJobUpdatePsreId() {
		return idJobUpdatePsreId;
	}
	
	public void setIdJobUpdatePsreId(long idJobUpdatePsreId) {
		this.idJobUpdatePsreId = idJobUpdatePsreId;
	}
	
	@Column(name = "request_id", nullable = false, length = 100)
	public String getRequestId() {
		return requestId;
	}
	
	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
	public AmMsuser getAmMsuser() {
		return amMsuser;
	}
	
	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor")
	public MsVendor getMsVendor() {
		return msVendor;
	}
	
	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "request_status")
	public Short getRequestStatus() {
		return requestStatus;
	}
	
	public void setRequestStatus(Short requestStatus) {
		this.requestStatus = requestStatus;
	}
	
}