package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.DocumentLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.EmateraiPajakkuLogic;
import com.adins.esignhubjob.model.custom.pajakku.PajakkuDocumentTypeBean;
import com.adins.esignhubjob.model.custom.pajakku.PajakkuDocumentTypeResultBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsPeruriDocType;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.aliyun.fc.runtime.Context;

@Component
public class GenericDocumentLogic extends BaseLogic implements DocumentLogic{

    @Autowired private EmateraiPajakkuLogic emateraiPajakkuLogic;

    @Override
    public void updateDocumentType(AuditContext audit, Context context) throws IOException {
        Date startTime = new Date();
		Long data = 0L	;
		PajakkuDocumentTypeBean jsonDocType =  emateraiPajakkuLogic.getDocumentType(context);

        List<String> allperuriDocId = new ArrayList<>();

        for(PajakkuDocumentTypeResultBean documentTypeResult : jsonDocType.getResult()) {
            context.getLogger().info(String.format("Id: %1$s, kode: %2$s, name: %3$s", documentTypeResult.getId(), documentTypeResult.getKode(), documentTypeResult.getNama()));
			String peruriDocId = documentTypeResult.getId();
			MsPeruriDocType docType = daoFactory.getPeruriDocTypeDao().getMsPeruriDocTypeByDocId(peruriDocId); 
			
			allperuriDocId.add(peruriDocId);
			
			if (docType == null) {
				docType = new MsPeruriDocType();
				docType.setPeruriDocId(documentTypeResult.getId());
				docType.setDocCode(documentTypeResult.getKode());
				docType.setDocName(documentTypeResult.getNama());
				docType.setIsActive("1");
				docType.setUsrCrt(audit.getCallerId());
				docType.setDtmCrt(new Date());
				daoFactory.getPeruriDocTypeDao().insertMsPeruriDocType(docType);
				data++;
			} else if ( (!docType.getDocName().equals(documentTypeResult.getNama())) || (!docType.getDocCode().equals(documentTypeResult.getKode())) || (!docType.getIsActive().equals("1"))){
				docType.setDocCode(documentTypeResult.getKode());
				docType.setDocName(documentTypeResult.getNama());
				docType.setIsActive("1");
				docType.setUsrUpd(audit.getCallerId());
				docType.setDtmUpd(new Date());
				daoFactory.getPeruriDocTypeDao().updateMsPeruriDocType(docType);
				data++;
			}
		}

        daoFactory.getPeruriDocTypeDao().deactiveDocumentNotInDocId(allperuriDocId, audit.getCallerId());
		
		String notes = "";
		this.insertTrSchedulerJob(startTime, Constants.LOV_CODE_SCHEDULER_DAILY , Constants.CODE_LOV_JOB_TYPE_SYNCREGTEKEN,
				data, notes, audit);
    }

	private void insertTrSchedulerJob(Date startTime, String lovSchedulerTypeCode, String lovJobTypeCode,
			long dataProcessed, String notes, AuditContext audit) {
		MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE,
				lovSchedulerTypeCode);
		MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, lovJobTypeCode);

		TrSchedulerJob schedulerJob = new TrSchedulerJob();
		schedulerJob.setSchedulerStart(startTime);
		schedulerJob.setSchedulerEnd(new Date());
		schedulerJob.setMsLovBySchedulerType(lovSchedulerType);
		schedulerJob.setMsLovByJobType(lovJobType);
		schedulerJob.setDataProcessed(dataProcessed);
		schedulerJob.setNotes(notes);
		schedulerJob.setUsrCrt(audit.getCallerId());
		schedulerJob.setDtmCrt(new Date());
		daoFactory.getSchedulerJobDao().insertSchedulerJob(schedulerJob);
	}

	@Override
	public void updateDocumentUnfinishedExpiredJob(AuditContext audit, Context context) {
		
		List<String> expiredDocument =  daoFactory.getDocumentDao().getExpiredUnfinishDocumentNewTrx();
		
		for (String refNumber : expiredDocument){
		
			TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByRefNumberNewTrx(refNumber);
			documentH.setIsActive("0");
			documentH.setDtmUpd(new Date());
			documentH.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHeader(documentH);

		}
		context.getLogger().info(" jumlah document yang diupdate " + expiredDocument.size() );
		String notes = "";
		this.insertTrSchedulerJob(new Date(), Constants.CODE_LOV_SCHED_TYPE_MONTHLY, Constants.CODE_LOV_JOB_TYPE_UNFINISHDOCUMENT,
				expiredDocument.size(), notes, audit);
	}
    
}
