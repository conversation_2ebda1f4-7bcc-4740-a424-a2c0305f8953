package com.adins.esignhubjob.businesslogic.api.interfacing;

import java.util.Date;
import java.util.List;

import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;
import com.adins.esignhubjob.model.table.TrProcessAutosignBmH;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.aliyun.fc.runtime.Context;

public interface AliyunOssCloudStorageLogic {
    public Long deleteFileFromOss(String path, Context context);

    // KTP & Selfie
    public String storeRegistrationSelfie(String nik, byte[] photo, Context context);
	public String storeRegistrationKtp(String nik, byte[] photo, Context context);
	public byte[] getContentKtp(String key, Context context); //Foto ktp dienkrip
	public byte[] getContentNonKtp(String key, Context context); //Foto non-ktp tidak dienkrip

    // Base sign document
    byte[] getBaseSignDocument(TrDocumentD document, Context context);
    String storeBaseSignDocument(TrDocumentD document, byte[] documentByteArray, Context context); // unsigned
    void deleteBaseSignDocument(TrDocumentD document, Context context);
    void storeArchiveBaseSignDocument(TrDocumentD document, Context context);

    // Signed document, directory: /sign_complete/
    String storeSignedDocument(TrDocumentD document, byte[] documentByteArray, Context context);
    void deleteSignedDocument(TrDocumentD document, Context context); // sign complete
    byte[] getSignedDocument(TrDocumentD document, Context context);
    void storeArchiveSignedDocument(TrDocumentD document, Context context);

    // User selfie, directory: selfie_user/{year}/{month}/{trx no}.jpeg
    byte[] getUserSelfie(String trxNo, Date trxDate, Context context);

    // Register request, directory: register_request/{year}/{month}/{id_job_check_register_status}.txt
    byte[] getRegisterRequest(TrJobCheckRegisterStatus jobCheckRegisterStatus, Context context);

    // Stamping Document, directory: /stamping_eMaterai/
    byte[] getStampingDocument(String tenantCode, String refNumber, String documentId, Context context);
    void deleteStampingDocument(String tenantCode, String refNumber, String documentId, Context context);
    void storeArchiveStampingDocument(String tenantCode, String refNumber, String documentId, Context context);

    //Save Manual Stamp , directory: stamping_ematerai\{year}\{month}\{document_id}.pdf
    byte[] getManualStamp(TrDocumentD document, Context context);
    void deleteManualStamp(TrDocumentD document, Context context);
    void storeArchiveManualStamp(TrDocumentD document, Context context);

    // Stamped Document, directory: /stamping_result/
    String storeStampedDocument(TrDocumentD document, byte[] documentByteArray, Context context);
    byte[] getStampedDocument(TrDocumentD document, Context context);
    void deleteStampedDocument(TrDocumentD document, Context context);
    void storeArchiveStampedDocument(TrDocumentD document, Context context);

    // Autosign Import Excel, directory: import_bm\{year}\{month}\{tenant_code}-{id_process_autosign_bm_h}-{file_excel_name}.xlsx
    byte[] getAutosignImportExcel(TrProcessAutosignBmH processAutosignBmH, Context context);

    // Audit trail API log, directory: audit_log/{process_name}/{id_signing_process_audit_trail}.zip
	String storeZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail, byte[] zippedTextFile, Context context);
	byte[] getZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail, Context context);
    void deleteZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail, Context context);
    void deleteExpiredRegistrationFolder(String pathKey, Context context);
    List<String> getListExpiredFolder(String path);
}
