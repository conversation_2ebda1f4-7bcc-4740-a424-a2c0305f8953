package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.AmGlobalKey;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.TenantDao;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Component
@Transactional
public class TenantDaoHbn extends BaseDaoHbn implements TenantDao {

    @Override
    public MsTenant getTenantByApiKey(String apiKey) {
        Map<String, Object> params = new HashMap<>();
        params.put("apiKey", apiKey);

        return managerDAO.selectOne(
            "from MsTenant "
            + "where apiKey = :apiKey ", params);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<MsTenant> getActiveTenants() {
       Map<String, Object> mapListTenant =  this.getManagerDAO().list(MsTenant.class, 
				new Object[][] {{ActivatableEntity.IS_ACTIVE_HBM, "1"}}, null);
		return (List<MsTenant>) mapListTenant.get(AmGlobalKey.MAP_RESULT_LIST);
    }

    @Override
	public MsTenant getTenantByCode(String tenantCode) {
		
		// Query diubah pakai left join karena untuk sekarang ada case tenant id_email_hosting null atau notif_type null
		if (StringUtils.isBlank(tenantCode))
			return null;
	
		return this.managerDAO.selectOne(
				"from MsTenant t "
				+ "left join fetch t.msLov "
				+ "left join fetch t.msEmailHosting "
				+ "left join fetch t.lovSmsGateway "
				+ "left join fetch t.lovDefaultOtpSendingOption "
				+ "where t.tenantCode = :tenantCode and t.isActive ='1'", 
						new Object[][] {{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}});
	}
    
}

