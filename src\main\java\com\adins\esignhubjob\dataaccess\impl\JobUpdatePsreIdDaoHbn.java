package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.JobUpdatePsreIdDao;
import com.adins.esignhubjob.model.table.TrJobUpdatePsreId;
import com.adins.util.Tools;

@Component
@Transactional
public class JobUpdatePsreIdDaoHbn extends BaseDaoHbn implements JobUpdatePsreIdDao {

    @Override
    public void updateJobUpdatePsreId(TrJobUpdatePsreId jobUpdatePsreId) {
        jobUpdatePsreId.setUsrUpd(Tools.maskData(jobUpdatePsreId.getUsrUpd()));
        managerDAO.update(jobUpdatePsreId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateJobUpdatePsreIdNewTran(TrJobUpdatePsreId jobUpdatePsreId) {
        jobUpdatePsreId.setUsrUpd(Tools.maskData(jobUpdatePsreId.getUsrUpd()));
        managerDAO.update(jobUpdatePsreId);
    }

    @Override
    public TrJobUpdatePsreId getJobUpdatePsreId(long idJobUpdatePsreId) {
        Map<String, Object> params = new HashMap<>();
        params.put("idJobUpdatePsreId", idJobUpdatePsreId);

        return managerDAO.selectOne(
            "from TrJobUpdatePsreId upi "
            + "join fetch upi.amMsuser mu "
            + "join fetch upi.msVendor mv "
            + "join fetch upi.msTenant mt "
            + "where upi.idJobUpdatePsreId = :idJobUpdatePsreId ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrJobUpdatePsreId getJobUpdatePsreIdNewTran(long idJobUpdatePsreId) {
        Map<String, Object> params = new HashMap<>();
        params.put("idJobUpdatePsreId", idJobUpdatePsreId);

        return managerDAO.selectOne(
            "from TrJobUpdatePsreId upi "
            + "join fetch upi.amMsuser mu "
            + "join fetch upi.msVendor mv "
            + "join fetch upi.msTenant mt "
            + "where upi.idJobUpdatePsreId = :idJobUpdatePsreId ", params);
    }
    
}
