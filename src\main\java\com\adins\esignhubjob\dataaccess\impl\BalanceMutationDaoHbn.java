package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.BalanceMutationDao;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.util.Tools;

@Component
@Transactional
public class BalanceMutationDaoHbn extends BaseDaoHbn implements BalanceMutationDao {

    @Override
    public void insertBalanceMutation(TrBalanceMutation balanceMutation) {
        balanceMutation.setUsrCrt(Tools.maskData(balanceMutation.getUsrCrt()));
        managerDAO.insert(balanceMutation);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertBalanceMutationNewTran(TrBalanceMutation balanceMutation) {
        balanceMutation.setUsrCrt(Tools.maskData(balanceMutation.getUsrCrt()));
        managerDAO.insert(balanceMutation);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteBalanceMutationNewTran(MsTenant tenant, MsVendor vendor, MsLov lovBalanceType, MsLov lovTrxType, Date startTime, Date endTime) {
        Map<String, Object> params = new HashMap<>();
        params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
        params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
        params.put("lovBalType", lovBalanceType.getIdLov());
        params.put("lovTrxType", lovTrxType.getIdLov());
        params.put("startTime", startTime);
        params.put("endTime", endTime);

        StringBuilder query = new StringBuilder();
        query
            .append("delete from tr_balance_mutation ")
            .append("where id_ms_tenant = :idMsTenant ")
            .append("and id_ms_vendor = :idMsVendor ")
            .append("and lov_balance_type = :lovBalType ")
            .append("and lov_trx_type = :lovTrxType ")
            .append("and trx_date >= :startTime ")
            .append("and trx_date <= :endTime ");

        managerDAO.deleteNativeString(query.toString(), params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateBalanceMutationNewTran(TrBalanceMutation balanceMutation) {
        balanceMutation.setUsrUpd(Tools.maskData(balanceMutation.getUsrUpd()));
        managerDAO.update(balanceMutation);
    }

    @Override
    public TrBalanceMutation getBalanceMutationByTrxNo(String trxNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("trxNo", trxNo);

        return managerDAO.selectOne(
            "from TrBalanceMutation bm "
            + "join fetch bm.msTenant mt "
            + "join fetch bm.msLovByLovBalanceType lbt "
            + "where bm.trxNo = :trxNo ", params);
    }

    @Override
    public BigInteger getCurrentBalance(MsTenant tenant, MsVendor vendor, MsLov lovBalanceType) {
        Map<String, Object> params = new HashMap<>();
        params.put("date", new Date());
        params.put("balanceType", lovBalanceType.getIdLov());
        params.put("vendorId", vendor.getIdMsVendor());
        params.put("tenantId", tenant.getIdMsTenant());

        StringBuilder query = new StringBuilder();
        query
            .append("with bdc as ( ")
                .append("select recap_date, recap_total_balance ")
                .append("from tr_balance_daily_recap bdc ")
                .append("where bdc.lov_balance_type = :balanceType ")
                .append("and bdc.id_ms_tenant = :tenantId and bdc.id_ms_vendor = :vendorId " )
                .append("and bdc.recap_date <= :date ")
                .append("order by bdc.recap_date desc limit 1 ")
            .append(") ")
            .append("select CAST(SUM(COALESCE(recap_total_balance,0) + COALESCE(number_of_use,0)) AS BIGINT) ")
            .append("from bdc ")
            .append("left join lateral ( ")
                .append("select SUM(qty) as number_of_use ")
                .append("from tr_balance_mutation bm ")
                .append("where DATE(bm.trx_date)>bdc.recap_date ")
                .append("and bm.lov_balance_type = :balanceType ")
                .append("and bm.id_ms_tenant= :tenantId and bm.id_ms_vendor= :vendorId ")
            .append(") bm on TRUE ");

        return (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
    }

    @Override
    public TrBalanceMutation getBalanceMutationByVendorTrxNo(String vendorTrxNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("vendorTrxNo", vendorTrxNo);

        return managerDAO.selectOne(
            "from TrBalanceMutation bm "
            + "join fetch bm.msTenant mt "
            + "where bm.vendorTrxNo = :vendorTrxNo ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrBalanceMutation getBalanceMutationByVendorTrxNoNewTran(String vendorTrxNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("vendorTrxNo", vendorTrxNo);

        return managerDAO.selectOne(
            "from TrBalanceMutation bm "
            + "join fetch bm.msTenant mt "
            + "where bm.vendorTrxNo = :vendorTrxNo ", params);
    }
    
    @Override
    public TrBalanceMutation getUsersLatestTrx(long idMsUser) {
        Object[][] params = new Object[][] {
			{"idUser", idMsUser}
		};

        StringBuilder query = new StringBuilder();
        query.append("select trx_no from tr_balance_mutation ")
            .append("where id_ms_user = :idUser ")
            .append("order by trx_date desc ")
            .append("limit 1");

        String trxNo = (String) this.managerDAO.selectOneNativeString(query.toString(), params);
        if (null == trxNo) {
            return null;
        }

        return getBalanceMutationByTrxNo(trxNo);
    }

    @Override
    public TrBalanceMutation getBalanceMutationById(long idBalanceMutation) {
       Object[][] queryParams = { 
			{ Restrictions.eq("idBalanceMutation", idBalanceMutation)}};
		return managerDAO.selectOne(TrBalanceMutation.class, queryParams);
    }

    
    @SuppressWarnings("unchecked")
    @Override 
    @Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrBalanceMutation> getListBalanceMutationDailyRecapTopUpByDate (MsTenant tenant,MsVendor vendor, MsLov balanceType , String date){
		Object[][] queryParams = {
				{"msTenant",tenant},
				{"msVendor", vendor},
				{"msLov", balanceType},
				{"date", Tools.formatStringToDate(date, Constants.DATE_FORMAT) }
		};
		
		return (List<TrBalanceMutation>) this.managerDAO.list(
				"from TrBalanceMutation bm "
				+ "where bm.msTenant = :msTenant "
					+ " and bm.msVendor = :msVendor " 
				+ " and bm.msLovByLovBalanceType = :msLov " 
					+ " and Date(bm.trxDate) = :date "
					+ " and bm.trBalanceTopUp is null "
					+ " and bm.qty < 0 ", queryParams)
				.get("resultList");
	}

//method sementara untuk jalan kan daily recap
    @SuppressWarnings("unchecked")
    @Override 
    @Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrBalanceMutation> getListBalanceMutationDailyRecapTopUpRecountByDate (MsTenant tenant,MsVendor vendor, MsLov balanceType , String date){
		Object[][] queryParams = {
				{"msTenant",tenant},
				{"msVendor", vendor},
				{"msLov", balanceType},
				{"date", Tools.formatStringToDate(date, Constants.DATE_FORMAT) }
		};
		
		return (List<TrBalanceMutation>) this.managerDAO.list(
				"from TrBalanceMutation bm "
				+ "where bm.msTenant = :msTenant "
					+ " and bm.msVendor = :msVendor " 
				+ " and bm.msLovByLovBalanceType = :msLov " 
					+ " and Date(bm.trxDate) = :date "
					+ " and bm.trBalanceTopUp is null "
					+ " and bm.qty < 0 ", queryParams)
				.get("resultList");
	}


    @Override
	public BigInteger getSingleBalanceByVendorAndTenant(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov) {
		Map<String, Object> params = new HashMap<>();
		params.put("date", new Date());
		params.put("balanceType", balanceTypeLov.getIdLov());
		params.put("vendorId", vendor.getIdMsVendor());
		params.put("tenantId", tenant.getIdMsTenant());
		
		StringBuilder query = new StringBuilder();
		query.append("with bdc as ( ")
			.append("     Select recap_date, recap_total_balance ")
			.append("     from   tr_balance_daily_recap bdc ")
			.append("     where  bdc.lov_balance_type = :balanceType ")
					.append( " and bdc.id_ms_tenant = :tenantId and bdc.id_ms_vendor = :vendorId")
					.append( " and bdc.recap_date <= :date ")
			.append("    order by bdc.recap_date desc limit 1 ")
			.append(" ) ")
			.append("select CAST(SUM(COALESCE(recap_total_balance,0) + COALESCE(number_of_use,0)) AS BIGINT)  ")
			.append("from bdc ")
			.append("left join lateral (Select SUM(qty) as number_of_use ")
			.append("                   from tr_balance_mutation bm")
			.append("                   where DATE(bm.trx_date)>bdc.recap_date ")
			.append("                     and bm.lov_balance_type = :balanceType ")
			.append("                     and bm.id_ms_tenant= :tenantId and bm.id_ms_vendor= :vendorId ")
			.append(") bm on TRUE ");
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		 
	}

    @Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertBalanceMutationNativeString(long idBalancemutation, long idTopUp, String usrUpd, String notes, int qty) {
		Map<String, Object> params = new HashMap<>();
        params.put("idBalancemutation", idBalancemutation);
        params.put("usrUpd", Tools.maskData(usrUpd));
		params.put("qty", qty);
		params.put("notes", notes);
        params.put("idTopUp", idTopUp);

		StringBuilder query = new StringBuilder();
		query.append(" INSERT INTO tr_balance_mutation (trx_no, trx_date, ref_no, qty, lov_trx_type, lov_balance_type, usr_crt, dtm_crt, id_ms_tenant , id_ms_vendor , notes, id_balance_top_up) ")
		.append(" select (select nextval('sq_trbalancemutation_trxno')), now(), bm.ref_no, :qty, (select id_lov  from ms_lov where code = CONCAT('U', (select constraint_1 from ms_lov ml2 where id_lov = bm.lov_trx_type and lov_group = 'TRX_TYPE'))), bm.lov_balance_type, :usrUpd, now(), bm.id_ms_tenant, bm.id_ms_vendor, :notes, :idTopUp ")
		.append(" from tr_balance_mutation bm ")
        .append(" join ms_lov ml on bm.lov_balance_type = ml.id_lov  ")
        .append(" where bm.id_balance_mutation = :idBalancemutation ");
        
        managerDAO.updateNativeString(query.toString(), params);
	}

    
    @Override
	public TrBalanceMutation getVendorTenantLovByIdBalMut(Long idBalMut) {
		if (idBalMut == null){
			return null;
		}
	
		return this.managerDAO.selectOne(
			"from TrBalanceMutation bm "
				+ "join fetch bm.msVendor "
                + "join fetch bm.msTenant "
                + "join fetch bm.msLovByLovBalanceType "
				+ "where bm.idBalanceMutation = :idBalMut", 
						new Object[][] {{"idBalMut", idBalMut}});
	}

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getListMutationForTopupBinding(MsTenant tenant, MsVendor vendor, MsLov lovBalanceType, Date trxDate, long availableBalance) {
        
        Map<String, Object> params = new HashMap<>();
        params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
        params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
        params.put(MsLov.ID_LOV_HBM, lovBalanceType.getIdLov());
        params.put("trxDate", trxDate);
        params.put("availableBalance", availableBalance);

        StringBuilder query = new StringBuilder();
        query
            .append("WITH running_balance AS ( ")
                .append("SELECT bm.id_balance_mutation, ")
                .append("bm.qty AS affected_qty, ")
                .append("ABS(SUM(bm.qty) OVER (ORDER BY bm.id_balance_mutation ASC)) AS affected_running_qty ")
                .append("FROM tr_balance_mutation bm ")
                .append("WHERE DATE(bm.trx_date) = :trxDate ")
                .append("AND bm.lov_balance_type = :idLov ")
                .append("AND bm.id_ms_tenant = :idMsTenant ")
                .append("AND bm.id_ms_vendor = :idMsVendor ")
                .append("AND bm.id_balance_top_up IS NULL ")
                .append("AND bm.qty < 0 ")
                .append("ORDER BY bm.id_balance_mutation ASC ")
            .append(") ")
            .append("SELECT * FROM running_balance rb ")
            .append("WHERE rb.affected_running_qty <= :availableBalance ");

        return managerDAO.selectAllNativeString(query.toString(), params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateBalanceMutationIdBalanceTopup(List<Object> idBalanceMutations, long idBalanceToup, String usrUpd) {

        if (CollectionUtils.isEmpty(idBalanceMutations)) {
            return;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("idBalanceMutations", idBalanceMutations);
        params.put("idBalanceToup", idBalanceToup);
        params.put("usrUpd", usrUpd);

        StringBuilder query = new StringBuilder();
        query
            .append("UPDATE tr_balance_mutation ")
            .append("SET id_balance_top_up = :idBalanceToup , ")
            .append("usr_upd = :usrUpd , ")
            .append("dtm_upd = now() ")
            .append("WHERE id_balance_mutation IN :idBalanceMutations ");

        managerDAO.updateNativeString(query.toString(), params);
    }
 
}
