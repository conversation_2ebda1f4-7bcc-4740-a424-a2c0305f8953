package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.constants.enums.StampingErrorDetail;
import com.adins.constants.enums.StampingErrorLocation;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.CommonStampingLogic;
import com.adins.esignhubjob.businesslogic.api.FileAccessLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.EmateraiPajakkuLogic;
import com.adins.esignhubjob.model.custom.adins.SignLocationBean;
import com.adins.esignhubjob.model.custom.adins.StampingErrorDetailBean;
import com.adins.esignhubjob.model.custom.pajakku.AttachEmeteraiErrorDetail;
import com.adins.esignhubjob.model.custom.pajakku.PajakkuDocumentTypeBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrStampDuty;
import com.adins.esignhubjob.model.webservice.pajakku.EmeteraiPajakkuLoginRequestBean;
import com.adins.esignhubjob.model.webservice.pajakku.EmeteraiPajakkuLoginResponseBean;
import com.adins.esignhubjob.model.webservice.pajakku.GenerateEmeteraiPajakkuRequest;
import com.adins.esignhubjob.model.webservice.pajakku.GenerateEmeteraiPajakkuResponse;
import com.adins.esignhubjob.model.webservice.pajakku.StampingEmeteraiPajakkuResponse;
import com.adins.esignhubjob.model.webservice.pajakku.StampingOnPremEmeteraiPajakkuRequest;
import com.adins.esignhubjob.model.webservice.pajakku.UploadDocPajakkuResponseBean;
import com.adins.exceptions.EmeteraiOnPremException;
import com.adins.util.FtpClient;
import com.aliyun.fc.runtime.Context;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
@Transactional
public class GenericEmateraiPajakkuLogic extends BaseLogic implements EmateraiPajakkuLogic{

	@Autowired CommonStampingLogic commonStampingLogic;
	@Autowired FileAccessLogic fileAccessLogic;
	@Autowired AliyunOssCloudStorageLogic aliyunOssCloudStorageLogic;

	String unsignedFtpDir = "/UNSIGNED/";
	String signedFtpDir = "/SIGNED/";
	String stampFtpDir = "/STAMP/";

    @Override
    public PajakkuDocumentTypeBean getDocumentType(Context context) throws IOException {
       
		EmeteraiPajakkuLoginResponseBean token = this.getToken(context);
		
		Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.BEARER + token.getToken());
        Headers header = Headers.of(headerMap);

		String url = System.getenv(Constants.ENV_VAR_EMETERAI_DOCUMENT_TYPE_URL);
		
		Request okHttpRequest = new Request.Builder()
            .headers(header)
            .url(url)
            .build();

		OkHttpClient client = new OkHttpClient.Builder().build();
        Response okHttResponse = client.newCall(okHttpRequest).execute();
		String jsonResponse = okHttResponse.body().string();

		String responseCode = StringUtils.isBlank(okHttResponse.message()) ? String.valueOf(okHttResponse.code()) : okHttResponse.code() + " " + okHttResponse.message();
		context.getLogger().info(String.format("Get Document Type response code: %1$s, body: %2$s", responseCode, jsonResponse));
        
        return gson.fromJson(jsonResponse, PajakkuDocumentTypeBean.class);
    }

	@Override
	public EmeteraiPajakkuLoginResponseBean getToken(Context context) throws IOException {
		
		String url = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_LOGIN);
		String userPajakku = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_USER);
		String passwordPajakku = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_PASSWORD);

		Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		Headers header = Headers.of(headerMap);

		EmeteraiPajakkuLoginRequestBean request = new EmeteraiPajakkuLoginRequestBean();
		request.setUser(userPajakku);
		request.setPassword(passwordPajakku);

		String jsonRequest = gson.toJson(request);
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

		Request okHttpRequest = new Request.Builder()
            .headers(header)
            .url(url)
            .post(body)
            .build();

		OkHttpClient client = new OkHttpClient.Builder().build();
        Response okHttResponse = client.newCall(okHttpRequest).execute();
        String jsonResponse = okHttResponse.body().string();

		String responseCode = StringUtils.isBlank(okHttResponse.message()) ? String.valueOf(okHttResponse.code()) : okHttResponse.code() + " " + okHttResponse.message();
		context.getLogger().info(String.format("Generate Emeterai Pajakku Token response code: %1$s, body: %2$s", responseCode, jsonResponse));
		
		Gson gson = new Gson();
		return gson.fromJson(jsonResponse, EmeteraiPajakkuLoginResponseBean.class);
		
	}
    
	@Override
	public EmeteraiPajakkuLoginResponseBean loginPeruri(TrDocumentH documentH, long connectTimeout, long readTimeout, Context context) { 
        String jsonRequest = null;
		String jsonResponse = null;
		String url = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_LOGIN);
		
		try {
			String username = commonStampingLogic.getAccountUsername(documentH, context);
			String password = commonStampingLogic.getAccountPassword(documentH, context);
			
			if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
				context.getLogger().error(String.format("Kontrak %1$s, Peruri login credential is empty", documentH.getRefNumber()));
				
				StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
				errorDetailBean.setErrorLocation(StampingErrorLocation.LOGIN.toString());
				errorDetailBean.setErrorLocationDetail(StampingErrorDetail.VALIDATION.toString());
				errorDetailBean.setErrorMessage("Empty Login Credentials");
				errorDetailBean.setThrowMaxError(false);
				commonStampingLogic.incrementAgreementStampingErrorCountPajakku(documentH, null, errorDetailBean, context);
				
				EmeteraiPajakkuLoginResponseBean response = new EmeteraiPajakkuLoginResponseBean();
				response.setStatusCode(Constants.PERURI_ERROR_CODE);
				return response;
			}
			
			Map<String, String> headerMap = new HashMap<>();
			headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
			Headers header = Headers.of(headerMap);

			EmeteraiPajakkuLoginRequestBean request = new EmeteraiPajakkuLoginRequestBean();
			request.setUser(username);
			request.setPassword(password);

			jsonRequest = gson.toJson(request);
			RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

			Request okHttpRequest = new Request.Builder()
				.headers(header)
				.url(url)
				.post(body)
				.build();

			OkHttpClient client = new OkHttpClient.Builder().build();
			Response okHttResponse = client.newCall(okHttpRequest).execute();
			jsonResponse = okHttResponse.body().string();

			String responseCode = StringUtils.isBlank(okHttResponse.message()) ? String.valueOf(okHttResponse.code()) : okHttResponse.code() + " " + okHttResponse.message();
			context.getLogger().info(String.format("Generate Emeterai Pajakku Token response code: %1$s, body: %2$s", responseCode, jsonResponse));
			
			Gson gson = new Gson();
			
			EmeteraiPajakkuLoginResponseBean response = gson.fromJson(jsonResponse, EmeteraiPajakkuLoginResponseBean.class);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			
			if (!Constants.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {			
				StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
				errorDetailBean.setErrorLocation(StampingErrorLocation.LOGIN.toString());
				errorDetailBean.setErrorLocationDetail(StampingErrorDetail.FAIL_RESPONSE.toString());
				errorDetailBean.setErrorMessage(response.getErrorMessage());
				commonStampingLogic.incrementAgreementStampingErrorCountPajakku(documentH, null, errorDetailBean, context);
			}
			
			return response;
		} catch (Exception e) {
			
			context.getLogger().error(String.format("Kontrak %1$s, Login Peruri exception: %2$s", documentH.getRefNumber(), e.getLocalizedMessage(), e));
			StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
			errorDetailBean.setErrorLocation(StampingErrorLocation.LOGIN.toString());
			errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
			errorDetailBean.setException(e);
			commonStampingLogic.incrementAgreementStampingErrorCountPajakku(documentH, null, errorDetailBean, context);
			
			EmeteraiPajakkuLoginResponseBean response = new EmeteraiPajakkuLoginResponseBean();
			response.setStatusCode(Constants.PERURI_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			response.setErrorMessage(e.getLocalizedMessage());
			response.setException(e);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			return response;
		}
    }

	@Override
	public UploadDocPajakkuResponseBean uploadDocumentForStamping(TrDocumentD document, TrDocumentDStampduty docSdt, FtpClient ftpClient, Context context) {
		try {
			// Set stamping start time
			if (null == docSdt.getStartStampProcess()) {
				docSdt.setStartStampProcess(new Date());
				daoFactory.getDocumentDao().updateDocumentDStampdutyNewTrx(docSdt);
			}

			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Deleting Stamped document", document.getTrDocumentH().getRefNumber(), document.getDocumentId()));
			String backupFilename = document.getDocumentId() + ".pdf.bckp";
			String filename = document.getDocumentId() + ".pdf";
			String base64Document = commonStampingLogic.getDocumentFileToUploadPajakku(document, context);
			
			String method = System.getenv(Constants.ENV_VAR_UPL_DOC_METHOD);
			if (StringUtils.equalsIgnoreCase("NAS", method)) {
				// for NAS
				fileAccessLogic.deleteStampedDocument(document, context);
				byte[] fileContent = Base64.getDecoder().decode(base64Document);
				fileAccessLogic.storeBaseStampDocument(fileContent, document, context);
			} else {
				// for FTP
				ftpClient.deleteFile(signedFtpDir + backupFilename);
				ftpClient.deleteFile(signedFtpDir + filename);
				ftpClient.putFileToPath(base64Document, unsignedFtpDir + filename);
			}
			
			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, uploading unstamped document", document.getTrDocumentH().getRefNumber(), document.getDocumentId()));

			commonStampingLogic.updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_GEN_SDT, context);
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			response.setStatusCode(Constants.PERURI_SUCCESS_CODE);
			return response;
		} catch (Exception e) {
			context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, Upload document for stamping exception: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e));
			StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
			errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_DOC.toString());
			errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
			errorDetailBean.setException(e);
			commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			response.setStatusCode(Constants.PERURI_ERROR_CODE);
			response.setErrorMessage(response.getMessage());
			return response;
		}
	}

	@Override
	public GenerateEmeteraiPajakkuResponse generateEmeterai(TrDocumentD document, TrDocumentDStampduty docSdt, String token, long connectTimeout, long readTimeout, FtpClient ftpClient, Context context) {
		String jsonRequest = null;
		String jsonResponse = null;
		String url = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_GEN_SDT);

		try {
			
			// Validasi kebutuhan meterai
			int sdtNeeded = document.getTotalMaterai() - document.getTotalStamping();
			int availableSdt = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			context.getLogger().info(String.format("Kontrak %1$s, Document %2$s, Need SDT: %3$s, Available SDT: %4$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtNeeded, availableSdt));
			if (availableSdt == sdtNeeded) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_STM_SDT, context);
				
				GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
				response.setMessage("Stamp duty already available");
				response.setStatusCode(Constants.PERURI_SUCCESS_CODE);
				return response;
			}
			
			//Validasi jumlah saldo meterai                                                                                                
			boolean enoughSdt = commonStampingLogic.enoughSdtBalance(document, sdtNeeded, context);
			if (!enoughSdt) {
				String message = "Not enough balance";
				context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, Generate meterai validation: Not enough balance", document.getTrDocumentH().getRefNumber(), document.getDocumentId()));
				StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
				errorDetailBean.setErrorLocation(StampingErrorLocation.GEN_SDT.toString());
				errorDetailBean.setErrorLocationDetail(StampingErrorDetail.VALIDATION.toString());
				errorDetailBean.setErrorMessage(message);
				commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
				
				GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
				response.setMessage(message);
				response.setStatusCode(Constants.PERURI_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			// Prepare API request
			String nilaiMeteraiLunas = commonStampingLogic.getStampDutyFee(context);
			GenerateEmeteraiPajakkuRequest request = new GenerateEmeteraiPajakkuRequest();
			request.setUpload(false);
			request.setNamadoc(commonStampingLogic.getNamaDocForGenerate(document, context));
			request.setNamafile(document.getDocumentId() + ".pdf");
			request.setNilaidoc(nilaiMeteraiLunas);
			request.setSnOnly(false);
			request.setNodoc(commonStampingLogic.getNoDocForGenerate(document, context));
			request.setTgldoc(commonStampingLogic.getTglDocForGenerate(document, context));
			
			if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && "1".equals(document.getTrDocumentH().getIsPostpaidStampduty())  && StringUtils.isNotBlank(document.getDocumentName())) {
				request.setNamejidentitas(document.getMsLovIdType().getCode());
				request.setNoidentitas(document.getIdNo());
				request.setNamedipungut(document.getIdName());
			}
			
			jsonRequest = gson.toJson(request);
			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Generate meterai request: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest));
			
			// Prepare API Header
			Map<String, String> headerMap = new HashMap<>();
			headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.BEARER + token);
			headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
			Headers header = Headers.of(headerMap);

			jsonRequest = gson.toJson(request);
			RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

			Request okHttpRequest = new Request.Builder()
				.headers(header)
				.url(url)
				.post(body)
				.build();

			OkHttpClient client = new OkHttpClient.Builder()
									.connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
									.readTimeout(readTimeout, TimeUnit.MILLISECONDS)
									.build();

			Response okHttResponse = client.newCall(okHttpRequest).execute();
			jsonResponse = okHttResponse.body().string();

			String responseCode = StringUtils.isBlank(okHttResponse.message()) ? String.valueOf(okHttResponse.code()) : okHttResponse.code() + " " + okHttResponse.message();
			
			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Generate meterai response: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonResponse));
			GenerateEmeteraiPajakkuResponse generateResponse = gson.fromJson(jsonResponse, GenerateEmeteraiPajakkuResponse.class);
			if (!Constants.PERURI_SUCCESS_CODE.equals(generateResponse.getStatusCode())) {
				StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
				errorDetailBean.setErrorLocation(StampingErrorLocation.GEN_SDT.toString());
				errorDetailBean.setErrorLocationDetail(StampingErrorDetail.FAIL_RESPONSE.toString());
				errorDetailBean.setErrorMessage(generateResponse.getErrorMessage());
				commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
				return generateResponse;
			}
			
			String method = System.getenv(Constants.ENV_VAR_UPL_DOC_METHOD);
			if (StringUtils.equalsIgnoreCase("NAS", method)) { 
				// Save QR to local file for NAS
				byte[] qrContent = Base64.getDecoder().decode(generateResponse.getResult().getImage());
				fileAccessLogic.storeStampQr(qrContent, generateResponse.getResult().getSn(), context);
			} else {
				// for FTP
				String filename = generateResponse.getResult().getSn() + ".png";
				ftpClient.putFileToPath(generateResponse.getResult().getImage(), stampFtpDir + filename);
			}

			MsTenant tenant = document.getMsTenant();
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);
			
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsLov sdtStatus = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_STAMP_DUTY_STATUS, Constants.LOV_CODE_STAMP_DUTY_STATUS_AVAILABLE);
			MsLov balanceType = null;
			MsLov trxType = null;
			if ("1".equals(document.getTrDocumentH().getIsPostpaidStampduty())) {
				balanceType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SDT_POSTPAID);
				trxType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USDT_POSTPAID);
			} else {
				balanceType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SDT);
				trxType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USDT);
			}
			
			TrStampDuty sdt = new TrStampDuty();
			sdt.setTrxNo(trxNo);
			sdt.setStampDutyNo(generateResponse.getResult().getSn());
			sdt.setStampQr(generateResponse.getResult().getSn() + ".png");
			sdt.setMsLov(sdtStatus);
			sdt.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
			sdt.setDtmCrt(new Date());
			sdt.setMsTenant(tenant);
			sdt.setMsVendor(vendor);
			sdt.setStampDutyFee(Integer.valueOf(nilaiMeteraiLunas));
			daoFactory.getStampDutyDao().insertStampDuty(sdt);
			context.getLogger().info(String.format("Kontrak %1$s, stamp duty with SN %2$s inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo()));
			
			docSdt.setTrStampDuty(sdt);
			docSdt.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
			docSdt.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDStampdutyNewTrx(docSdt);
			
			String notes = commonStampingLogic.getBalanceMutationNotesForGenerate(docSdt, context);
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(trxNo);
			mutation.setTrxDate(new Date());
			mutation.setRefNo(document.getTrDocumentH().getRefNumber());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setTrDocumentD(document);
			mutation.setTrDocumentH(document.getTrDocumentH());
			mutation.setNotes(notes);
			mutation.setTrStampDuty(sdt);
			mutation.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
			mutation.setDtmCrt(new Date());
			mutation.setMsOffice(document.getTrDocumentH().getMsOffice());
			mutation.setMsBusinessLine(document.getTrDocumentH().getMsBusinessLine());
			daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
			context.getLogger().info(String.format("Kontrak %1$s, balance mutation with stamp duty SN %2$s inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo()));
			
			commonStampingLogic.updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_STM_SDT, context);
			return generateResponse;
		} catch (Exception e) {
			context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, Generate meterai exception: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e));
			StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
			errorDetailBean.setErrorLocation(StampingErrorLocation.GEN_SDT.toString());
			errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
			errorDetailBean.setException(e);
			commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
			
			GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
			response.setMessage(e.getLocalizedMessage());
			response.setStatusCode(Constants.PERURI_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			return response;
			
		}
	}

	@Override
	public StampingEmeteraiPajakkuResponse stampEmeterai(TrDocumentD document, TrDocumentDStampduty docSdt, String token, long connectTimeout, long readTimeout, FtpClient ftpClient, Context context) {
		String jsonRequest = null;
		String jsonResponse = null;
		String certifLevel = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_CERT_LVL);
		String profileName = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_PROFILE_NAME);
		String url = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_STM_SDT_URL);

		try {
			int sdtAvailable = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, available SDT qty: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtAvailable));
			if (0 == sdtAvailable) {
				String message = "No stamp duty for " + document.getDocumentId();
				StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
				errorDetailBean.setErrorLocation(StampingErrorLocation.STM_SDT.toString());
				errorDetailBean.setErrorLocationDetail(StampingErrorDetail.VALIDATION.toString());
				errorDetailBean.setErrorMessage(message);
				commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);	
				
				StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
				response.setErrorCode(Constants.PERURI_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			TrStampDuty sdt = docSdt.getTrStampDuty();
			if (Constants.LOV_CODE_STAMP_DUTY_STATUS_GO_LIVE.equals(sdt.getMsLov().getCode())) {
				String message = "SDT with number " + sdt.getStampDutyNo() + " cannot be used.";
				StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
				errorDetailBean.setErrorLocation(StampingErrorLocation.STM_SDT.toString());
				errorDetailBean.setErrorLocationDetail(StampingErrorDetail.VALIDATION.toString());
				errorDetailBean.setErrorMessage(message);
				commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
				
				StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
				response.setErrorCode(Constants.PERURI_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			SignLocationBean coordinate = gson.fromJson(docSdt.getSignLocation(), SignLocationBean.class);
			
			StampingOnPremEmeteraiPajakkuRequest request = new StampingOnPremEmeteraiPajakkuRequest();
			request.setCertificatelevel(certifLevel);
			request.setDest(commonStampingLogic.getOnPremStampDestination(document, context));
			request.setDocpass(StringUtils.EMPTY);
			request.setJwToken(token);
			request.setLocation(document.getTrDocumentH().getMsOffice().getOfficeName());
			request.setProfileName(profileName);
			request.setReason(commonStampingLogic.getReasonForStamp(document, context));
			request.setRefToken(sdt.getStampDutyNo());
			request.setSpesimenPath(commonStampingLogic.getOnPremSpecimenPath(docSdt, context));
			request.setSrc(commonStampingLogic.getOnPremSource(document, context));
			request.setRetryFlag("1");
			request.setVisLLX(Double.valueOf(coordinate.getLlx()));
			request.setVisLLY(Double.valueOf(coordinate.getLly()));
			request.setVisURX(Double.valueOf(coordinate.getUrx()));
			request.setVisURY(Double.valueOf(coordinate.getUry()));
			request.setVisSignaturePage(docSdt.getSignPage());
			
			// Prepare API Header
			Map<String, String> headerMap = new HashMap<>();
			headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.BEARER + token);
			headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
			Headers header = Headers.of(headerMap);
			
			// Prepare API request body
			jsonRequest = gson.toJson(request);
			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Stamping meterai request: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest));
			RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

			Request okHttpRequest = new Request.Builder()
				.headers(header)
				.url(url)
				.post(body)
				.build();

			OkHttpClient client = new OkHttpClient.Builder()
									.connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
									.readTimeout(readTimeout, TimeUnit.MILLISECONDS)
									.build();

			// Get API response
			Response okHttResponse = client.newCall(okHttpRequest).execute();
			jsonResponse = okHttResponse.body().string();
			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Stamping meterai response: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonResponse));
			StampingEmeteraiPajakkuResponse stampingResponse = gson.fromJson(jsonResponse, StampingEmeteraiPajakkuResponse.class);
			
			if (!Constants.PERURI_SUCCESS_CODE.equals(stampingResponse.getErrorCode())) {
				StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
				errorDetailBean.setErrorLocation(StampingErrorLocation.STM_SDT.toString());
				errorDetailBean.setErrorLocationDetail(StampingErrorDetail.FAIL_RESPONSE.toString());
				errorDetailBean.setErrorMessage(stampingResponse.getErrorMessage());
				commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
				return stampingResponse;
			}
			
			// Update SDT Status to GO LIVE
			MsLov sdtStatus = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_STAMP_DUTY_STATUS, Constants.LOV_CODE_STAMP_DUTY_STATUS_GO_LIVE);
			sdt.setMsLov(sdtStatus);
			sdt.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
			sdt.setDtmUpd(new Date());
			daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
			
			// Update tr_document_d_stampduty, fill id_stamp_duty and stamp date
			docSdt.setTrStampDuty(sdt);
			docSdt.setStampingDate(new Date());
			docSdt.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
			docSdt.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDStampdutyNewTrx(docSdt);
			
			commonStampingLogic.updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_UPL_OSS, context);
			return stampingResponse;
		} catch (Exception e) {
			context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, Stamping meterai exception: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e));
			StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
			errorDetailBean.setErrorLocation(StampingErrorLocation.STM_SDT.toString());
			errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
			errorDetailBean.setException(e);
			commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
			
			StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
			response.setErrorCode(Constants.PERURI_ERROR_CODE);
			response.setErrorMessage(e.getLocalizedMessage());
			response.setErrorMsg(e.getLocalizedMessage());
			return response;
		}
	}

	@Override
	public AttachEmeteraiErrorDetail storeStampedDocumentToOss(TrDocumentD document, FtpClient ftpClient, Context context) {
		try {
			boolean isExists = false;
			int attempCount = commonStampingLogic.getFileCheckAttempts(context);
			long delayMs = commonStampingLogic.getFileCheckDelay(context);
		
			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Upload to OSS", document.getTrDocumentH().getRefNumber(), document.getDocumentId()));
			while (!isExists) {
				if (attempCount <= 0) {
					context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, Max check count reached", document.getTrDocumentH().getRefNumber(), document.getDocumentId()));
					throw new EmeteraiOnPremException("No stamp result document yet");
				}
				
				Thread.sleep(delayMs);
				context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, %3$s attempts left to check stamped document", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), attempCount));
				String filename = document.getDocumentId() + ".pdf";
				isExists = false;
				
				String method = System.getenv(Constants.ENV_VAR_UPL_DOC_METHOD);
				if (StringUtils.equalsIgnoreCase("NAS", method)) {
					isExists = fileAccessLogic.isStampedDocumentExists(document, context);
				} else {
					isExists = ftpClient.isFileExist(signedFtpDir + filename);
				}

				attempCount -= 1;
			}
			
			String filename = document.getDocumentId() + ".pdf";
			byte[] documentByteArray;

			String method = System.getenv(Constants.ENV_VAR_UPL_DOC_METHOD);
			if (StringUtils.equalsIgnoreCase("NAS", method)) {
				documentByteArray = fileAccessLogic.getStampedDocument(document, context);
			} else {
				documentByteArray = Base64.getDecoder().decode(ftpClient.downloadFile(signedFtpDir + filename));
			} 

			if (null == documentByteArray) {
				throw new EmeteraiOnPremException("No stamp result document yet");
			}
			
			aliyunOssCloudStorageLogic.storeStampedDocument(document, documentByteArray, context);
			
			// Update documentD
			short stamped = document.getTotalStamping();
			stamped += 1;
			document.setTotalStamping(stamped);
			daoFactory.getDocumentDao().updateDocumentDNewTran(document);
						
			if (document.getTotalMaterai().equals(document.getTotalStamping())) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_UPL_CON, context);
			} else {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_UPL_DOC, context);
			}
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(Constants.PERURI_SUCCESS_CODE);
			return response;
		} catch (InterruptedException e) {
			
			context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, Upload to OSS exception: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e));
			context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, interrupting current thread", document.getTrDocumentH().getRefNumber(), document.getDocumentId()));
			
			Thread.currentThread().interrupt();

			StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
			errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_OSS.toString());
			errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
			errorDetailBean.setException(e);
			commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(Constants.PERURI_ERROR_CODE);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
		catch (Exception e) {
			context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, Upload to OSS exception: %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e));
			
			StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
			errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_OSS.toString());
			errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
			errorDetailBean.setException(e);
			commonStampingLogic.incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(Constants.PERURI_ERROR_CODE);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
		
	}
}
