package com.adins.esignhubjob.model.webservice.jatis;

import java.util.List;

import com.google.gson.annotations.SerializedName;

public class JatisWhatsAppWebhookEntryChangeValue {
    @SerializedName("ban_info") private Object banInfo;
    private Object contacts;
    @SerializedName("current_limit") private String currentLimit;
    private String decision;
    @SerializedName("disable_date") private String disableDate;
    @SerializedName("disable_info") private Object disableInfo;
    @SerializedName("display_phone_number") private String displayPhoneNumber;
    private Object errors;
    private String event;
    @SerializedName("max_daily_conversation_per_phone") private Integer maxDailyConversationPerPhone;
    @SerializedName("max_phone_number_per_business") private Integer maxPhoneNumberPerBusiness;
    @SerializedName("message_template_id") private Integer messageTemplateId;
    @SerializedName("message_template_language") private String messageTemplateLanguage;
    @SerializedName("message_template_name") private String messageTemplateName;
    private Object messages;
    @SerializedName("messaging_product") private String messagingProduct;
    private Object metadata;
    @SerializedName("old_limit") private String oldLimit;
    @SerializedName("phone_number") private String phoneNumber;
    private String reason;
    @SerializedName("rejection_reason") private String rejectionReason;
    @SerializedName("requested_verified_name") private String requestedVerifiedName;
    private List<JatisWhatsAppWebhookEntryChangeStatus> statuses;

    public Object getBanInfo() {
        return this.banInfo;
    }

    public void setBanInfo(Object banInfo) {
        this.banInfo = banInfo;
    }

    public Object getContacts() {
        return this.contacts;
    }

    public void setContacts(Object contacts) {
        this.contacts = contacts;
    }

    public String getCurrentLimit() {
        return this.currentLimit;
    }

    public void setCurrentLimit(String currentLimit) {
        this.currentLimit = currentLimit;
    }

    public String getDecision() {
        return this.decision;
    }

    public void setDecision(String decision) {
        this.decision = decision;
    }

    public String getDisableDate() {
        return this.disableDate;
    }

    public void setDisableDate(String disableDate) {
        this.disableDate = disableDate;
    }

    public Object getDisableInfo() {
        return this.disableInfo;
    }

    public void setDisableInfo(Object disableInfo) {
        this.disableInfo = disableInfo;
    }

    public String getDisplayPhoneNumber() {
        return this.displayPhoneNumber;
    }

    public void setDisplayPhoneNumber(String displayPhoneNumber) {
        this.displayPhoneNumber = displayPhoneNumber;
    }

    public Object getErrors() {
        return this.errors;
    }

    public void setErrors(Object errors) {
        this.errors = errors;
    }

    public String getEvent() {
        return this.event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public Integer getMaxDailyConversationPerPhone() {
        return this.maxDailyConversationPerPhone;
    }

    public void setMaxDailyConversationPerPhone(Integer maxDailyConversationPerPhone) {
        this.maxDailyConversationPerPhone = maxDailyConversationPerPhone;
    }

    public Integer getMaxPhoneNumberPerBusiness() {
        return this.maxPhoneNumberPerBusiness;
    }

    public void setMaxPhoneNumberPerBusiness(Integer maxPhoneNumberPerBusiness) {
        this.maxPhoneNumberPerBusiness = maxPhoneNumberPerBusiness;
    }

    public Integer getMessageTemplateId() {
        return this.messageTemplateId;
    }

    public void setMessageTemplateId(Integer messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    public String getMessageTemplateLanguage() {
        return this.messageTemplateLanguage;
    }

    public void setMessageTemplateLanguage(String messageTemplateLanguage) {
        this.messageTemplateLanguage = messageTemplateLanguage;
    }

    public String getMessageTemplateName() {
        return this.messageTemplateName;
    }

    public void setMessageTemplateName(String messageTemplateName) {
        this.messageTemplateName = messageTemplateName;
    }

    public Object getMessages() {
        return this.messages;
    }

    public void setMessages(Object messages) {
        this.messages = messages;
    }

    public String getMessagingProduct() {
        return this.messagingProduct;
    }

    public void setMessagingProduct(String messagingProduct) {
        this.messagingProduct = messagingProduct;
    }

    public Object getMetadata() {
        return this.metadata;
    }

    public void setMetadata(Object metadata) {
        this.metadata = metadata;
    }

    public String getOldLimit() {
        return this.oldLimit;
    }

    public void setOldLimit(String oldLimit) {
        this.oldLimit = oldLimit;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getReason() {
        return this.reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRejectionReason() {
        return this.rejectionReason;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }

    public String getRequestedVerifiedName() {
        return this.requestedVerifiedName;
    }

    public void setRequestedVerifiedName(String requestedVerifiedName) {
        this.requestedVerifiedName = requestedVerifiedName;
    }

    public List<JatisWhatsAppWebhookEntryChangeStatus> getStatuses() {
        return this.statuses;
    }

    public void setStatuses(List<JatisWhatsAppWebhookEntryChangeStatus> statuses) {
        this.statuses = statuses;
    }

}
