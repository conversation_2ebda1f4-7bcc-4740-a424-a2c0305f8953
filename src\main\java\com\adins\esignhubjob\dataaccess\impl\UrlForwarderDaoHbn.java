package com.adins.esignhubjob.dataaccess.impl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.UrlForwarderDao;
import com.adins.esignhubjob.model.table.TrUrlForwarder;

@Component
@Transactional
public class UrlForwarderDaoHbn extends BaseDaoHbn implements UrlForwarderDao {

	 @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteUrlForwadrer(TrUrlForwarder trUrlForwarder) {
        managerDAO.delete(trUrlForwarder);
    }

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrUrlForwarder getUrlForwarderByUrlCodeNewTrx(String urlCode) {
		return this.managerDAO.selectOne(
				"from TrUrlForwarder tuf "
				+ "where tuf.urlCode = :urlCode ", 
						new Object[][] {{"urlCode", urlCode}});
	}

	@Override
	public TrUrlForwarder getUrlForwarderByUrlCode(String urlCode) {
		return this.managerDAO.selectOne(
				"from TrUrlForwarder tuf "
				+ "where tuf.urlCode = :urlCode ", 
						new Object[][] {{"urlCode", urlCode}});
	}


    @Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<String> getExpiredUrlForwarder() {
		StringBuilder query = new StringBuilder();
		List<String> result = new ArrayList<>();
		query.append(" select url_code from tr_url_forwarder tuf ")
			.append(" join am_generalsetting gs on  gs.gs_code = 'URL_FORWARDER_EXPIRED_LIMIT' ")
			.append(" where datediff('day',CAST (tuf.dtm_crt as Date),CAST (now() as Date) )  >= cast( gs.gs_value as INTEGER)  ");
					
			
		Object[][] queryParams = { };
		List<Map<String,Object>> resultQuery = this.managerDAO.selectAllNativeString(query.toString(),queryParams);
		Iterator<Map<String, Object>> itr = resultQuery.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			result.add((String) map.get("d0"));
		}

		return result;
	}


}
