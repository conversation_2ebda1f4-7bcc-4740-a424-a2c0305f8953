package com.adins.esignhubjob.dataaccess.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.OfficeDao;
import com.adins.esignhubjob.model.table.MsOffice;
import com.adins.esignhubjob.model.table.MsTenant;

@Component
@Transactional
public class OfficeDaoHbn extends BaseDaoHbn implements OfficeDao {

    @Override
    public MsOffice getFirstOfficeByTenantCode(String tenantCode) {
        StringBuilder query = new StringBuilder();
		query
			.append(" select office_code from ms_office mo ")
			.append(" join ms_tenant mt on mo.id_ms_tenant = mt.id_ms_tenant ")
			.append(" where tenant_code = :tenantCode order by mo.dtm_crt limit 1");
		
		String  officeCode = (String) this.managerDAO.selectOneNativeString(query.toString(),
				new Object[][] {{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) }});
		
		if (StringUtils.isBlank(officeCode)) {
			return null;
		}

        Object[][] queryParams = {
            {MsOffice.OFFICE_CODE_HBM, StringUtils.upperCase(officeCode)},
            {"tenantCode", StringUtils.upperCase(tenantCode)}
        };

        return this.managerDAO.selectOne(
				"from MsOffice mo "
				+ "join fetch mo.msTenant mt "
				+ "where mo.officeCode = :officeCode and mt.tenantCode = :tenantCode and mo.isActive ='1' ",
				queryParams);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MsOffice getFirstOfficeByTenantCodeNewTran(String tenantCode) {
        StringBuilder query = new StringBuilder();
		query
			.append(" select office_code from ms_office mo ")
			.append(" join ms_tenant mt on mo.id_ms_tenant = mt.id_ms_tenant ")
			.append(" where tenant_code = :tenantCode order by mo.dtm_crt limit 1");
		
		String  officeCode = (String) this.managerDAO.selectOneNativeString(query.toString(),
				new Object[][] {{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) }});
		
		if (StringUtils.isBlank(officeCode)) {
			return null;
		}

        Object[][] queryParams = {
            {MsOffice.OFFICE_CODE_HBM, StringUtils.upperCase(officeCode)},
            {"tenantCode", StringUtils.upperCase(tenantCode)}
        };

        return this.managerDAO.selectOne(
				"from MsOffice mo "
				+ "join fetch mo.msTenant mt "
				+ "where mo.officeCode = :officeCode and mt.tenantCode = :tenantCode and mo.isActive ='1' ",
				queryParams);
    }
    
}
