package com.adins.esignhubjob.model.custom.adins;

import com.adins.constants.enums.StampingDocumentType;

public class StampingErrorDetailBean {

    private String errorLocation;
    private String errorLocationDetail;
    private StampingDocumentType documentType;
    private String errorMessage;
    private Exception exception;
    private String jsonRequest;
    private String jsonResponse;
    private boolean throwMaxError;

    public String getErrorLocation() {
        return this.errorLocation;
    }

    public void setErrorLocation(String errorLocation) {
        this.errorLocation = errorLocation;
    }

    public String getErrorLocationDetail() {
        return this.errorLocationDetail;
    }

    public void setErrorLocationDetail(String errorLocationDetail) {
        this.errorLocationDetail = errorLocationDetail;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Exception getException() {
        return this.exception;
    }

    public void setException(Exception exception) {
        this.exception = exception;
    }

    public String getJsonRequest() {
        return this.jsonRequest;
    }

    public void setJsonRequest(String jsonRequest) {
        this.jsonRequest = jsonRequest;
    }

    public String getJsonResponse() {
        return this.jsonResponse;
    }

    public void setJsonResponse(String jsonResponse) {
        this.jsonResponse = jsonResponse;
    }

    public StampingDocumentType getDocumentType() {
        return documentType;
    }

    public void setDocumentType(StampingDocumentType documentType) {
        this.documentType = documentType;
    }

    public boolean isThrowMaxError() {
        return throwMaxError;
    }

    public void setThrowMaxError(boolean throwMaxError) {
        this.throwMaxError = throwMaxError;
    }
    
}
