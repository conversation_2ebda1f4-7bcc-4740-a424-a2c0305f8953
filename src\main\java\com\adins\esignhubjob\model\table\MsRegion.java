package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_region")
public class MsRegion extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	
	public static final String TENANT_HBM = "msTenant";
	public static final String REGION_CODE_HBM = "regionCode";

	private long idMsRegion;
	private MsTenant msTenant;
	private String regionName;
	private String regionCode;
	private Set<MsOffice> msOffices = new HashSet<>(0);

	public MsRegion() {
	}

	public MsRegion(long idMsRegion, String usrCrt, Date dtmCrt) {
		this.idMsRegion = idMsRegion;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}

	public MsRegion(long idMsRegion, MsTenant msTenant, String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd,
			String regionName, String regionCode, Set<MsOffice> msOffices) {
		this.idMsRegion = idMsRegion;
		this.msTenant = msTenant;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
		this.regionName = regionName;
		this.regionCode = regionCode;
		this.msOffices = msOffices;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_region", unique = true, nullable = false)
	public long getIdMsRegion() {
		return this.idMsRegion;
	}

	public void setIdMsRegion(long idMsRegion) {
		this.idMsRegion = idMsRegion;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "region_name", length = 50)
	public String getRegionName() {
		return this.regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	@Column(name = "region_code", length = 20)
	public String getRegionCode() {
		return this.regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msRegion")
	public Set<MsOffice> getMsOffices() {
		return this.msOffices;
	}

	public void setMsOffices(Set<MsOffice> msOffices) {
		this.msOffices = msOffices;
	}
}
