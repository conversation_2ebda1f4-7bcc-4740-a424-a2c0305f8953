package com.adins.esignhubjob.model.custom.privygeneral;

import java.util.List;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralSignerBean {
    private Long id;
    @SerializedName("privy_id") private String privyId;
    private String name;
    @SerializedName("signer_type") private String signerType;
    private String status;
    private List<PrivyGeneralSignerHistoryBean> histories;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPrivyId() {
        return this.privyId;
    }

    public void setPrivyId(String privyId) {
        this.privyId = privyId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSignerType() {
        return this.signerType;
    }

    public void setSignerType(String signerType) {
        this.signerType = signerType;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<PrivyGeneralSignerHistoryBean> getHistories() {
        return this.histories;
    }

    public void setHistories(List<PrivyGeneralSignerHistoryBean> histories) {
        this.histories = histories;
    }

}
