package com.adins.esignhubjob.model.webservice.pajakku;

public class StampingOnPremEmeteraiPajakkuRequest  {
    private String docId;
	private String certificatelevel;
	private String dest;
	private String docpass;
	private String jwToken;
	private String location;
	private String profileName;
	private String reason;
	private String refToken;
	private String spesimenPath;
	private String src;
	private String retryFlag;
	private Double visLLX;
	private Double visLLY;
	private Double visURX;
	private Double visURY;
	private int visSignaturePage;
    public String getDocId() {
        return docId;
    }
    public void setDocId(String docId) {
        this.docId = docId;
    }
    public String getCertificatelevel() {
        return certificatelevel;
    }
    public void setCertificatelevel(String certificatelevel) {
        this.certificatelevel = certificatelevel;
    }
    public String getDest() {
        return dest;
    }
    public void setDest(String dest) {
        this.dest = dest;
    }
    public String getDocpass() {
        return docpass;
    }
    public void setDocpass(String docpass) {
        this.docpass = docpass;
    }
    public String getJwToken() {
        return jwToken;
    }
    public void setJwToken(String jwToken) {
        this.jwToken = jwToken;
    }
    public String getLocation() {
        return location;
    }
    public void setLocation(String location) {
        this.location = location;
    }
    public String getProfileName() {
        return profileName;
    }
    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }
    public String getReason() {
        return reason;
    }
    public void setReason(String reason) {
        this.reason = reason;
    }
    public String getRefToken() {
        return refToken;
    }
    public void setRefToken(String refToken) {
        this.refToken = refToken;
    }
    public String getSpesimenPath() {
        return spesimenPath;
    }
    public void setSpesimenPath(String spesimenPath) {
        this.spesimenPath = spesimenPath;
    }
    public String getSrc() {
        return src;
    }
    public void setSrc(String src) {
        this.src = src;
    }
    public String getRetryFlag() {
        return retryFlag;
    }
    public void setRetryFlag(String retryFlag) {
        this.retryFlag = retryFlag;
    }
    public Double getVisLLX() {
        return visLLX;
    }
    public void setVisLLX(Double visLLX) {
        this.visLLX = visLLX;
    }
    public Double getVisLLY() {
        return visLLY;
    }
    public void setVisLLY(Double visLLY) {
        this.visLLY = visLLY;
    }
    public Double getVisURX() {
        return visURX;
    }
    public void setVisURX(Double visURX) {
        this.visURX = visURX;
    }
    public Double getVisURY() {
        return visURY;
    }
    public void setVisURY(Double visURY) {
        this.visURY = visURY;
    }
    public int getVisSignaturePage() {
        return visSignaturePage;
    }
    public void setVisSignaturePage(int visSignaturePage) {
        this.visSignaturePage = visSignaturePage;
    }
}
