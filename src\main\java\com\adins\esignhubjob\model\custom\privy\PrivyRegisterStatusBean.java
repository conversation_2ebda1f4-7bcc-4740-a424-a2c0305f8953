package com.adins.esignhubjob.model.custom.privy;

import com.google.gson.annotations.SerializedName;

public class PrivyRegisterStatusBean {
	@SerializedName("request_id") private String requestId;
	@SerializedName("privy_id") private String privyId;
	private String email;
	private String phone;
	@SerializedName("processed_at") private String processedAt;
	@SerializedName("user_token") private String userToken;
	private PrivyIdentityBean identity;
	private PrivyVerificationBean verification;
	@SerializedName("verification_status") private String verificationStatus;
	@SerializedName("registration_status") private String registrationStatus;
	@SerializedName("reject_code") private String rejectCode;
	
	public String getRequestId() {
		return requestId;
	}
	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}
	public String getPrivyId() {
		return privyId;
	}
	public void setPrivyId(String privyId) {
		this.privyId = privyId;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getProcessedAt() {
		return processedAt;
	}
	public void setProcessedAt(String processedAt) {
		this.processedAt = processedAt;
	}
	public String getUserToken() {
		return userToken;
	}
	public void setUserToken(String userToken) {
		this.userToken = userToken;
	}
	public PrivyIdentityBean getIdentity() {
		return identity;
	}
	public void setIdentity(PrivyIdentityBean identity) {
		this.identity = identity;
	}
	public PrivyVerificationBean getVerification() {
		return verification;
	}
	public void setVerification(PrivyVerificationBean verification) {
		this.verification = verification;
	}
	public String getVerificationStatus() {
		return verificationStatus;
	}
	public void setVerificationStatus(String verificationStatus) {
		this.verificationStatus = verificationStatus;
	}
	public String getRegistrationStatus() {
		return registrationStatus;
	}
	public void setRegistrationStatus(String registrationStatus) {
		this.registrationStatus = registrationStatus;
	}
	public String getRejectCode() {
		return rejectCode;
	}
	public void setRejectCode(String rejectCode) {
		this.rejectCode = rejectCode;
	}
	
}