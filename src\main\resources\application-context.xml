<?xml version="1.0" encoding="UTF-8"?>
<beans
	xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd        
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

	<context:property-placeholder system-properties-mode="ENVIRONMENT"/>

	<context:annotation-config/>
	<context:component-scan base-package="com.adins.esignhubjob"/>
	<tx:annotation-driven/>

	<bean id="hikariConfig" class="com.zaxxer.hikari.HikariConfig">
		<property name="jdbcUrl" value="${DATASOURCE_URL}" />
		<property name="username" value="${DATASOURCE_USER}" />
		<property name="password" value="${DATASOURCE_PASSWORD}" />
	    <property name="minimumIdle" value="1" />
	    <property name="connectionTestQuery" value="SELECT 1" />
	</bean>

	<bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
	    <constructor-arg ref="hikariConfig" />
	</bean>

	<bean id="hibernateSessionFactoryBean" class="org.springframework.orm.hibernate5.LocalSessionFactoryBean">
		<property name="dataSource" ref="dataSource"/>
        <property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.PostgreSQLDialect</prop>
                <prop key="hibernate.show_sql">false</prop>
                <prop key="hibernate.format_sql">false</prop>
                <prop key="hibernate.jdbc.use_streams_for_binary">true</prop>
            </props>
        </property>
		<property name="packagesToScan">
			<list>
				<value>com.adins.esignhubjob.model.table</value>
			</list>
		</property>
    </bean>

	<bean id="transactionManager" class="org.springframework.orm.hibernate5.HibernateTransactionManager">
	    <property name="sessionFactory" ref="hibernateSessionFactoryBean" />
	</bean>
	
    <bean id="globalManagerDAO" class="com.adins.framework.persistence.dao.hibernate.manager.global.GlobalManagerDao">
		<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    </bean>

	<bean id="gson" class="com.google.gson.Gson"/>
</beans>