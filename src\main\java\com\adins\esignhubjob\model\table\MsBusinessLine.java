package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_business_line")
public class MsBusinessLine extends CreatableAndUpdatableEntity implements Serializable{
    private static final long serialVersionUID = 1L;

	public static final String TENANT_HBM = "msTenant";
	public static final String BUSINESSLINE_CODE_HBM = "businessLineCode";

	private long idMsBusinessLine;
	private MsTenant msTenant;
	private String businessLineCode;
	private String businessLineName;

	private Set<TrDocumentH> trDocumentHs = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_business_line", unique = true, nullable = false)
	public long getIdMsBusinessLine() {
		return this.idMsBusinessLine;
	}

	public void setIdMsBusinessLine(long idMsBusinessLine) {
		this.idMsBusinessLine = idMsBusinessLine;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "business_line_code", nullable = false, length = 20)
	public String getBusinessLineCode() {
		return this.businessLineCode;
	}

	public void setBusinessLineCode(String businessLineCode) {
		this.businessLineCode = businessLineCode;
	}

	@Column(name = "business_line_name", length = 50)
	public String getBusinessLineName() {
		return this.businessLineName;
	}

	public void setBusinessLineName(String businessLineName) {
		this.businessLineName = businessLineName;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msBusinessLine")
	public Set<TrDocumentH> getTrDocumentHs() {
		return this.trDocumentHs;
	}

	public void setTrDocumentHs(Set<TrDocumentH> trDocumentHs) {
		this.trDocumentHs = trDocumentHs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msBusinessLine")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}
}
