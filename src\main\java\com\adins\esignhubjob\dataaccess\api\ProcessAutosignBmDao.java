package com.adins.esignhubjob.dataaccess.api;

import java.util.List;

import com.adins.esignhubjob.model.table.TrProcessAutosignBmD;
import com.adins.esignhubjob.model.table.TrProcessAutosignBmH;

public interface ProcessAutosignBmDao {
    // tr_process_autosign_bm_h
    TrProcessAutosignBmH getProcessAutosignBmH(long idProcessAutosignBmH);
    TrProcessAutosignBmH getProcessAutosignBmHNewTrx(long idProcessAutosignBmH);

    List<TrProcessAutosignBmH> getProcessAutosignBmHs(String status, String executeType);

    void updateProcessAutosignBmH(TrProcessAutosignBmH processAutosignBmH);
    void updateProcessAutosignBmHNewTrx(TrProcessAutosignBmH processAutosignBmH);

    // tr_process_autosign_bm_d
    void insertProcessAutosignBmD(TrProcessAutosignBmD processAutosignBmD);
    void insertProcessAutosignBmDNewTrx(TrProcessAutosignBmD processAutosignBmD);
}
