package com.adins.esignhubjob.businesslogic.api.interfacing;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.aliyun.fc.runtime.Context;

public interface SchedulerLogic {
    	void dailyRecap(AuditContext auditContext, Context context);
		void deleteUrlForwarderExpired( AuditContext audit, Context context);
		void dailyRecapUpdate(String dateRecapString,AuditContext audit, Context context);
		

}
