package com.adins.esignhubjob.model.table.custom;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@MappedSuperclass
public abstract class TraceableEntity {
    protected Date activityDate;
    protected String activity;
    protected String terminalAddress;
    protected String terminalId;
    protected String terminalUserId;

    @Temporal(TemporalType.TIMESTAMP)
	@Column(name = "activity_date", length = 29)
    public Date getActivityDate() {
        return this.activityDate;
    }

    public void setActivityDate(Date activityDate) {
        this.activityDate = activityDate;
    }

	@Column(name = "activity", nullable = false, length = 80)
    public String getActivity() {
        return this.activity;
    }

    public void setActivity(String activity) {
        this.activity = activity;
    }

	@Column(name = "terminal_address", length = 45)
    public String getTerminalAddress() {
        return this.terminalAddress;
    }

    public void setTerminalAddress(String terminalAddress) {
        this.terminalAddress = terminalAddress;
    }

	@Column(name = "terminal_id", length = 80)
    public String getTerminalId() {
        return this.terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

	@Column(name = "terminal_user_id", length = 80)
    public String getTerminalUserId() {
        return this.terminalUserId;
    }

    public void setTerminalUserId(String terminalUserId) {
        this.terminalUserId = terminalUserId;
    }
}