package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.DocumentSigningRequestDao;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequestDetail;
import com.adins.util.Tools;

@Component
@Transactional
public class DocumentSigningRequestDaoHbn extends BaseDaoHbn implements DocumentSigningRequestDao {

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrDocumentSigningRequest getDocumentSigningRequestNewTran(long idDocumentSigningRequest) {
        Map<String, Object> params = new HashMap<>();
        params.put("idDocumentSigningRequest", idDocumentSigningRequest);

        return managerDAO.selectOne(
            "from TrDocumentSigningRequest dsr "
				+ "left join fetch dsr.trDocumentD dd "
                + "left join fetch dd.msTenant mt "
                + "left join fetch dd.msVendor mv "
				+ "join fetch dsr.trDocumentH dh "
                + "left join fetch dh.msOffice mo "
                + "left join fetch dh.msBusinessLine mbl "
                + "join fetch dh.msTenant dmt "
				+ "join fetch dsr.amMsuser mu "
				+ "where dsr.idDocumentSigningRequest = :idDocumentSigningRequest ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateDocumentSigningRequestNewTran(TrDocumentSigningRequest documentSigningRequest) {
        documentSigningRequest.setUsrUpd(Tools.maskData(documentSigningRequest.getUsrUpd()));
        managerDAO.update(documentSigningRequest);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<TrDocumentSigningRequestDetail> getDocumentSigningRequestDetailsNewTran(TrDocumentSigningRequest documentSigningRequest) {
        Map<String, Object> params = new HashMap<>();
        params.put("documentSigningRequest", documentSigningRequest);

        return (List<TrDocumentSigningRequestDetail>) managerDAO.list(
            "from TrDocumentSigningRequestDetail  dsrl "
            + "join fetch dsrl.trDocumentSigningRequest tdsr "
            + "join fetch tdsr.amMsuser mu "
            + "join fetch dsrl.trDocumentD dd "
            + "where dsrl.trDocumentSigningRequest = :documentSigningRequest ", params).get(Constants.MAP_RESULT_LIST);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertDocumentSigningRequestNewTran(TrDocumentSigningRequest documentSigningRequest) {
        documentSigningRequest.setUsrCrt(Tools.maskData(documentSigningRequest.getUsrCrt()));
        managerDAO.insert(documentSigningRequest);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertDocumentSigningRequestDetailNewTran(TrDocumentSigningRequestDetail requestDetail) {
        requestDetail.setUsrCrt(Tools.maskData(requestDetail.getUsrCrt()));
        managerDAO.insert(requestDetail);
    }
    
}
