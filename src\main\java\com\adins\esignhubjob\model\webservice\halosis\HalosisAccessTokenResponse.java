package com.adins.esignhubjob.model.webservice.halosis;

import com.google.gson.annotations.SerializedName;

public class HalosisAccessTokenResponse {
    private String message;
	@SerializedName("long_lived_token") private String longLivedToken;
	@SerializedName("token_expired_at") private String tokenExpiredAt;
	
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getLongLivedToken() {
		return longLivedToken;
	}
	public void setLongLivedToken(String longLivedToken) {
		this.longLivedToken = longLivedToken;
	}
	public String getTokenExpiredAt() {
		return tokenExpiredAt;
	}
	public void setTokenExpiredAt(String tokenExpiredAt) {
		this.tokenExpiredAt = tokenExpiredAt;
	}
}
