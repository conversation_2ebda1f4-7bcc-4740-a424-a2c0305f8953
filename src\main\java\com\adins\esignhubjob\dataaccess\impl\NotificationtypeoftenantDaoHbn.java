package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.NotificationtypeoftenantDao;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsNotificationtypeoftenant;
import com.adins.esignhubjob.model.table.MsTenant;

@Transactional
@Component
public class NotificationtypeoftenantDaoHbn extends BaseDaoHbn implements NotificationtypeoftenantDao {

    @Override
    public MsNotificationtypeoftenant getNotificationType(MsTenant tenant, String sendingPointCode) {
        Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(sendingPointCode));
		
		return managerDAO.selectOne(
				"from MsNotificationtypeoftenant nt "
				+ "join fetch nt.msTenant mt "
				+ "join fetch nt.lovSendingPoint sp "
				+ "join fetch nt.lovSmsGateway sg "
				+ "join fetch nt.lovWaGateway wg "
				+ "where nt.msTenant = :tenant "
				+ "and sp.code = :code ", params);
    }

    @Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsNotificationtypeoftenant getNotificationTypeNewTrx(MsTenant tenant, String sendingPointCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(sendingPointCode));
		
		return managerDAO.selectOne(
				"from MsNotificationtypeoftenant nt "
				+ "join fetch nt.msTenant mt "
				+ "join fetch nt.lovSendingPoint sp "
				+ "join fetch nt.lovSmsGateway sg "
				+ "join fetch nt.lovWaGateway wg "
				+ "where nt.msTenant = :tenant "
				+ "and sp.code = :code ", params);
	}
    
}
