package com.adins.esignhubjob.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.esignhubjob.model.custom.vida.VidaSigningResponseContainer;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.webservice.vida.VidaCheckStampStatusResponse;
import com.adins.esignhubjob.model.webservice.vida.VidaGetPeruriDocumentTypeResponse;
import com.adins.esignhubjob.model.webservice.vida.VidaStampResponse;
import com.aliyun.fc.runtime.Context;

public interface VidaLogic {
    VidaSigningResponseContainer signVida(TrDocumentSigningRequest signingRequest, TrDocumentDSign documentDSign, MsVendorRegisteredUser vendorRegisteredUser, String reservedTrxNo, Context context) throws IOException;
    VidaSigningResponseContainer getDocumentStatus(TrDocumentDSign documentDSign, String trackStatusId, Context context) throws IOException;
    VidaGetPeruriDocumentTypeResponse getPeruriDocumentType(String partnerId, Context context) throws IOException;
    String vidaLogOn(Context context) throws IOException;
    VidaStampResponse uploadStampVida(String accessToken, TrDocumentD document, TrDocumentDStampduty documentStampduty, String reservedTrxNo, Context context) throws IOException;
    VidaCheckStampStatusResponse checkStampStatus(String accessToken, TrDocumentD document, String reservedTrxNo, Context context) throws IOException;
}
