package com.adins.esignhubjob.dataaccess.api;

import java.util.Date;
import java.util.List;

import com.adins.esignhubjob.model.custom.adins.RemainingQuotaBean;
import com.adins.esignhubjob.model.table.TrBalanceTopUp;

public interface BalanceTopUpDao {
    void insertTrBalanceTopUp(TrBalanceTopUp balanceTopUp);
    void updateBalanceTopUp(TrBalanceTopUp updBaltop);
    List<RemainingQuotaBean> getExpiredTopupBalanceByTenant(Date range, String tenantCode);
    List<TrBalanceTopUp> getReminderExpiredBalanceTopUp(Date today, Date targetDate, String tenantCode);
    TrBalanceTopUp getTrBalanceTopUp (long idTrBalanceTopUp);
	void updateBalanceTopUpNativeString(long idBalanceTopup, String usrUpd, String isUsed);
}
