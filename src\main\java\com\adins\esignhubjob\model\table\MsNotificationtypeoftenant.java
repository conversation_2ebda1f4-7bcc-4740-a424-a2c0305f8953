package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_notificationtypeoftenant")
public class MsNotificationtypeoftenant extends CreatableAndUpdatableEntity implements Serializable {

    private static final long serialVersionUID = 1L;
	
	private long idMsNotificationtypeoftenant;
	private MsTenant msTenant;
	private MsLov lovSendingPoint;
	private String useWaMessage;
	private String mustUseWaFirst;
	private String sendOtpByEmail;
	private MsLov lovSmsGateway;
	private MsLov lovWaGateway;
	private String mustUseSmsFirst;

    @Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_notificationtypeoftenant", unique = true, nullable = false)
	public long getIdMsNotificationtypeoftenant() {
		return idMsNotificationtypeoftenant;
	}
	
	public void setIdMsNotificationtypeoftenant(long idMsNotificationtypeoftenant) {
		this.idMsNotificationtypeoftenant = idMsNotificationtypeoftenant;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return msTenant;
	}
	
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sending_point", nullable = false)
	public MsLov getLovSendingPoint() {
		return lovSendingPoint;
	}
	
	public void setLovSendingPoint(MsLov lovSendingPoint) {
		this.lovSendingPoint = lovSendingPoint;
	}
	
	@Column(name = "use_wa_message", length = 1)
	public String getUseWaMessage() {
		return useWaMessage;
	}
	
	public void setUseWaMessage(String useWaMessage) {
		this.useWaMessage = useWaMessage;
	}
	
	@Column(name = "must_use_wa_first", length = 1)
	public String getMustUseWaFirst() {
		return mustUseWaFirst;
	}
	
	public void setMustUseWaFirst(String mustUseWaFirst) {
		this.mustUseWaFirst = mustUseWaFirst;
	}
	
	@Column(name = "send_otp_by_email", length = 1)
	public String getSendOtpByEmail() {
		return sendOtpByEmail;
	}
	public void setSendOtpByEmail(String sendOtpByEmail) {
		this.sendOtpByEmail = sendOtpByEmail;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sms_gateway", nullable = false)
	public MsLov getLovSmsGateway() {
		return lovSmsGateway;
	}
	
	public void setLovSmsGateway(MsLov lovSmsGateway) {
		this.lovSmsGateway = lovSmsGateway;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_wa_gateway", nullable = false)
	public MsLov getLovWaGateway() {
		return lovWaGateway;
	}

	public void setLovWaGateway(MsLov lovWaGateway) {
		this.lovWaGateway = lovWaGateway;
	}

	@Column(name = "must_use_sms_first", length = 1)
	public String getMustUseSmsFirst() {
		return mustUseSmsFirst;
	}

	public void setMustUseSmsFirst(String mustUseSmsFirst) {
		this.mustUseSmsFirst = mustUseSmsFirst;
	}

}
