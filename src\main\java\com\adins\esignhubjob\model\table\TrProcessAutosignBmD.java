package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_process_autosign_bm_d")
public class TrProcessAutosignBmD extends CreatableAndUpdatableEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    private long idProcessAutosignBmD;
    private TrProcessAutosignBmH trProcessAutosignBmH;
    private String email;
    private String phone;
    private String nik;
    private String placeOfBirth;
    private Date dateOfBirth;
    private String gender;
    private String keyUser;
    private String cvv;
    private String poaId;
    private String status;
    private String notes;
    private Date certExpiredDate;

    @Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_process_autosign_d", unique = true, nullable = false)
    public long getIdProcessAutosignBmD() {
        return this.idProcessAutosignBmD;
    }

    public void setIdProcessAutosignBmD(long idProcessAutosignBmD) {
        this.idProcessAutosignBmD = idProcessAutosignBmD;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_process_autosign_bm_h", nullable = false)
    public TrProcessAutosignBmH getTrProcessAutosignBmH() {
        return this.trProcessAutosignBmH;
    }

    public void setTrProcessAutosignBmH(TrProcessAutosignBmH trProcessAutosignBmH) {
        this.trProcessAutosignBmH = trProcessAutosignBmH;
    }

    @Column(name = "email", length = 80)
    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "phone", length = 20)
    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Column(name = "nik", length = 50)
    public String getNik() {
        return this.nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    @Column(name = "place_of_birth", length = 80)
    public String getPlaceOfBirth() {
        return this.placeOfBirth;
    }

    public void setPlaceOfBirth(String placeOfBirth) {
        this.placeOfBirth = placeOfBirth;
    }

    @Temporal(TemporalType.DATE)
	@Column(name = "date_of_birth", length = 13)
    public Date getDateOfBirth() {
        return this.dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    @Column(name = "gender", length = 1)
    public String getGender() {
        return this.gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    @Column(name = "key_user", length = 100)
    public String getKeyUser() {
        return this.keyUser;
    }

    public void setKeyUser(String keyUser) {
        this.keyUser = keyUser;
    }

    @Column(name = "cvv", length = 3)
    public String getCvv() {
        return this.cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    @Column(name = "poa_id", length = 40)
    public String getPoaId() {
        return this.poaId;
    }

    public void setPoaId(String poaId) {
        this.poaId = poaId;
    }

    @Column(name = "status", length = 1)
    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "notes", length = 200)
    public String getNotes() {
        return this.notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Temporal(TemporalType.TIMESTAMP)
	@Column(name = "cert_expired_date", length = 29)
    public Date getCertExpiredDate() {
        return certExpiredDate;
    }


    public void setCertExpiredDate(Date certExpiredDate) {
        this.certExpiredDate = certExpiredDate;
    }
    

}
