package com.adins.esignhubjob.model.webservice.privygeneral;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralRegisterStatusRequest {
    @SerializedName("reference_number") private String referenceNumber;
    @SerializedName("channel_id") private String channelId;
    @SerializedName("register_token") private String registerToken;
    private String info;

    public String getReferenceNumber() {
        return this.referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getChannelId() {
        return this.channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getRegisterToken() {
        return this.registerToken;
    }

    public void setRegisterToken(String registerToken) {
        this.registerToken = registerToken;
    }

    public String getInfo() {
        return this.info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

}
