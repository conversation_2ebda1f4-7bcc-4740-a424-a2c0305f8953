package com.adins.esignhubjob.model.webservice.privygeneral;

import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralErrorBean;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralRegisterStatusBean;

public class PrivyGeneralRegisterStatusResponse {
    private String message;
    private PrivyGeneralRegisterStatusBean data;
    private Boolean resend;
    private PrivyGeneralErrorBean error;

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public PrivyGeneralRegisterStatusBean getData() {
        return this.data;
    }

    public void setData(PrivyGeneralRegisterStatusBean data) {
        this.data = data;
    }

    public Boolean isResend() {
        return this.resend;
    }

    public Boolean getResend() {
        return this.resend;
    }

    public void setResend(Boolean resend) {
        this.resend = resend;
    }

    public PrivyGeneralErrorBean getError() {
        return this.error;
    }

    public void setError(PrivyGeneralErrorBean error) {
        this.error = error;
    }

}
