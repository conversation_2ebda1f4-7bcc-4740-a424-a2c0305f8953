package com.adins.esignhubjob;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.businesslogic.factory.api.LogicFactory;
import com.adins.esignhubjob.dataaccess.factory.api.DaoFactory;
import com.adins.esignhubjob.factory.api.ApplicationBean;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.exceptions.ApiKeyException;
import com.adins.exceptions.Status;
import com.adins.framework.exception.AdInsException;
import com.aliyun.fc.runtime.Context;
import com.aliyun.fc.runtime.FunctionComputeLogger;
import com.aliyun.fc.runtime.FunctionInitializer;
import com.aliyun.fc.runtime.HttpRequestHandler;
import com.google.gson.Gson;

public abstract class BaseHttpHandler implements HttpRequestHandler, FunctionInitializer{

    protected FunctionComputeLogger logger = null;
    protected ClassPathXmlApplicationContext appContext = null;
    protected DaoFactory daoFactory = null;
    protected LogicFactory logicFactory = null;
    protected Gson gson = null;

    public abstract void doHandleRequest(HttpServletRequest request, HttpServletResponse response, Context context) throws IOException, ServletException;

    @Override
    public void initialize(Context context) throws IOException {
        try {
            long startTime = System.currentTimeMillis();
            
            this.logger = context.getLogger();

            // Load beans from xml
            appContext = new ClassPathXmlApplicationContext("application-context.xml");
            
            ApplicationBean appBeans = appContext.getBean(ApplicationBean.class);
            daoFactory = appBeans.getDaoFactory();
            logicFactory = appBeans.getLogicFactory();
            gson = appContext.getBean(Gson.class);
            
            this.logProcessDuration("Initialize function", startTime);
        } catch (Exception e) {
            String stacktrace = ExceptionUtils.getStackTrace(e);
            this.logger.error(stacktrace);
            throw e;
        }
    }

    @Override
    public void handleRequest(HttpServletRequest request, HttpServletResponse response, Context context) throws IOException, ServletException {
        try {
            
            doHandleRequest(request, response, context);

        } catch (AdInsException e) {

            Status status = new Status();
            status.setCode(e.getErrorCode());
            status.setMessage(e.getMessage());
            this.writeResponse(response, status);

        } catch (Exception e) {

            String stacktrace = ExceptionUtils.getStackTrace(e);
            this.logger.error(stacktrace);

            Status status = new Status(9999, "Unknown system error");
            this.writeResponse(response, status);
        }
    }

    private void writeResponse(HttpServletResponse response, Status status) throws IOException {
		response.setHeader("Content-Type", "application/json");
		response.getWriter().write(gson.toJson(status));
	}

    /**
	 * @param processLabel
	 * @param startTime Obtain startTime from System.currentTimeMillis()
	 */
	protected void logProcessDuration(String processLabel, long startTime) {
		long durationMs = System.currentTimeMillis() - startTime;
		this.logger.info(processLabel + " duration: " + durationMs + " ms");
	}

    protected void validateApiKey(HttpServletRequest request) {
        String xApiKey = request.getHeader(HttpHeaders.KEY_X_API_KEY);
        this.logger.debug("Validating key: " + xApiKey);
        MsTenant tenant = this.daoFactory.getTenantDao().getTenantByApiKey(xApiKey);
        if (null == tenant) {
            throw new ApiKeyException("Invalid API key");
        }
        
    }
    
}
