package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_email_hosting")
public class MsEmailHosting extends ActivatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	
	public static final String ID_EMAIL_HOSTING_HBM = "idEmailHosting";
	
	private long idEmailHosting;
	private MsVendor msVendor;
	private String emailHostingDomain;
	private String cpanelUsername;
	private String cpanelPassword;
	private String defaultEmailPassword;
	private String defaultEmailInbox;
	private String accessPort;
	private Set<MsTenant> msTenants = new HashSet<>(0);
	private Set<AmMsuser> amMsusers = new HashSet<>(0);
	private Set<MsVendorRegisteredUser> msVendorRegisteredUsers = new HashSet<>(0);

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_email_hosting", unique = true, nullable = false)
	public long getIdEmailHosting() {
		return this.idEmailHosting;
	}

	public void setIdEmailHosting(long idEmailHosting) {
		this.idEmailHosting = idEmailHosting;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor")
	public MsVendor getMsVendor() {
		return this.msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@Column(name = "email_hosting_domain", length = 64)
	public String getEmailHostingDomain() {
		return this.emailHostingDomain;
	}

	public void setEmailHostingDomain(String emailHostingDomain) {
		this.emailHostingDomain = emailHostingDomain;
	}

	@Column(name = "cpanel_username", length = 36)
	public String getCpanelUsername() {
		return this.cpanelUsername;
	}

	public void setCpanelUsername(String cpanelUsername) {
		this.cpanelUsername = cpanelUsername;
	}

	@Column(name = "cpanel_password", length = 200)
	public String getCpanelPassword() {
		return this.cpanelPassword;
	}

	public void setCpanelPassword(String cpanelPassword) {
		this.cpanelPassword = cpanelPassword;
	}

	@Column(name = "default_email_password", length = 200)
	public String getDefaultEmailPassword() {
		return this.defaultEmailPassword;
	}

	public void setDefaultEmailPassword(String defaultEmailPassword) {
		this.defaultEmailPassword = defaultEmailPassword;
	}

	@Column(name = "default_email_inbox", length = 64)
	public String getDefaultEmailInbox() {
		return this.defaultEmailInbox;
	}

	public void setDefaultEmailInbox(String defaultEmailInbox) {
		this.defaultEmailInbox = defaultEmailInbox;
	}
	
	@Column(name = "access_port", length = 10)
	public String getAccessPort() {
		return accessPort;
	}

	public void setAccessPort(String accessPort) {
		this.accessPort = accessPort;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msEmailHosting")
	public Set<MsTenant> getMsTenants() {
		return this.msTenants;
	}

	public void setMsTenants(Set<MsTenant> msTenants) {
		this.msTenants = msTenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msEmailHosting")
	public Set<AmMsuser> getAmMsusers() {
		return amMsusers;
	}

	public void setAmMsusers(Set<AmMsuser> amMsusers) {
		this.amMsusers = amMsusers;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msEmailHosting")
	public Set<MsVendorRegisteredUser> getMsVendorRegisteredUsers() {
		return msVendorRegisteredUsers;
	}

	public void setMsVendorRegisteredUsers(Set<MsVendorRegisteredUser> msVendorRegisteredUsers) {
		this.msVendorRegisteredUsers = msVendorRegisteredUsers;
	}
}
