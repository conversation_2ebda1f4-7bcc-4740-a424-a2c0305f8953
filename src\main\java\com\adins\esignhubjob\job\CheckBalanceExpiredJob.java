package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailAttachmentBean;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.custom.adins.RemainingQuotaBean;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.TrBackgroundProcessFail;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrBalanceTopUp;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.exceptions.EmailSendingException;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class CheckBalanceExpiredJob extends BaseJobHandler {

    private static final String[] ERROR_EMAIL_RECEIVERS = { "<EMAIL>" };

    private static final String PARAM_NOTES = "notes";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {

        Date range = Tools.changeDateFormat(new Date(), Constants.DATE_FORMAT);

        String usrUpd = "JOB_CHECK_EXPIRED_BALANCE";

        AmGeneralsetting picOperation = daoFactory.getGeneralSettingDao().getGsObjByCode("PIC_OPERATION_ADINS");
        String[] recipientOperation = picOperation.getGsValue().split(";");
        if (StringUtils.isBlank(recipientOperation[0])) {
            recipientOperation = ERROR_EMAIL_RECEIVERS;
        }

        List<MsTenant> activeTenants = daoFactory.getTenantDao().getActiveTenants();
        context.getLogger().info("Total active tenant : " + activeTenants.size());

        processExpiredBalances(activeTenants, range, usrUpd, recipientOperation, context);

        processExpiringBalances(activeTenants, range, recipientOperation, context);
    }

    private void processExpiredBalances(List<MsTenant> activeTenants, Date range, String usrUpd, String[] recipientOperation, Context context) {
        context.getLogger().info("Start List Expired Balance");
        for (MsTenant tenant : activeTenants) {
            context.getLogger().info("Selected tenant: " + tenant.getTenantCode());

            List<RemainingQuotaBean> balanceTopups = daoFactory.getBalanceTopUpDao().getExpiredTopupBalanceByTenant(range, tenant.getTenantCode());

            if (CollectionUtils.isEmpty(balanceTopups)) {
                context.getLogger().info("No matching expired balance for tenant: " + tenant.getTenantCode());
                continue;
            }

            context.getLogger().info("Data processed by each tenant: " + balanceTopups.size());

            String receiver = tenant.getEmailReminderDest();
            String[] receivers = receiver.split(",");

            String itemsHtml = processExpiredBalanceAndBuildHtml(balanceTopups, usrUpd, context);

            // After looping — send email once per tenant
            Map<String, Object> templateParams = new HashMap<>();
            templateParams.put("items", itemsHtml);
            templateParams.put("tenantName", tenant.getTenantName());

            MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(
                    Constants.TEMPLATE_SEND_EMAIL_EXPIRED_BALANCE, templateParams);

            EmailInformationBean emailInfo = new EmailInformationBean();
            emailInfo.setSubject(template.getSubject());
            emailInfo.setBodyMessage(template.getBody());
            emailInfo.setTo(receivers);
            emailInfo.setCc(recipientOperation);

            try {
                logicFactory.getEmailSenderLogic().sendEmail(emailInfo, null, context);
                context.getLogger().info("Email sent successfully to: " + Arrays.toString(receivers)
                        + ", Cc to " + Arrays.toString(recipientOperation));
            } catch (EmailSendingException e) {
                String notes = "Send Email Expired Balance";
                handleEmailSendingError(e, template, recipientOperation, itemsHtml, notes, context);
            }

            // Log and record scheduler info
            String notes = "Check Expired Balance Done";

            insertTrSchedulerJob(new Date(), Constants.LOV_CODE_SCHEDULER_DAILY,
                    Constants.CODE_LOV_JOB_TYPE_CHECK_EXPIRED_BALANCE, balanceTopups.size(), notes);
        }
    }

    private void processExpiringBalances(List<MsTenant> activeTenants, Date range, String[] recipientOperation, Context context) {
        context.getLogger().info("Start List Expiring Balance");
        for (MsTenant tenant : activeTenants) {
            processExpiringBalance(tenant, range, recipientOperation, context);
        }
    }

    private void processExpiringBalance(MsTenant tenant, Date range, String[] recipientOperation, Context context) {

        context.getLogger().info("Processing tenant: " + tenant.getTenantCode());
        int reminderDuration = getExpiredReminderDuration(tenant, context);

        // Konversi ke target date
        Date targetDate = DateUtils.addDays(new Date(), reminderDuration);
        
        List<TrBalanceTopUp> reminderBalance = daoFactory.getBalanceTopUpDao().getReminderExpiredBalanceTopUp(range, targetDate, tenant.getTenantCode());
        if (CollectionUtils.isEmpty(reminderBalance)) {
            context.getLogger().info("No Expiring Balance");
            return;
        }
        
        String receiver = tenant.getEmailReminderDest();
        String[] receivers = receiver.split(",");
        
        context.getLogger().info("Data processed each tenant: " + reminderBalance.size());
        String itemsHtml = processExpiringBalanceAndBuildHtml(reminderBalance, context);
        
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("items", itemsHtml);
        templateParams.put("tenantName", tenant.getTenantName());
            
        MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(Constants.TEMPLATE_SEND_EMAIL_REMINDER_EXPIRED_BALANCE, templateParams);
        
        EmailInformationBean emailInfo = new EmailInformationBean();
        emailInfo.setSubject(template.getSubject());
        emailInfo.setBodyMessage(template.getBody());
        emailInfo.setTo(receivers);
        
        try {
            logicFactory.getEmailSenderLogic().sendEmail(emailInfo, null, context);
            context.getLogger().info("Email sent successfully to: " + Arrays.toString(receivers));
        } catch (EmailSendingException e) {
            String notes = "Send Email Expiring Balance";
            handleEmailSendingError(e, template, recipientOperation, itemsHtml, notes, context);
        }
    }

    private int getExpiredReminderDuration(MsTenant tenant, Context context) {
        MsTenantSettings expiredTS = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, AmGlobalKey.GS_REMINDER_DURATION_BEFORE_BALANCE_EXPIRED);
        if (null == expiredTS || StringUtils.isEmpty(expiredTS.getSettingValue())) {
            return getDefaultExpiredReminderDuration(tenant, context);
        }

        try {
            Integer duration = Integer.parseInt(expiredTS.getSettingValue());
            if (duration <= 0) {
                return getDefaultExpiredReminderDuration(tenant, context);
            }

            context.getLogger().info(String.format("Tenant %1$s, using expired date range from tenant setting. Date range: %2$s", tenant.getTenantCode(), duration));
            return duration;
        } catch (Exception e) {
            return getDefaultExpiredReminderDuration(tenant, context);
        }
    }

    private int getDefaultExpiredReminderDuration(MsTenant tenant, Context context) {
        String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_REMINDER_DURATION_BEFORE_BALANCE_EXPIRED);
        context.getLogger().info(String.format("Tenant %1$s, using expired date range from general setting. Date range: %2$s", tenant.getTenantCode(), maxRangeDate));
        return Integer.parseInt(maxRangeDate);
    }

    private void handleEmailSendingError(Exception e, MsMsgTemplate template, String[] receiver, String itemsHtml,
            String notes, Context context) {
        TrBackgroundProcessFail processFail = new TrBackgroundProcessFail();
        processFail.setProcessName(template.getSubject());
        processFail.setProcessLocation("Reminder Saldo Expired");
        processFail.setStackTrace(Arrays.toString(e.getStackTrace()));
        processFail.setProcessStatus("0");
        processFail.setUsrCrt("FC Job Scheduler Expired Balance");
        processFail.setDtmCrt(new Date());

        daoFactory.getBackgroundProcessFailDao().insertTrBackgroundProcessFail(processFail);

        EmailAttachmentBean[] attachments = null;
        byte[] stackTraceFile = buildStackTraceTextFile(e);
        String filename = buildStackTraceFileName(processFail.getProcessLocation());
        EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
        attachments = new EmailAttachmentBean[] { attachment };

        Map<String, Object> templateParameter = new HashMap<>();
        templateParameter.put("templateSubject", template.getSubject());
        templateParameter.put("module", itemsHtml);
        templateParameter.put(PARAM_NOTES, "Process send email '" + notes + "' gagal.");

        MsMsgTemplate templateError = logicFactory.getMessageTemplateLogic().getAndParseContent(
                Constants.TEMPLATE_SEND_EMAIL_FAIL,
                templateParameter);

        EmailInformationBean emailInfoError = new EmailInformationBean();
        emailInfoError.setSubject(templateError.getSubject());
        emailInfoError.setBodyMessage(templateError.getBody());
        emailInfoError.setTo(receiver);

        try {
            logicFactory.getEmailSenderLogic().sendEmail(emailInfoError, attachments, context);
            context.getLogger().info("Email Failed sent successfully to: " + Arrays.toString(receiver));
        } catch (Exception e1) {
            context.getLogger().error("Send email error");
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
        }
    }

    private byte[] buildStackTraceTextFile(Exception e) {
        String stackTrace = ExceptionUtils.getStackTrace(e);
        String base64 = Base64.getEncoder().encodeToString(stackTrace.getBytes());
        return Base64.getDecoder().decode(base64);
    }

    private String buildStackTraceFileName(String errorLocation) {
        String currentTime = new SimpleDateFormat(Constants.DATE_FORMAT).format(new Date());
        StringBuilder filename = new StringBuilder()
                .append(StringUtils.upperCase(errorLocation)).append("_")
                .append(currentTime)
                .append(".txt");
        return filename.toString();
    }

    private void insertTrSchedulerJob(Date startTime, String lovSchedulerTypeCode, String lovJobTypeCode,
            long dataProcessed, String notes) {
        MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE,
                lovSchedulerTypeCode);
        MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, lovJobTypeCode);

        TrSchedulerJob schedulerJob = new TrSchedulerJob();
        schedulerJob.setSchedulerStart(startTime);
        schedulerJob.setSchedulerEnd(new Date());
        schedulerJob.setMsLovBySchedulerType(lovSchedulerType);
        schedulerJob.setMsLovByJobType(lovJobType);
        schedulerJob.setDataProcessed(dataProcessed);
        schedulerJob.setNotes(notes);
        schedulerJob.setUsrCrt("SH");
        schedulerJob.setDtmCrt(new Date());
        daoFactory.getSchedulerJobDao().insertSchedulerJob(schedulerJob);
    }

    private String processExpiredBalanceAndBuildHtml(List<RemainingQuotaBean> balanceTopups, String usrUpd,
            Context context) {
        StringBuilder itemsBuilder = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MMMM-yyyy");
        int index = 1;

        // Table Header
        itemsBuilder.append(Constants.TEMPLATE_HTML_TAG_START_TR)
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>No</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Vendor</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Service Name</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Tanggal Isi Ulang</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Jumlah Kedaluwarsa</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Sisa Saldo</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Invoice No.</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Notes</strong></td>")
                .append(Constants.TEMPLATE_HTML_TAG_END_TR);

        for (RemainingQuotaBean balanceTopup : balanceTopups) {
            TrBalanceMutation data = daoFactory.getBalanceMutationDao()
                    .getVendorTenantLovByIdBalMut(balanceTopup.getBalmut().longValue());

            if (balanceTopup.getRemainingQuota() > 0) {
                String formattedExpiredDate = sdf.format(data.getTrxDate());

                itemsBuilder.append(Constants.TEMPLATE_HTML_TAG_START_TR)
                        .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                        .append(index++)
                        .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                        .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                        .append(data.getMsVendor().getVendorName())
                        .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                        .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                        .append(data.getMsLovByLovBalanceType().getDescription())
                        .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                        .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                        .append(formattedExpiredDate)
                        .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                        .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                        .append(balanceTopup.getUsedQty())
                        .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                        .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                        .append(balanceTopup.getRemainingQuota())
                        .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                        .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                        .append(data.getRefNo())
                        .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                        .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                        .append(data.getNotes())
                        .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                        .append(Constants.TEMPLATE_HTML_TAG_END_TR);

                // Update DB
                daoFactory.getBalanceTopUpDao().updateBalanceTopUpNativeString(
                        balanceTopup.getBaltop().longValue(), usrUpd, "1");

                daoFactory.getBalanceMutationDao().insertBalanceMutationNativeString(
                        balanceTopup.getBalmut().longValue(), balanceTopup.getBalmut().longValue(), usrUpd,
                        "Saldo Expired", balanceTopup.getRemainingQuota() * -1);

                context.getLogger().info("Expired Topup: " + balanceTopup.getBaltop()
                        + ", Remaining Quota: " + balanceTopup.getRemainingQuota());
            } else {
                daoFactory.getBalanceTopUpDao().updateBalanceTopUpNativeString(
                        balanceTopup.getBaltop().longValue(), usrUpd, "1");

                context.getLogger().info("Topup already expired, no remaining quota: " + balanceTopup.getBaltop());
            }
        }

        return itemsBuilder.toString();
    }

    private String processExpiringBalanceAndBuildHtml(List<TrBalanceTopUp> filteredBalance, Context context) {
        StringBuilder itemsBuilder = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MMMM-yyyy");
        int index = 1;

        // For Header Table Expiring Balance
        itemsBuilder.append(Constants.TEMPLATE_HTML_TAG_START_TR)
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>No</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Vendor</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Service Name</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Jumlah</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Invoice No.</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Notes</strong></td>")
                .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                .append("<strong>Tanggal Kedaluwarsa</strong></td>")
                .append(Constants.TEMPLATE_HTML_TAG_END_TR);

        for (TrBalanceTopUp item : filteredBalance) {
            String formattedExpiredDate = sdf.format(item.getExpiredDate());
            itemsBuilder.append(Constants.TEMPLATE_HTML_TAG_START_TR)
                    .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                    .append(index)
                    .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                    .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                    .append(item.getTrBalanceMutation().getMsVendor().getVendorName())
                    .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                    .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                    .append(item.getTrBalanceMutation().getMsLovByLovBalanceType().getDescription())
                    .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                    .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                    .append(item.getTrBalanceMutation().getQty())
                    .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                    .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                    .append(item.getTrBalanceMutation().getRefNo())
                    .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                    .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                    .append(item.getTrBalanceMutation().getNotes())
                    .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                    .append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD)
                    .append(formattedExpiredDate)
                    .append(Constants.TEMPLATE_HTML_TAG_END_TD)
                    .append(Constants.TEMPLATE_HTML_TAG_END_TR);
            index++;
        }

        return itemsBuilder.toString();
    }
}
