package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_document_d_stampduty")
public class TrDocumentDStampduty extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	private long idDocumentDStampduty;
	private TrDocumentD trDocumentD;
	private TrStampDuty trStampDuty;
	private String signLocation;
	private Integer signPage;
	private Short seqNo;
	private String transform;
	private Date stampingDate;
	private String notes;
	private Date startStampProcess;
	private String privySignLocation;
	private String reservedTrxNo;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_document_d_stampduty", unique = true, nullable = false)
	public long getIdDocumentDStampduty() {
		return this.idDocumentDStampduty;
	}

	public void setIdDocumentDStampduty(long idDocumentDStampduty) {
		this.idDocumentDStampduty = idDocumentDStampduty;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return this.trDocumentD;
	}

	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_stamp_duty")
	public TrStampDuty getTrStampDuty() {
		return this.trStampDuty;
	}

	public void setTrStampDuty(TrStampDuty trStampDuty) {
		this.trStampDuty = trStampDuty;
	}

	@Column(name = "sign_location", length = 250)
	public String getSignLocation() {
		return this.signLocation;
	}

	public void setSignLocation(String signLocation) {
		this.signLocation = signLocation;
	}

	@Column(name = "sign_page")
	public Integer getSignPage() {
		return this.signPage;
	}

	public void setSignPage(Integer signPage) {
		this.signPage = signPage;
	}

	@Column(name = "seq_no")
	public Short getSeqNo() {
		return this.seqNo;
	}

	public void setSeqNo(Short seqNo) {
		this.seqNo = seqNo;
	}

	@Column(name = "transform")
	public String getTransform() {
		return this.transform;
	}

	public void setTransform(String transform) {
		this.transform = transform;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "stamping_date", length = 29)
	public Date getStampingDate() {
		return this.stampingDate;
	}

	public void setStampingDate(Date stampingDate) {
		this.stampingDate = stampingDate;
	}

	@Column(name = "notes", length = 200)
	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "start_stamp_process", length = 29)
	public Date getStartStampProcess() {
		return startStampProcess;
	}

	public void setStartStampProcess(Date startStampProcess) {
		this.startStampProcess = startStampProcess;
	}

	@Column(name = "privy_sign_location", length = 100)
	public String getPrivySignLocation() {
		return privySignLocation;
	}

	public void setPrivySignLocation(String privySignLocation) {
		this.privySignLocation = privySignLocation;
	}

	@Column(name = "reserved_trx_no", length = 100)
	public String getReservedTrxNo() {
		return reservedTrxNo;
	}

	public void setReservedTrxNo(String reservedTrxNo) {
		this.reservedTrxNo = reservedTrxNo;
	}
}
