package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableAndDeletableEntity;

@Entity
@Table(name = "ms_lov")
public class MsLov extends ActivatableAndDeletableEntity implements Serializable {
	private static final long serialVersionUID = 1L;

    public static final String ID_LOV_HBM = "idLov";
	public static final String LOV_GROUP_HBM = "lovGroup";
	public static final String CODE_HBM = "code";
	public static final String DESCRIPTION_HBM = "description";
	public static final String SEQUENCE_HBM = "sequence";
	public static final String CONSTRAINT_1_HBM = "constraint1";
	public static final String CONSTRAINT_2_HBM = "constraint2";
	public static final String CONSTRAINT_3_HBM = "constraint3";
	public static final String CONSTRAINT_4_HBM = "constraint4";
	public static final String CONSTRAINT_5_HBM = "constraint5";	

	private long idLov;
	private String lovGroup;
	private String code;
	private String description;
	private Integer sequence;
	private String constraint1;
	private String constraint2;
	private String constraint3;
	private String constraint4;
	private String constraint5;
	
	private Set<MsDocTemplate> msDocTemplates = new HashSet<>(0);
	private Set<MsDocTemplateSignLoc> msDocTemplateSignLocsForLovSignType = new HashSet<>(0);
	private Set<TrDocumentDSign> trDocumentDSignsForLovAutosign = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutationsForLovBalanceType = new HashSet<>(0);
	private Set<TrStampDuty> trStampDuties = new HashSet<>(0);
	private Set<TrDocumentH> trDocumentHs = new HashSet<>(0);
	private Set<MsDocTemplateSignLoc> msDocTemplateSignLocsForLovSignerType = new HashSet<>(0);
	private Set<TrDocumentDSign> trDocumentDSignsForLovSignType = new HashSet<>(0);
	private Set<MsVendor> msVendors = new HashSet<>(0);
	private Set<TrDocumentDSign> trDocumentDSignsForLovSignerType = new HashSet<>(0);
	private Set<TrBalanceDailyRecap> trBalanceDailyRecaps = new HashSet<>(0);
	private Set<TrDocumentD> trDocumentDsForLovSignStatus = new HashSet<>(0);
	private Set<TrDocumentD> trDocumentDsForLovPaymentSignType = new HashSet<>(0);
	private Set<MsPaymentsigntypeoftenant> msPaymentsigntypeoftenants = new HashSet<>(0);
	private Set<AmUserpwdhistory> amUserpwdhistories = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutationsForLovTrxType = new HashSet<>(0);
	private Set<MsEmailPattern> msEmailPatterns = new HashSet<>(0);
	private Set<TrSchedulerJob> trSchedulerJobsForJobType = new HashSet<>(0);
	private Set<TrSchedulerJob> trSchedulerJobsForSchedulerType = new HashSet<>(0);
	private Set<TrSchedulerJob> trSchedulerJobsForLovBalanceType = new HashSet<>(0);
	private Set<MsBalancevendoroftenant> msBalancevendoroftenants = new HashSet<>(0);
	private Set<TrErrorHistory> trErrorHistories = new HashSet<>(0);
	private Set<TrDocumentD> trDocumentDsForLovTipeIdentitas = new HashSet<>(0);
	private Set<TrJobResult> trJobResults = new HashSet<>(0);
	private Set<TrJobCheckRegisterStatus> trJobCheckRegisterStatus = new HashSet<>(0);
	private Set<TrClientCallbackRequest> trClientCallbackRequests = new HashSet<>(0);
	private Set<MsTenantSettings> msTenantSettings = new HashSet<>(0);
	private Set<MsTenant> msTenantsForLovSmsGateway = new HashSet<>(0);

	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_lov", unique = true, nullable = false)
	public long getIdLov() {
		return this.idLov;
	}

	public void setIdLov(long idLov) {
		this.idLov = idLov;
	}

	@Column(name = "lov_group", length = 80)
	public String getLovGroup() {
		return this.lovGroup;
	}

	public void setLovGroup(String lovGroup) {
		this.lovGroup = lovGroup;
	}

	@Column(name = "code", length = 80)
	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	@Column(name = "description", length = 200)
	public String getDescription() {
		return this.description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(name = "sequence")
	public Integer getSequence() {
		return this.sequence;
	}

	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}

	@Column(name = "constraint_1", length = 80)
	public String getConstraint1() {
		return this.constraint1;
	}

	public void setConstraint1(String constraint1) {
		this.constraint1 = constraint1;
	}

	@Column(name = "constraint_2", length = 80)
	public String getConstraint2() {
		return this.constraint2;
	}

	public void setConstraint2(String constraint2) {
		this.constraint2 = constraint2;
	}

	@Column(name = "constraint_3", length = 80)
	public String getConstraint3() {
		return this.constraint3;
	}

	public void setConstraint3(String constraint3) {
		this.constraint3 = constraint3;
	}

	@Column(name = "constraint_4", length = 80)
	public String getConstraint4() {
		return this.constraint4;
	}

	public void setConstraint4(String constraint4) {
		this.constraint4 = constraint4;
	}

	@Column(name = "constraint_5", length = 80)
	public String getConstraint5() {
		return this.constraint5;
	}

	public void setConstraint5(String constraint5) {
		this.constraint5 = constraint5;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovPaymentSignType")
	public Set<MsDocTemplate> getMsDocTemplates() {
		return this.msDocTemplates;
	}

	public void setMsDocTemplates(Set<MsDocTemplate> msDocTemplates) {
		this.msDocTemplates = msDocTemplates;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovSignType")
	public Set<MsDocTemplateSignLoc> getMsDocTemplateSignLocsForLovSignType() {
		return this.msDocTemplateSignLocsForLovSignType;
	}

	public void setMsDocTemplateSignLocsForLovSignType(Set<MsDocTemplateSignLoc> msDocTemplateSignLocsForLovSignType) {
		this.msDocTemplateSignLocsForLovSignType = msDocTemplateSignLocsForLovSignType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovAutosign")
	public Set<TrDocumentDSign> getTrDocumentDSignsForLovAutosign() {
		return this.trDocumentDSignsForLovAutosign;
	}

	public void setTrDocumentDSignsForLovAutosign(Set<TrDocumentDSign> trDocumentDSignsForLovAutosign) {
		this.trDocumentDSignsForLovAutosign = trDocumentDSignsForLovAutosign;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovBalanceType")
	public Set<TrBalanceMutation> getTrBalanceMutationsForLovBalanceType() {
		return this.trBalanceMutationsForLovBalanceType;
	}

	public void setTrBalanceMutationsForLovBalanceType(Set<TrBalanceMutation> trBalanceMutationsForLovBalanceType) {
		this.trBalanceMutationsForLovBalanceType = trBalanceMutationsForLovBalanceType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<TrStampDuty> getTrStampDuties() {
		return this.trStampDuties;
	}

	public void setTrStampDuties(Set<TrStampDuty> trStampDuties) {
		this.trStampDuties = trStampDuties;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<TrDocumentH> getTrDocumentHs() {
		return this.trDocumentHs;
	}

	public void setTrDocumentHs(Set<TrDocumentH> trDocumentHs) {
		this.trDocumentHs = trDocumentHs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovSignerType")
	public Set<MsDocTemplateSignLoc> getMsDocTemplateSignLocsForLovSignerType() {
		return this.msDocTemplateSignLocsForLovSignerType;
	}

	public void setMsDocTemplateSignLocsForLovSignerType(
			Set<MsDocTemplateSignLoc> msDocTemplateSignLocsForLovSignerType) {
		this.msDocTemplateSignLocsForLovSignerType = msDocTemplateSignLocsForLovSignerType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovSignType")
	public Set<TrDocumentDSign> getTrDocumentDSignsForLovSignType() {
		return this.trDocumentDSignsForLovSignType;
	}

	public void setTrDocumentDSignsForLovSignType(Set<TrDocumentDSign> trDocumentDSignsForLovSignType) {
		this.trDocumentDSignsForLovSignType = trDocumentDSignsForLovSignType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovVendorType")
	public Set<MsVendor> getMsVendors() {
		return this.msVendors;
	}

	public void setMsVendors(Set<MsVendor> msVendors) {
		this.msVendors = msVendors;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovSignerType")
	public Set<TrDocumentDSign> getTrDocumentDSignsForLovSignerType() {
		return this.trDocumentDSignsForLovSignerType;
	}

	public void setTrDocumentDSignsForLovSignerType(Set<TrDocumentDSign> trDocumentDSignsForLovSignerType) {
		this.trDocumentDSignsForLovSignerType = trDocumentDSignsForLovSignerType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<TrBalanceDailyRecap> getTrBalanceDailyRecaps() {
		return this.trBalanceDailyRecaps;
	}

	public void setTrBalanceDailyRecaps(Set<TrBalanceDailyRecap> trBalanceDailyRecaps) {
		this.trBalanceDailyRecaps = trBalanceDailyRecaps;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovSignStatus")
	public Set<TrDocumentD> getTrDocumentDsForLovSignStatus() {
		return this.trDocumentDsForLovSignStatus;
	}

	public void setTrDocumentDsForLovSignStatus(Set<TrDocumentD> trDocumentDsForLovSignStatus) {
		this.trDocumentDsForLovSignStatus = trDocumentDsForLovSignStatus;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovPaymentSignType")
	public Set<TrDocumentD> getTrDocumentDsForLovPaymentSignType() {
		return this.trDocumentDsForLovPaymentSignType;
	}

	public void setTrDocumentDsForLovPaymentSignType(Set<TrDocumentD> trDocumentDsForLovPaymentSignType) {
		this.trDocumentDsForLovPaymentSignType = trDocumentDsForLovPaymentSignType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<MsPaymentsigntypeoftenant> getMsPaymentsigntypeoftenants() {
		return this.msPaymentsigntypeoftenants;
	}

	public void setMsPaymentsigntypeoftenants(Set<MsPaymentsigntypeoftenant> msPaymentsigntypeoftenants) {
		this.msPaymentsigntypeoftenants = msPaymentsigntypeoftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<AmUserpwdhistory> getAmUserpwdhistories() {
		return this.amUserpwdhistories;
	}

	public void setAmUserpwdhistories(Set<AmUserpwdhistory> amUserpwdhistories) {
		this.amUserpwdhistories = amUserpwdhistories;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByLovTrxType")
	public Set<TrBalanceMutation> getTrBalanceMutationsForLovTrxType() {
		return this.trBalanceMutationsForLovTrxType;
	}

	public void setTrBalanceMutationsForLovTrxType(Set<TrBalanceMutation> trBalanceMutationsForLovTrxType) {
		this.trBalanceMutationsForLovTrxType = trBalanceMutationsForLovTrxType;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByJobType")
	public Set<TrSchedulerJob> getTrSchedulerJobsForJobType() {
		return this.trSchedulerJobsForJobType;
	}

	public void setTrSchedulerJobsForJobType(Set<TrSchedulerJob> trSchedulerJobsForJobType) {
		this.trSchedulerJobsForJobType = trSchedulerJobsForJobType;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovBySchedulerType")
	public Set<TrSchedulerJob> getTrSchedulerJobsForSchedulerType() {
		return this.trSchedulerJobsForSchedulerType;
	}

	public void setTrSchedulerJobsForSchedulerType(Set<TrSchedulerJob> trSchedulerJobsForSchedulerType) {
		this.trSchedulerJobsForSchedulerType = trSchedulerJobsForSchedulerType;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovByBalanceType")
	public Set<TrSchedulerJob> getTrSchedulerJobsForLovBalanceType() {
		return this.trSchedulerJobsForLovBalanceType;
	}

	public void setTrSchedulerJobsForLovBalanceType(Set<TrSchedulerJob> trSchedulerJobsForLovBalanceType) {
		this.trSchedulerJobsForLovBalanceType = trSchedulerJobsForLovBalanceType;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<MsEmailPattern> getMsEmailPatterns() {
		return this.msEmailPatterns;
	}

	public void setMsEmailPatterns(Set<MsEmailPattern> msEmailPatterns) {
		this.msEmailPatterns = msEmailPatterns;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<MsBalancevendoroftenant> getMsBalancevendoroftenants() {
		return this.msBalancevendoroftenants;
	}

	public void setMsBalancevendoroftenants(Set<MsBalancevendoroftenant> msBalancevendoroftenants) {
		this.msBalancevendoroftenants = msBalancevendoroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<TrErrorHistory> getTrErrorHistories() {
		return trErrorHistories;
	}

	public void setTrErrorHistories(Set<TrErrorHistory> trErrorHistories) {
		this.trErrorHistories = trErrorHistories;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovIdType")
	public Set<TrDocumentD> getTrDocumentDsForLovTipeIdentitas() {
		return trDocumentDsForLovTipeIdentitas;
	}

	public void setTrDocumentDsForLovTipeIdentitas(Set<TrDocumentD> trDocumentDsForLovTipeIdentitas) {
		this.trDocumentDsForLovTipeIdentitas = trDocumentDsForLovTipeIdentitas;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLovJobType")
	public Set<TrJobResult> getTrJobResults() {
		return trJobResults;
	}

	public void setTrJobResults(Set<TrJobResult> trJobResults) {
		this.trJobResults = trJobResults;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "lovUserType")
	public Set<TrJobCheckRegisterStatus> getTrJobCheckRegisterStatus() {
		return trJobCheckRegisterStatus;
	}

	public void setTrJobCheckRegisterStatus(Set<TrJobCheckRegisterStatus> trJobCheckRegisterStatus) {
		this.trJobCheckRegisterStatus = trJobCheckRegisterStatus;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "lovCallbackType")
	public Set<TrClientCallbackRequest> getTrClientCallbackRequests() {
		return trClientCallbackRequests;
	}

	public void setTrClientCallbackRequests(Set<TrClientCallbackRequest> trClientCallbackRequests) {
		this.trClientCallbackRequests = trClientCallbackRequests;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "lovSettingType")
	public Set<MsTenantSettings> getMsTenantSettings() {
		return msTenantSettings;
	}

	public void setMsTenantSettings(Set<MsTenantSettings> msTenantSettings) {
		this.msTenantSettings = msTenantSettings;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "lovSmsGateway")
	public Set<MsTenant> getMsTenantsForLovSmsGateway() {
		return msTenantsForLovSmsGateway;
	}

	public void setMsTenantsForLovSmsGateway(Set<MsTenant> msTenantsForLovSmsGateway) {
		this.msTenantsForLovSmsGateway = msTenantsForLovSmsGateway;
	}
}
