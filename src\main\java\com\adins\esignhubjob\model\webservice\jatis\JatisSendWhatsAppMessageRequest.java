package com.adins.esignhubjob.model.webservice.jatis;

import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppTemplateBean;
import com.google.gson.annotations.SerializedName;

public class JatisSendWhatsAppMessageRequest {
    private String xid;
	private String to;
	private String type;
	@SerializedName("preview_url") private Boolean previewUrl;
    private JatisWhatsAppTemplateBean template;
	
	public String getXid() {
		return xid;
	}
	public void setXid(String xid) {
		this.xid = xid;
	}
	public String getTo() {
		return to;
	}
	public void setTo(String to) {
		this.to = to;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public Boolean getPreviewUrl() {
		return previewUrl;
	}
	public void setPreviewUrl(Boolean previewUrl) {
		this.previewUrl = previewUrl;
	}
	public JatisWhatsAppTemplateBean getTemplate() {
		return template;
	}
	public void setTemplate(JatisWhatsAppTemplateBean template) {
		this.template = template;
	}
}
