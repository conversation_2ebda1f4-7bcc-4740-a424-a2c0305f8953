package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_balance_daily_recap")
public class TrBalanceDailyRecap extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	
	private long idBalanceDailyRecap;
	private MsLov msLov;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private Date recapDate;
	private Integer recapTotalBalance;

	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_balance_daily_recap", unique = true, nullable = false)
	public long getIdBalanceDailyRecap() {
		return this.idBalanceDailyRecap;
	}

	public void setIdBalanceDailyRecap(long idBalanceDailyRecap) {
		this.idBalanceDailyRecap = idBalanceDailyRecap;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_balance_type")
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return this.msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "recap_date", length = 29)
	public Date getRecapDate() {
		return this.recapDate;
	}

	public void setRecapDate(Date recapDate) {
		this.recapDate = recapDate;
	}

	@Column(name = "recap_total_balance")
	public Integer getRecapTotalBalance() {
		return this.recapTotalBalance;
	}

	public void setRecapTotalBalance(Integer recapTotalBalance) {
		this.recapTotalBalance = recapTotalBalance;
	}
}
