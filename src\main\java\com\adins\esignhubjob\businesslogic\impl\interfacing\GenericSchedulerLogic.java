package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.math.BigInteger;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SchedulerLogic;
import com.adins.esignhubjob.model.custom.adins.BalanceTopUpBean;
import com.adins.esignhubjob.model.table.MsBalancevendoroftenant;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceDailyRecap;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.esignhubjob.model.table.TrUrlForwarder;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;


@Component
public class GenericSchedulerLogic extends BaseLogic implements SchedulerLogic {

	private static final String AUDIT = "FC_DAILY_RECAP";

	@Override
	public void dailyRecap(AuditContext audit, Context context) {
		LocalDate localDate = LocalDate.now().minusDays(1);
		Date recapDate = java.sql.Date.valueOf(localDate); // scheduler on 00:00:01, jadi local date -1 untuk rekap

		List<MsBalancevendoroftenant> listBalance = daoFactory.getBalancevendoroftenantDao().getListMsBalancevendoroftenant();
		for (MsBalancevendoroftenant bvot : listBalance) {
			if ("1".equals(bvot.getMsTenant().getIsActive())) {
				context.getLogger().info("============SEPARATOR============");
				this.balanceDailyRecap(recapDate, bvot.getMsLov(), bvot.getMsTenant(), bvot.getMsVendor(), audit, context);
				this.updateBalanceMutationsUsage(recapDate, bvot.getMsLov(), bvot.getMsTenant(), bvot.getMsVendor(),audit,context);
			}
		}

		String notes = "";
		this.insertTrSchedulerJob(recapDate, Constants.CODE_LOV_SCHED_TYPE_DAILY, Constants.CODE_LOV_JOB_TYPE_BALRECAP,
				0, notes, audit);
	}

	@Override
	public void dailyRecapUpdate(String recapDateString,AuditContext audit, Context context) {
		
		LocalDate localDate = LocalDate.now().withDayOfMonth(Integer.parseInt(recapDateString) );
		Date recapDate = java.sql.Date.valueOf(localDate); // scheduler on 00:00:01, jadi local date -1 untuk rekap
		context.getLogger().info(recapDate.toString());
		List<MsBalancevendoroftenant> listBalance = daoFactory.getBalancevendoroftenantDao().getListMsBalancevendoroftenant();
		for (MsBalancevendoroftenant bvot : listBalance) {
			if ("1".equals(bvot.getMsTenant().getIsActive())) {
				this.balanceDailyRecapUpdate(recapDate, bvot.getMsLov(), bvot.getMsTenant(), bvot.getMsVendor(), audit, context);
				this.updateBalanceMutationsUsage(recapDate, bvot.getMsLov(), bvot.getMsTenant(), bvot.getMsVendor(),audit,context);
			}
		}

		this.insertTrSchedulerJob(recapDate, Constants.CODE_LOV_SCHED_TYPE_DAILY, Constants.CODE_LOV_JOB_TYPE_BALRECAP,
				0, StringUtils.EMPTY, audit);
	}

	private void updateBalanceMutationsUsage(Date date, MsLov balanceType, MsTenant tenant, MsVendor vendor, AuditContext audit, Context context) {

		List<BalanceTopUpBean> listTopUp = daoFactory.getDailyRecapDao().getBalanceTopUp(balanceType, tenant, vendor);
		context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, found %4$s available topup(s)", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode(), listTopUp.size()));

		for (BalanceTopUpBean topUpBean : listTopUp) {
			try {
				updateBalanceMutationUsage(topUpBean, date, balanceType, tenant, vendor, context);
			} catch (Exception e) {
				context.getLogger().error(String.format("Tenant %1$s, vendor %2$s, balance %3$s, id_balance_topup %4$s, exception occurred", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode(), topUpBean.getIdBalanceTopUp()));
				context.getLogger().error(ExceptionUtils.getStackTrace(e));
			}
		}
	}

	private void updateBalanceMutationUsage(BalanceTopUpBean topUpBean, Date date, MsLov balanceType, MsTenant tenant, MsVendor vendor, Context context) {
		
		context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, id_balance_topup %4$s, found remaining balance: %5$s", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode(), topUpBean.getIdBalanceTopUp(), topUpBean.getAvailableBalance()));

		long availableBalance = topUpBean.getAvailableBalance();
		long idBalanceTopUp = topUpBean.getIdBalanceTopUp();

		// Get all tr_balance_mutation that will be bind to tr_balance_top_up
		List<Map<String, Object>> mutationsToBeBind = daoFactory.getBalanceMutationDao().getListMutationForTopupBinding(tenant, vendor, balanceType, date, availableBalance);
		List<Object> idBalanceMutations = mutationsToBeBind.stream()
			.filter(map -> map.containsKey("d0"))
			.map(map -> map.get("d0"))
			.collect(Collectors.toList());

		if (CollectionUtils.isEmpty(idBalanceMutations)) {
			context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, id_balance_topup %4$s, balance mutation not found", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode(), topUpBean.getIdBalanceTopUp()));
			return;
		}

		// Bind tr_balance_mutation to tr_balance_top_up
		daoFactory.getBalanceMutationDao().updateBalanceMutationIdBalanceTopup(idBalanceMutations, idBalanceTopUp, AUDIT);
		int mutationSize = mutationsToBeBind.size();
		context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, id_balance_topup %4$s, processed %5$s balance mutation(s)", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode(), topUpBean.getIdBalanceTopUp(), mutationSize));

		BigInteger lastRunningBalance = (BigInteger) mutationsToBeBind.get(mutationSize - 1).get("d2");
		long remainingBalance = availableBalance - lastRunningBalance.longValue();

		if (remainingBalance == 0) {
			daoFactory.getBalanceTopUpDao().updateBalanceTopUpNativeString(idBalanceTopUp, AUDIT, "1");
			context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, id_balance_topup %4$s processed is fully used", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode(), topUpBean.getIdBalanceTopUp()));
		} else {
			context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, id_balance_topup %4$s processed. Still has remaining balance: %5$s", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode(), topUpBean.getIdBalanceTopUp(), remainingBalance));
		}
	}

    private void balanceDailyRecap(Date recapDate, MsLov balanceTypeLov, MsTenant tenant, MsVendor vendor, AuditContext audit, Context context) {
		String recapDateString = Tools.formatDateToStringIn(recapDate, Constants.DATE_FORMAT); // yyyy-MM-dd 00:00:00

		if (!checkDailyRecapExisted(recapDateString, balanceTypeLov, tenant, vendor, context)) {
			int qtyBalance = daoFactory.getDailyRecapDao().countQtyDailyRecap(recapDateString, balanceTypeLov, tenant, vendor);
			context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, inserting daily recap with total balance: %4$s", tenant.getTenantCode(), vendor.getVendorCode(), balanceTypeLov.getCode(), qtyBalance));
			this.insertDailyRecap(recapDate, qtyBalance, balanceTypeLov, tenant, vendor, audit);
		}
	}

	private void balanceDailyRecapUpdate(Date recapDate, MsLov balanceTypeLov, MsTenant tenant, MsVendor vendor, AuditContext audit, Context context) {
		String recapDateString = Tools.formatDateToStringIn(recapDate, Constants.DATE_FORMAT); // yyyy-MM-dd 00:00:00
		context.getLogger().info(recapDateString + " " + balanceTypeLov.getCode() + "-" + tenant.getTenantCode() + "-"+ vendor.getVendorCode());
		
		int qtyBalance = daoFactory.getDailyRecapDao().countQtyDailyRecap(recapDateString, balanceTypeLov, tenant, vendor);
		context.getLogger().info(String.format("Tenant %1$s, Vendor %2$s, Balance type %3$s, Balance %4$s",tenant.getTenantCode(),vendor.getVendorCode(), balanceTypeLov.getCode(), qtyBalance));
			
		if (!checkDailyRecapExisted(recapDateString, balanceTypeLov, tenant, vendor, context)) {
			this.insertDailyRecap(recapDate, qtyBalance, balanceTypeLov, tenant, vendor, audit);
			
		} else {
			this.updateDailyRecap(recapDate, qtyBalance, balanceTypeLov, tenant, vendor, audit);
		}
	}


	@Override
	public void deleteUrlForwarderExpired( AuditContext audit, Context context) {
		List<String> listUrlCode = null;
		listUrlCode = daoFactory.getUrlForwarderDao().getExpiredUrlForwarder();
		for (String urlCode : listUrlCode){
			TrUrlForwarder trUrlForwarder = daoFactory.getUrlForwarderDao().getUrlForwarderByUrlCodeNewTrx(urlCode);
			context.getLogger().info("Url Code :  " + urlCode);
			daoFactory.getUrlForwarderDao().deleteUrlForwadrer(trUrlForwarder);
		}
		context.getLogger().info(" jumlah Url Forwarder yang didelete " + listUrlCode.size() );
		String notes = "";
		this.insertTrSchedulerJob(new Date(), Constants.CODE_LOV_SCHED_TYPE_MONTHLY, Constants.CODE_LOV_JOB_TYPE_DELETE_EXPIRED_URL_FORWARDER,
		listUrlCode.size(), notes, audit);
		
	}


    private void insertTrSchedulerJob(Date startTime, String lovSchedulerTypeCode, String lovJobTypeCode,
			long dataProcessed, String notes, AuditContext audit) {

		MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE,lovSchedulerTypeCode);
		MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, lovJobTypeCode);

		TrSchedulerJob schedulerJob = new TrSchedulerJob();
		schedulerJob.setSchedulerStart(startTime);
		schedulerJob.setSchedulerEnd(new Date());
		schedulerJob.setMsLovBySchedulerType(lovSchedulerType);
		schedulerJob.setMsLovByJobType(lovJobType);
		schedulerJob.setDataProcessed(dataProcessed);
		schedulerJob.setNotes(notes);
		schedulerJob.setUsrCrt(audit.getCallerId());
		schedulerJob.setDtmCrt(new Date());
		daoFactory.getSchedulerJobDao().insertSchedulerJob(schedulerJob);
	}


    private boolean checkDailyRecapExisted(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor, Context context) {
		TrBalanceDailyRecap existingDailyRecap = this.daoFactory.getDailyRecapDao().getDailyRecap(dateRecap, balanceType, tenant, vendor);
		boolean previousDailyRecapExist = 0 != existingDailyRecap.getIdBalanceDailyRecap(); // empty object model has id = 0

		if (previousDailyRecapExist) {
			context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, previous daily recap found", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode()));
		} else {
			context.getLogger().info(String.format("Tenant %1$s, vendor %2$s, balance %3$s, previous daily recap not found", tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode()));
		}

		return previousDailyRecapExist;
	}


    private void updateDailyRecap(Date recapDate, int qtyBalanceRecap, MsLov balanceType, MsTenant tenant,
			MsVendor vendor, AuditContext audit) {
				String recapDateString = Tools.formatDateToStringIn(recapDate, Constants.DATE_FORMAT); // yyyy-MM-dd 00:00:00

		TrBalanceDailyRecap dailyRecap = daoFactory.getDailyRecapDao().getDailyRecap(recapDateString, balanceType, tenant, vendor);
		if (dailyRecap != null ){
			dailyRecap.setDtmCrt(dailyRecap.getDtmCrt());
			dailyRecap.setUsrCrt(dailyRecap.getUsrCrt());
			dailyRecap.setDtmUpd(new Date());
			dailyRecap.setUsrUpd(audit.getCallerId());
			dailyRecap.setRecapTotalBalance(qtyBalanceRecap);
			daoFactory.getDailyRecapDao().updateTrBalanceDailyRecap(dailyRecap);

		}
	}
	
    private void insertDailyRecap(Date recapDate, int qtyBalanceRecap, MsLov balanceType, MsTenant tenant,
			MsVendor vendor, AuditContext audit) {

		TrBalanceDailyRecap dailyRecap = new TrBalanceDailyRecap();
		dailyRecap.setUsrCrt(audit.getCallerId());
		dailyRecap.setDtmCrt(new Date());
		dailyRecap.setMsLov(balanceType);
		dailyRecap.setMsTenant(tenant);
		dailyRecap.setMsVendor(vendor);
		dailyRecap.setRecapDate(Tools.changeDateFormat(recapDate, Constants.DATE_FORMAT));
		dailyRecap.setRecapTotalBalance(qtyBalanceRecap);
		
		daoFactory.getDailyRecapDao().insertTrBalanceDailyRecap(dailyRecap);
	}
}
