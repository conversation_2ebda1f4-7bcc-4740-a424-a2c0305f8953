package com.adins.esignhubjob.businesslogic.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.Properties;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.EmailSenderLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.model.custom.adins.EmailAttachmentBean;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;
import com.adins.exceptions.EmailSendingException;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

@Component
public class GenericEmailSenderLogic extends BaseLogic implements EmailSenderLogic {

    private static JavaMailSenderImpl mailSender = null;

    @Autowired private PersonalDataEncryptionLogic encryptionLogic;

    private static void initilizeMailSender() {
        if (null != mailSender) {
            return;
        }

        mailSender = new JavaMailSenderImpl();
        mailSender.setHost(System.getenv(Constants.ENV_VAR_EMAIL_HOST));
        mailSender.setPort(Integer.valueOf(System.getenv(Constants.ENV_VAR_EMAIL_PORT)));
        mailSender.setUsername(System.getenv(Constants.ENV_VAR_EMAIL_USERNAME));
        mailSender.setPassword(System.getenv(Constants.ENV_VAR_EMAIL_PASSWORD));

        Properties properties = mailSender.getJavaMailProperties();
        properties.put("mail.smtp.starttls.enable", "true");
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.connectiontimeout", System.getenv(Constants.ENV_VAR_EMAIL_TIMEOUT));
        properties.put("mail.smtp.timeout", System.getenv(Constants.ENV_VAR_EMAIL_TIMEOUT));
        properties.put("mail.smtp.writetimeout", System.getenv(Constants.ENV_VAR_EMAIL_TIMEOUT));
        properties.put("mail.smtp.ssl.checkserveridentity", "true");
        properties.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
    }

    @Override
    public void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments, Context context) {
        initilizeMailSender();
        try {
            execSendEmail(emailInfo, attachments, context);
        } catch (Exception e) {
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
            throw new EmailSendingException("Failed to send email", e);
        }
    }

    private void execSendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments, Context context) throws MessagingException {
        MimeMessage email = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(email, true);
        String senderAddress = mailSender.getUsername();

        helper.setTo(emailInfo.getTo());
        helper.setSubject(emailInfo.getSubject());
        helper.setText(emailInfo.getBodyMessage(), true);



        if (null != senderAddress) {
            helper.setFrom(senderAddress);
        }
        if (StringUtils.isNotBlank(emailInfo.getReplyTo())) {
			helper.setReplyTo(emailInfo.getReplyTo());
		}
		if (null != emailInfo.getCc()) {
			helper.setCc(emailInfo.getCc());
		}
		if (null != emailInfo.getBcc()) {
			helper.setBcc(emailInfo.getBcc());
		}
		if (null != attachments) {
			checkAttachment(helper, attachments);
		}

        if (emailInfo.isImportance()) {
            email.addHeader("X-Priority", "1");
            email.addHeader("Importance", "High");
        }

        String recipients = Arrays.toString(emailInfo.getTo());
        context.getLogger().info(String.format("Sending mail '%1$s' to: %2$s, cc: %3$s, bcc: %4$s", emailInfo.getSubject(), recipients, emailInfo.getCc(), emailInfo.getBcc()));
        mailSender.send(email);
        context.getLogger().info(String.format("Mail '%1$s' sent to: %2$s, cc: %3$s, bcc: %4$s", emailInfo.getSubject(), recipients, emailInfo.getCc(), emailInfo.getBcc()));
        
    }

    private void checkAttachment(MimeMessageHelper helper, EmailAttachmentBean[] attachments) throws MessagingException {
		for (EmailAttachmentBean attachment : attachments) {
			if (null == attachment.getBinary()) {
				continue;
			}
			helper.addAttachment(attachment.getFileName(), new ByteArrayResource(attachment.getBinary()));
		}
	}

    @Override
    public void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments, SigningProcessAuditTrailBean auditTrail, Context context) {
        boolean success = true;
        try {
            sendEmail(emailInfo, attachments, context);
        } catch (Exception e) {
            success = false;
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
        }

        byte[] encryptedPhone = encryptionLogic.encryptFromString(auditTrail.getPhone());
        
        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(encryptedPhone);
		trail.setHashedPhoneNo(Tools.getHashedString(auditTrail.getPhone()));
		trail.setEmail(StringUtils.upperCase(auditTrail.getEmail()));
		trail.setAmMsUser(auditTrail.getUser());
		trail.setMsTenant(auditTrail.getTenant());
		trail.setMsVendor(auditTrail.getVendorPsre());
		trail.setNotificationMedia("EMAIL");
		trail.setLovSendingPoint(auditTrail.getLovSendingPoint());
		trail.setOtpCode(auditTrail.getOtpCode());
		trail.setLovProcessType(auditTrail.getLovProcessType());
		trail.setResultStatus(success ? "1" : "0");
		trail.setNotes(StringUtils.left(auditTrail.getNotes(), 200));
		trail.setTrInvitationLink(auditTrail.getInvLink());
		trail.setUsrCrt(context.getRequestId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);
		
		if (CollectionUtils.isEmpty(auditTrail.getDocumentDs())) {
			return;
		}
		
		for (TrDocumentD document : auditTrail.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail trailDetail = new TrSigningProcessAuditTrailDetail();
			trailDetail.setSigningProcessAuditTrail(trail);
			trailDetail.setTrDocumentD(document);
			trailDetail.setUsrCrt(StringUtils.upperCase(context.getRequestId()));
			trailDetail.setDtmCrt(new Date());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTrx(trailDetail);
		}
    }

    
}
