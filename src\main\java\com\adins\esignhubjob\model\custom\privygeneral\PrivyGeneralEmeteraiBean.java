package com.adins.esignhubjob.model.custom.privygeneral;

import java.util.List;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralEmeteraiBean {
    @SerializedName("doc_category") private String docCategory;
    @SerializedName("stamp_position") private List<PrivyGeneralStampPosition> stampPosition;

    public String getDocCategory() {
        return this.docCategory;
    }

    public void setDocCategory(String docCategory) {
        this.docCategory = docCategory;
    }

    public List<PrivyGeneralStampPosition> getStampPosition() {
        return this.stampPosition;
    }

    public void setStampPosition(List<PrivyGeneralStampPosition> stampPosition) {
        this.stampPosition = stampPosition;
    }

}
