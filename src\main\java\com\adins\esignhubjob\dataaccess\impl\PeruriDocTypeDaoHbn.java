package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.adins.constants.Constants;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.PeruriDocTypeDao;
import com.adins.esignhubjob.model.table.MsPeruriDocType;
import com.adins.util.Tools;

@Component
@Transactional
public class PeruriDocTypeDaoHbn extends BaseDaoHbn implements PeruriDocTypeDao {

    @Override
    public MsPeruriDocType getMsPeruriDocTypeByDocId(String peruriDocId) {
        if (peruriDocId == null) {
			return null;
		}
		return this.managerDAO.selectOne(MsPeruriDocType.class,
				new Object[][] {{ Restrictions.eq(MsPeruriDocType.PERURI_DOC_ID_HBM, peruriDocId) }});
    }

    @Override
    public void insertMsPeruriDocType(MsPeruriDocType msPeruriDocType) {
        msPeruriDocType.setUsrCrt(Tools.maskData(msPeruriDocType.getUsrCrt()));
        this.managerDAO.insert(msPeruriDocType);
    }

    @Override
    public void updateMsPeruriDocType(MsPeruriDocType msPeruriDocType) {
        msPeruriDocType.setUsrUpd(Tools.maskData(msPeruriDocType.getUsrUpd()));
        this.managerDAO.update(msPeruriDocType);
    }

    @Override
    public void deactiveDocumentNotInDocId(List<String> allperuriDocId, String usrUpd) {
        Map<String, Object> params = new HashMap<>();
		params.put("usrUpd", Tools.maskData(usrUpd));
		params.put("docId", allperuriDocId);

		StringBuilder query = new StringBuilder();
		query.append(" UPDATE ms_peruri_doc_type SET is_Active = '0', dtm_upd = NOW(), usr_upd = :usrUpd WHERE peruri_doc_id NOT IN :docId ");
		
		managerDAO.updateNativeString(query.toString(), params);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<MsPeruriDocType> getMsPeruriDocTypeByDocName(String docName) {
        Map<String, Object> params = new HashMap<>();
		params.put("documentName", docName);
        
        return (List<MsPeruriDocType>) managerDAO.list(
            " from MsPeruriDocType dt "
                + " where dt.docName = :documentName", params
        ).get(Constants.MAP_RESULT_LIST);
    }
    
}
