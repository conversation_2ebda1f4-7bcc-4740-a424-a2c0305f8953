package com.adins.esignhubjob.dataaccess.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.RoleDao;
import com.adins.esignhubjob.model.table.AmMemberofrole;
import com.adins.esignhubjob.model.table.AmMsrole;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.util.Tools;

@Component
@Transactional
public class RoleDaoHbn extends BaseDaoHbn implements RoleDao {

    @Override
    public AmMsrole getRoleByCodeAndTenantCode(String roleCode, String tenantCode) {
        if (StringUtils.isBlank(roleCode)) {
            return null;
        }

        return this.managerDAO.selectOne(
				"from AmMsrole role "
				+" join fetch role.msTenant mt "
				+" where role.isActive='1'and mt.isActive ='1' "
						+" and role.roleCode = :roleCode and mt.tenantCode = :tenantCode",
				new Object[][] {{"roleCode", StringUtils.upperCase(roleCode)}, {MsTenant.TENANT_CODE_HBM, tenantCode}});
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public AmMsrole getRoleByCodeAndTenantCodeNewTran(String roleCode, String tenantCode) {
        if (StringUtils.isBlank(roleCode)) {
            return null;
        }

        return this.managerDAO.selectOne(
				"from AmMsrole role "
				+" join fetch role.msTenant mt "
				+" where role.isActive='1'and mt.isActive ='1' "
						+" and role.roleCode = :roleCode and mt.tenantCode = :tenantCode",
				new Object[][] {{"roleCode", StringUtils.upperCase(roleCode)}, {MsTenant.TENANT_CODE_HBM, tenantCode}});
    }

    @Override
    public AmMemberofrole getMemberofrole(AmMsuser user, AmMsrole role) {
        Object[][] params = new Object[][] {
			{"amMsuser", user},
			{"amMsrole", role}
		};
		return this.managerDAO.selectOne(
				"from AmMemberofrole mor "
				+ "join fetch mor.amMsrole "
				+ "join fetch mor.amMsuser mu "
				+ "where mor.amMsuser = :amMsuser and mor.amMsrole = :amMsrole ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public AmMemberofrole getMemberofroleNewTran(AmMsuser user, AmMsrole role) {
        Object[][] params = new Object[][] {
			{"amMsuser", user},
			{"amMsrole", role}
		};
		return this.managerDAO.selectOne(
				"from AmMemberofrole mor "
				+ "join fetch mor.amMsrole "
				+ "join fetch mor.amMsuser mu "
				+ "where mor.amMsuser = :amMsuser and mor.amMsrole = :amMsrole ", params);
    }

    @Override
    public void insertMemberOfRole(AmMemberofrole memberOfRole) {
        memberOfRole.setUsrCrt(Tools.maskData(memberOfRole.getUsrCrt()));
        managerDAO.insert(memberOfRole);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertMemberOfRoleNewTran(AmMemberofrole memberOfRole) {
        memberOfRole.setUsrCrt(Tools.maskData(memberOfRole.getUsrCrt()));
        managerDAO.insert(memberOfRole);
    }
    
}
