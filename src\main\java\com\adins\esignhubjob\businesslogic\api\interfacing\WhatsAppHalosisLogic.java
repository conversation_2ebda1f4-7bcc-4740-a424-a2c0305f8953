package com.adins.esignhubjob.businesslogic.api.interfacing;

import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.aliyun.fc.runtime.Context;

public interface WhatsAppHalosisLogic {
    /**
	 * Send WhatsApp<br>
	 * Insert balance mutation with <code>Propagation.REQUIRES_NEW</code> transaction.<br>
	 * If error occurred with database session, may be needed to get table object with <code>Propagation.REQUIRES_NEW</code> transaction.
	 */
	void sendMessage(HalosisSendWhatsAppRequestBean request, Context context);

	void sendMessage(HalosisSendWhatsAppRequestBean request, SigningProcessAuditTrailBean auditTrailBean, Context context);
}
