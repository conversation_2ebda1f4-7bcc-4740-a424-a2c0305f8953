package com.adins.esignhubjob.dataaccess.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.AmGlobalKey;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.VendorDao;
import com.adins.esignhubjob.model.table.MsBalancevendoroftenant;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.util.Tools;

@Component
@Transactional
public class VendorDaoHbn extends BaseDaoHbn implements VendorDao {

    public MsVendor getVendorByCode(String code) {
        Object[][] params = new Object[][] {{ Restrictions.eq("vendorCode", StringUtils.upperCase(code)) }};
        return managerDAO.selectOne(MsVendor.class, params);
    }

    @Override
    public void insertVendor(MsVendor vendor) {
        vendor.setUsrCrt(Tools.maskData(vendor.getUsrCrt()));
        managerDAO.insert(vendor);
    }

    @Override
    public void deleteVendor(MsVendor vendor) {
        managerDAO.delete(vendor);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MsVendor getVendorByCodeNewTrx(String code) {
        Object[][] params = new Object[][] {{ Restrictions.eq("vendorCode", StringUtils.upperCase(code)) }};
        return managerDAO.selectOne(MsVendor.class, params);
    }

    @SuppressWarnings("unchecked")
	@Override
	public List<MsBalancevendoroftenant> getListBalanceByVendorTenant(String tenantCode, String vendorCode) {
		Object[][] param = new Object[][] {{MsTenant.TENANT_CODE_HBM, tenantCode}, {MsVendor.VENDOR_CODE_HBM, vendorCode}};

		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsBalancevendoroftenant bvot "
				+ "join fetch bvot.msVendor mv "
				+ "join fetch bvot.msTenant mt "
				+ "join fetch bvot.msLov balType "
				+ "where mt.tenantCode = :tenantCode and mv.vendorCode = :vendorCode "
				+ "and balType.isActive = '1' "
				+ "and mv.isActive ='1' "
				+ "and mt.isActive='1' "
				+ "and (bvot.isHidden is null or bvot.isHidden = '0') "
			, param);
		return (List<MsBalancevendoroftenant>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
	}

}
