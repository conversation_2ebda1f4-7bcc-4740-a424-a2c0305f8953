package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrRegistrationLivenessResult;
import com.adins.esignhubjob.model.webservice.adins.LivenessResponse;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class RegistrationLivenessJob extends BaseJobHandler {

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String trxNo = IOUtils.toString(inputStream);
        TrBalanceMutation balanceMutation = daoFactory.getBalanceMutationDao().getBalanceMutationByTrxNo(trxNo);
        if (null == balanceMutation) {
            context.getLogger().error("Trx No " + trxNo + " not found");
            return;
        }

        byte[] selfiePhoto = logicFactory.getAliyunOssCloudStorageLogic().getUserSelfie(trxNo, balanceMutation.getTrxDate(), context);
        if (ArrayUtils.isEmpty(selfiePhoto)) {
            context.getLogger().error("Trx No " + trxNo + ", photo not found");
            return;
        }

        int responseCode = 0;
        boolean livenessDetected = false;
        String message = null;

        MsTenant tenant = balanceMutation.getMsTenant();
        String tenantCode = "ESIGNHUB-" + tenant.getTenantCode();
        String key = tenant.getApiKey();
        String filename = "selfie_" + trxNo + ".jpeg";

        RequestBody body = new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("tenant_code", tenantCode)
            .addFormDataPart("key", key)
            .addFormDataPart("img1", filename, RequestBody.create(selfiePhoto, MediaType.parse("image/jpeg")))
            .build();
            
        Request request = new Request.Builder()
            .url(System.getenv(Constants.ENV_VAR_ADINS_LIVENESS_URL))
            .post(body)
            .build();

        try {
            OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(90, TimeUnit.SECONDS)
            .build();
            Response response = client.newCall(request).execute();
            String jsonResponse = response.body().string();
            LivenessResponse livenessResponse = gson.fromJson(jsonResponse, LivenessResponse.class);
            context.getLogger().info(String.format("Trx No %1$s, liveness response code: %2$s", trxNo, response.code() + response.message()));
            context.getLogger().info(String.format("Trx No %1$s, liveness response body: %2$s", trxNo, jsonResponse));
            
            responseCode = response.code();

            if (200 == responseCode) {
                livenessDetected = "True".equalsIgnoreCase(livenessResponse.getResult().get(0).getFaceLiveness().getLive());
            }

            if (livenessDetected) {
                message = livenessResponse.getStatus();
            } else {
                message = livenessResponse.getError();
            }

            
        } catch (Exception e) {
            message = e.getLocalizedMessage();
            context.getLogger().error(String.format("Trx No %1$s, liveness exception: %2$s", trxNo, e.getLocalizedMessage()));
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
        }

        MsLov lovProcessResult = null;
        if (200 == responseCode) {
            lovProcessResult = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_PROCESS_RESULT, Constants.LOV_CODE_PROCESS_RESULT_SUCCESS);
        } else {
            lovProcessResult = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_PROCESS_RESULT, Constants.LOV_CODE_PROCESS_RESULT_FAILED);
        }

        TrRegistrationLivenessResult livenessResult = new TrRegistrationLivenessResult();
        livenessResult.setTrxNo(trxNo);
        livenessResult.setMsTenant(tenant);
        livenessResult.setLovProcessResult(lovProcessResult);
        livenessResult.setLivenessResult(livenessDetected ? "1" : "0");
        livenessResult.setResponseMessage(message);
        livenessResult.setUsrCrt("REG_LIVE_JOB");
        livenessResult.setDtmCrt(new Date());
        daoFactory.getRegistrationLivenessResultDao().insertRegistrationLivenessResult(livenessResult);   
    }
    
}
