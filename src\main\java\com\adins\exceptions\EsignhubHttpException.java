package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class EsignhubHttpException extends AdInsException {

    public EsignhubHttpException(String message) {
        super(message);
     }
 
     public EsignhubHttpException(String message, Throwable ex) {
         super(message, ex);
     }
 
     @Override
     public int getErrorCode() {
        // Nanti bisa ubah kalau tidak mau return code 9999 saja
        return StatusCode.UNKNOWN;
     }
}
