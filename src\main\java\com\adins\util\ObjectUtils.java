package com.adins.util;

import java.math.BigDecimal;
import java.math.BigInteger;

public class ObjectUtils {
	private ObjectUtils() {}

	public static int toInt(Object obj) {
        if (obj instanceof Integer) {
            return ((Integer) obj).intValue();
        }
        if (obj instanceof Double) {
            return ((Double) obj).intValue();
        }
        if (obj instanceof BigInteger) {
            return ((BigInteger) obj).intValue();
        }
        if (obj instanceof BigDecimal) {
            return ((BigDecimal) obj).intValue();
        }
        if (obj instanceof String) {
            try {
                return Integer.parseInt((String) obj);
            }
            catch (NumberFormatException nfe) {
                return (int) Double.parseDouble((String) obj);
            }
        }

        throw new IllegalArgumentException("Obj is not a number: " + obj.getClass());
    }
}
