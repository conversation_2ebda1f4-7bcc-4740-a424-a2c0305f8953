package com.adins.esignhubjob.model.custom.digisign;

import com.google.gson.annotations.SerializedName;

public class DigisignTransactionBean {
    @SerializedName("transaction_time") private String transactionTime;
    @SerializedName("transaction_id") private String transactionId;
    @SerializedName("trace_id") private String traceId;
    private Integer amount;
    private Long currentBalance;
    @SerializedName("phone_number") private String phoneNumber;
    private String type;

    public String getTransactionTime() {
        return this.transactionTime;
    }

    public void setTransactionTime(String transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getTransactionId() {
        return this.transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTraceId() {
        return this.traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Integer getAmount() {
        return this.amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Long getCurrentBalance() {
        return this.currentBalance;
    }

    public void setCurrentBalance(Long currentBalance) {
        this.currentBalance = currentBalance;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

   
}
