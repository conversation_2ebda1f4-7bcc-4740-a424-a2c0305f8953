package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_office")
public class MsOffice extends ActivatableEntity implements Serializable{
    private static final long serialVersionUID = 1L;
	public static final String OFFICE_CODE_HBM = "officeCode";
	public static final String TENANT_HBM = "msTenant";
	
	private long idMsOffice;
	private MsOffice msOfficeParent;
	private MsTenant msTenant;
	private String officeCode;
	private String officeName;
	private MsRegion msRegion;

	private Set<MsOffice> msOffices = new HashSet<>(0);
	private Set<TrDocumentH> trDocumentHs = new HashSet<>(0);
	private Set<AmMsuser> amMsusers = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_office", unique = true, nullable = false)
	public long getIdMsOffice() {
		return this.idMsOffice;
	}

	public void setIdMsOffice(long idMsOffice) {
		this.idMsOffice = idMsOffice;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "parent_id")
	public MsOffice getMsOfficeParent() {
		return this.msOfficeParent;
	}

	public void setMsOfficeParent(MsOffice msOfficeParent) {
		this.msOfficeParent = msOfficeParent;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "office_code", nullable = false, length = 20)
	public String getOfficeCode() {
		return this.officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	@Column(name = "office_name", nullable = false, length = 80)
	public String getOfficeName() {
		return this.officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_region")
	public MsRegion getMsRegion() {
		return this.msRegion;
	}

	public void setMsRegion(MsRegion msRegion) {
		this.msRegion = msRegion;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msOfficeParent")
	public Set<MsOffice> getMsOffices() {
		return this.msOffices;
	}

	public void setMsOffices(Set<MsOffice> msOffices) {
		this.msOffices = msOffices;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msOffice")
	public Set<TrDocumentH> getTrDocumentHs() {
		return this.trDocumentHs;
	}

	public void setTrDocumentHs(Set<TrDocumentH> trDocumentHs) {
		this.trDocumentHs = trDocumentHs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msOffice")
	public Set<AmMsuser> getAmMsusers() {
		return this.amMsusers;
	}

	public void setAmMsusers(Set<AmMsuser> amMsusers) {
		this.amMsusers = amMsusers;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msOffice")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}
}
