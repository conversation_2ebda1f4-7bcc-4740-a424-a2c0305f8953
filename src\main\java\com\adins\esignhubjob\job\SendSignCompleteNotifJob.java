package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;

import com.adins.constants.Constants;
import com.adins.constants.enums.NotificationSendingPoint;
import com.adins.constants.enums.NotificationType;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esignhubjob.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppRequestBean;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsBusinessLine;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsOffice;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.webservice.vfirst.SendSmsVfirstResponse;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

public class SendSignCompleteNotifJob extends BaseJobHandler {

    private static final String KEY_REF_NUM = "refNumber";
    private static final String KEY_REF_NUM_LABEL = "refNumberLabel";
    
    private static final String BALMUT_NOTES_SIGN_COMPLETE_NOTIF_SMS = "%1$s : Send SMS Sign Complete Notification";
    private static final String BALMUT_NOTES_SIGN_COMPLETE_NOTIF_WA = "%1$s : Send WhatsApp Sign Complete Notification";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String input = IOUtils.toString(inputStream);
        context.getLogger().info("Processing id_document_h: " + input);

        Long idDocumentH = Long.valueOf(input);
        TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByIdDocumentHNewTrx(idDocumentH);
        if (null == documentH) {
            context.getLogger().warn("id_document_h " + input + " not found");
            return;
        }

        if (!"1".equals(documentH.getIsActive())) {
            context.getLogger().warn("id_document_h " + input + " is inactive");
            return;
        }

        sendSignCompleteNotifToSigners(documentH, context);
        if ("1".equals(documentH.getIsManualUpload())) {
            sendSignCompleteNotifToDocOwner(documentH, context);
        } else {
            context.getLogger().info(String.format("Tenant %1$s, ref number %2$s does not send notif to doc owner (is_manual_upload != 1)", documentH.getMsTenant().getTenantCode(), documentH.getRefNumber()));
        }
    }

    private void sendSignCompleteNotifToSigners(TrDocumentH documentH, Context context) {
        MsTenant tenant = documentH.getMsTenant();
        if (!sendToSigners(tenant)) {
            context.getLogger().info(tenant.getTenantCode() + " does not send to signers");
            return;
        }

        List<MsVendorRegisteredUser> signers = daoFactory.getVendorRegisteredUserDao().getDocHSigners(documentH);
        for (MsVendorRegisteredUser vendorUser : signers) {
            NotificationType notificationType = logicFactory.getTenantLogic().getNotificationType(tenant, NotificationSendingPoint.SIGN_COMPLETE_NOTIF_JOB, vendorUser.getEmailService());
            sendNotifBasedOnNotifType(documentH, vendorUser, notificationType, false, context);
        }
    }

    private void sendSignCompleteNotifToDocOwner(TrDocumentH documentH, Context context) {
        MsTenant tenant = documentH.getMsTenant();
        if (!sendToDocOwner(tenant)) {
            context.getLogger().info(tenant.getTenantCode() + " does not send to document owner");
            return;
        }

        AmMsuser docOwner = documentH.getAmMsuserByIdMsuserRequestBy();
        MsVendorRegisteredUser vendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserNewTran(docOwner);
        NotificationType notificationType = logicFactory.getTenantLogic().getNotificationType(tenant, NotificationSendingPoint.SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER, vendorRegisteredUser.getEmailService());
        sendNotifBasedOnNotifType(documentH, vendorRegisteredUser, notificationType, true, context);
    }

    private void sendNotifBasedOnNotifType(TrDocumentH documentH, MsVendorRegisteredUser vendorUser, NotificationType notificationType, boolean isDocOwner, Context context) {
        if (NotificationType.EMAIL == notificationType) {
            sendEmailNotif(documentH, vendorUser, isDocOwner, context);
            return;
        }

        if (NotificationType.SMS_JATIS == notificationType) {
            sendSmsJatisNotif(documentH, vendorUser, isDocOwner, context);
            return;
        }

        if (NotificationType.SMS_VFIRST == notificationType) {
            sendSmsVfirstNotif(documentH, vendorUser, isDocOwner, context);
            return;
        }

        if (NotificationType.WHATSAPP == notificationType) {
            sendWhatsAppNotif(documentH, vendorUser, isDocOwner, context);
        }

        if (NotificationType.WHATSAPP_HALOSIS == notificationType) {
            sendHalosisWhatsAppNotif(documentH, vendorUser, isDocOwner, context);
        }
    }

    private void sendEmailNotif(TrDocumentH documentH, MsVendorRegisteredUser vendorUser, boolean isDocOwner, Context context) {
        MsTenant tenant = documentH.getMsTenant();

        Map<String, Object> userParam = new HashMap<>();
        userParam.put("name", vendorUser.getAmMsuser().getFullName());

        Map<String, Object> docParam = new HashMap<>();
        docParam.put(KEY_REF_NUM_LABEL, tenant.getRefNumberLabel());
        docParam.put(KEY_REF_NUM, documentH.getRefNumber());

        Map<String, Object> param = new HashMap<>();
        param.put("user", userParam);
        param.put("doc", docParam);

        String templateCode = isDocOwner ? Constants.TEMPLATE_DOC_OWNER_SIGN_COMPLETE_EMAIL : Constants.TEMPLATE_SIGNER_SIGN_COMPLETE_EMAIL;
        MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(templateCode, param);

        String[] recipient = new String[] {vendorUser.getSignerRegisteredEmail()};
        EmailInformationBean emailInfo = new EmailInformationBean();
        emailInfo.setSubject(template.getSubject());
        emailInfo.setBodyMessage(template.getBody());
        emailInfo.setTo(recipient);

        String phone = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
        List<TrDocumentD> documentDs = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
        MsVendor vendor = documentDs.get(0).getMsVendor();

        String sendingPointCode = isDocOwner ? Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER : Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB;
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);

        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_DOC_SIGN_COMPLETE_NOTIF);

        SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
        auditTrailBean.setPhone(phone);
        auditTrailBean.setEmail(vendorUser.getSignerRegisteredEmail());
        auditTrailBean.setUser(vendorUser.getAmMsuser());
        auditTrailBean.setTenant(tenant);
        auditTrailBean.setVendorPsre(vendor);
        auditTrailBean.setLovSendingPoint(lovSendingPoint);
        auditTrailBean.setLovProcessType(lovProcessType);
        auditTrailBean.setDocumentDs(documentDs);

        logicFactory.getEmailSenderLogic().sendEmail(emailInfo, null, auditTrailBean, context);
    }

    private void sendSmsJatisNotif(TrDocumentH documentH, MsVendorRegisteredUser vendorUser, boolean isDocOwner, Context context) {

        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_SMS))) {
            return;
        }

        if (ArrayUtils.isEmpty(vendorUser.getPhoneBytea())) {
            context.getLogger().info(vendorUser.getSignerRegisteredEmail() + " does not have phone number (skip send SMS)");
            return;
        }

        AmMsuser user = vendorUser.getAmMsuser();
        MsTenant tenant = documentH.getMsTenant();
        MsOffice office = documentH.getMsOffice();
        MsBusinessLine businessLine = documentH.getMsBusinessLine();
        String phoneNumber = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

        Map<String, Object> docParam = new HashMap<>();
        docParam.put(KEY_REF_NUM_LABEL, tenant.getRefNumberLabel());
        docParam.put(KEY_REF_NUM, documentH.getRefNumber());

        Map<String, Object> param = new HashMap<>();
        param.put("doc", docParam);

        String templateCode = isDocOwner ? Constants.TEMPLATE_DOC_OWNER_SIGN_COMPLETE_SMS : Constants.TEMPLATE_SIGNER_SIGN_COMPLETE_SMS;
        MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(templateCode, param);
        List<TrDocumentD> documentDs = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
        MsVendor vendor = documentDs.get(0).getMsVendor();

        String sendingPointCode = isDocOwner ? Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER : Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB;
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);

        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_DOC_SIGN_COMPLETE_NOTIF);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_SMS, tenant, vdr, 1, context);

        JatisSmsRequestBean smsRequestBean = new JatisSmsRequestBean(tenant, office, businessLine, phoneNumber, template.getBody(), trxNo, false);
        String notes = String.format(BALMUT_NOTES_SIGN_COMPLETE_NOTIF_SMS, phoneNumber);

        SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
        auditTrailBean.setPhone(phoneNumber);
        auditTrailBean.setEmail(vendorUser.getSignerRegisteredEmail());
        auditTrailBean.setUser(vendorUser.getAmMsuser());
        auditTrailBean.setTenant(tenant);
        auditTrailBean.setVendorPsre(vendor);
        auditTrailBean.setLovSendingPoint(lovSendingPoint);
        auditTrailBean.setLovProcessType(lovProcessType);
        auditTrailBean.setDocumentDs(documentDs);
        logicFactory.getSmsJatisLogic().sendSmsAndCutBalance(smsRequestBean, documentH, null, user, notes, auditTrailBean, context);
    }

    private void sendSmsVfirstNotif(TrDocumentH documentH, MsVendorRegisteredUser vendorUser, boolean isDocOwner, Context context) {

        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_SMS))) {
            return;
        }

        if (ArrayUtils.isEmpty(vendorUser.getPhoneBytea())) {
            context.getLogger().info(vendorUser.getSignerRegisteredEmail() + " does not have phone number (skip send SMS)");
            return;
        }

        AmMsuser user = vendorUser.getAmMsuser();
        MsTenant tenant = documentH.getMsTenant();
        MsOffice office = documentH.getMsOffice();
        MsBusinessLine businessLine = documentH.getMsBusinessLine();
        String phoneNumber = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        
        List<TrDocumentD> documentDs = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
        MsVendor vendorPsre = documentDs.get(0).getMsVendor();

        String sendingPointCode = isDocOwner ? Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER : Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB;
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);

        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_DOC_SIGN_COMPLETE_NOTIF);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_SMS, tenant, vdr, 1, context);

        Map<String, Object> docParam = new HashMap<>();
        docParam.put(KEY_REF_NUM_LABEL, tenant.getRefNumberLabel());
        docParam.put(KEY_REF_NUM, documentH.getRefNumber());

        Map<String, Object> param = new HashMap<>();
        param.put("doc", docParam);

        SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
        auditTrailBean.setPhone(phoneNumber);
        auditTrailBean.setEmail(vendorUser.getSignerRegisteredEmail());
        auditTrailBean.setUser(vendorUser.getAmMsuser());
        auditTrailBean.setTenant(tenant);
        auditTrailBean.setVendorPsre(vendorPsre);
        auditTrailBean.setLovSendingPoint(lovSendingPoint);
        auditTrailBean.setLovProcessType(lovProcessType);
        auditTrailBean.setDocumentDs(documentDs);

        String templateCode = isDocOwner ? Constants.TEMPLATE_DOC_OWNER_SIGN_COMPLETE_SMS : Constants.TEMPLATE_SIGNER_SIGN_COMPLETE_SMS;
        MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(templateCode, param);
        SendSmsVfirstResponse response = logicFactory.getSmsVfirstLogic().sendSms(tenant, phoneNumber, template.getBody(), auditTrailBean, context);
        String notes = String.format(BALMUT_NOTES_SIGN_COMPLETE_NOTIF_SMS, phoneNumber);

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SMS);
        MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USMS);
        MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation mutation = new TrBalanceMutation();
        mutation.setAmMsuser(user);
        mutation.setMsLovByLovBalanceType(balanceType);
        mutation.setMsLovByLovTrxType(trxType);
        mutation.setMsTenant(tenant);
        mutation.setMsVendor(vendor);
        mutation.setTrDocumentH(documentH);
        mutation.setTrxNo(trxNo);
        mutation.setTrxDate(new Date());
        mutation.setVendorTrxNo(response.getGuid());
        mutation.setMsBusinessLine(businessLine);
        mutation.setMsOffice(office);
        mutation.setUsrCrt(context.getRequestId());
        mutation.setDtmCrt(new Date());

        if (response.getErrorCode() == null || (!"28682".equals(response.getErrorCode()) && !"28681".equals(response.getErrorCode()))) {
            mutation.setQty(-1);
            mutation.setNotes(notes);
        } else {
            mutation.setQty(0);
            mutation.setNotes(notes + " error");
        }

        daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
    }

    private void sendWhatsAppNotif(TrDocumentH documentH, MsVendorRegisteredUser vendorUser, boolean isDocOwner, Context context) {

        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_WA))) {
            return;
        }

        if (ArrayUtils.isEmpty(vendorUser.getPhoneBytea())) {
            context.getLogger().info(vendorUser.getSignerRegisteredEmail() + " does not have phone number (skip send WA)");
            return;
        }

        String templateCode = isDocOwner ? Constants.TEMPLATE_DOC_OWNER_SIGN_COMPLETE_WA : Constants.TEMPLATE_SIGNER_SIGN_COMPLETE_WA;

        Map<String, Object> docParam = new HashMap<>();
        docParam.put(KEY_REF_NUM, documentH.getRefNumber());
        Map<String, Object> param = new HashMap<>();
        param.put("doc", docParam);

        MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(templateCode, param);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String phoneNumber = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());

        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(vendorUser.getAmMsuser().getFullName());
        bodyTexts.add(documentH.getMsTenant().getRefNumberLabel());
        bodyTexts.add(documentH.getRefNumber());

        List<TrDocumentD> documentDs = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
        MsVendor vendorPsre = documentDs.get(0).getMsVendor();

        String sendingPointCode = isDocOwner ? Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER : Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB;
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);

        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_DOC_SIGN_COMPLETE_NOTIF);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_WA, documentH.getMsTenant(), vdr, 1, context);

        SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
        auditTrailBean.setPhone(phoneNumber);
        auditTrailBean.setEmail(vendorUser.getSignerRegisteredEmail());
        auditTrailBean.setUser(vendorUser.getAmMsuser());
        auditTrailBean.setTenant(documentH.getMsTenant());
        auditTrailBean.setVendorPsre(vendorPsre);
        auditTrailBean.setLovSendingPoint(lovSendingPoint);
        auditTrailBean.setLovProcessType(lovProcessType);
        auditTrailBean.setDocumentDs(documentDs);

        JatisWhatsAppRequestBean requestBean = new JatisWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phoneNumber);
        requestBean.setAmMsuser(vendorUser.getAmMsuser());
        requestBean.setTrDocumentH(documentH);
        requestBean.setMsBusinessLine(documentH.getMsBusinessLine());
        requestBean.setMsOffice(documentH.getMsOffice());
        requestBean.setMsTenant(documentH.getMsTenant());
        requestBean.setRemoveHeader(false);
        requestBean.setNotes(String.format(BALMUT_NOTES_SIGN_COMPLETE_NOTIF_WA, phoneNumber));
        requestBean.setIsOtp(false);
        logicFactory.getWhatsAppJatisLogic().sendMessageAndCutBalance(requestBean, auditTrailBean, context);

    }

    private void sendHalosisWhatsAppNotif(TrDocumentH documentH, MsVendorRegisteredUser vendorUser, boolean isDocOwner, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_WA))) {
            return;
        }

        if (ArrayUtils.isEmpty(vendorUser.getPhoneBytea())) {
            context.getLogger().info(vendorUser.getSignerRegisteredEmail() + " does not have phone number (skip send WA Halosis)");
            return;
        }

        String templateCode = isDocOwner ? Constants.TEMPLATE_DOC_OWNER_SIGN_COMPLETE_WA : Constants.TEMPLATE_SIGNER_SIGN_COMPLETE_WA;
        MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(Constants.TEMPLATE_TYPE_WHATSAPP, templateCode);
        String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
        String phoneNumber = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());

        List<String> headerTexts = new ArrayList<>();
        headerTexts.add(documentH.getRefNumber());

        List<String> bodyTexts = new ArrayList<>();
        bodyTexts.add(vendorUser.getAmMsuser().getFullName());
        bodyTexts.add(documentH.getMsTenant().getRefNumberLabel());
        bodyTexts.add(documentH.getRefNumber());

        List<TrDocumentD> documentDs = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
        MsVendor vendorPsre = documentDs.get(0).getMsVendor();

        String sendingPointCode = isDocOwner ? Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER : Constants.NOTIF_SENDING_POINT_SIGN_COMPLETE_NOTIF_JOB;
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);

        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_DOC_SIGN_COMPLETE_NOTIF);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_WA, documentH.getMsTenant(), vdr, 1, context);

        SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
        auditTrailBean.setPhone(phoneNumber);
        auditTrailBean.setEmail(vendorUser.getSignerRegisteredEmail());
        auditTrailBean.setUser(vendorUser.getAmMsuser());
        auditTrailBean.setTenant(documentH.getMsTenant());
        auditTrailBean.setVendorPsre(vendorPsre);
        auditTrailBean.setLovSendingPoint(lovSendingPoint);
        auditTrailBean.setLovProcessType(lovProcessType);
        auditTrailBean.setDocumentDs(documentDs);

        HalosisSendWhatsAppRequestBean requestBean = new HalosisSendWhatsAppRequestBean();
        requestBean.setTemplate(template);
        requestBean.setHeaderTexts(headerTexts);
        requestBean.setBodyTexts(bodyTexts);
        requestBean.setReservedTrxNo(trxNo);
        requestBean.setPhoneNumber(phoneNumber);
        requestBean.setAmMsuser(vendorUser.getAmMsuser());
        requestBean.setTrDocumentH(documentH);
        requestBean.setMsBusinessLine(documentH.getMsBusinessLine());
        requestBean.setMsOffice(documentH.getMsOffice());
        requestBean.setMsTenant(documentH.getMsTenant());
        requestBean.setNotes(String.format(BALMUT_NOTES_SIGN_COMPLETE_NOTIF_WA, phoneNumber));
        requestBean.setIsOtp(false);
        logicFactory.getWhatsAppHalosisLogic().sendMessage(requestBean, auditTrailBean, context);
    }

    private boolean sendToSigners(MsTenant tenant) {
        MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, "SEND_COMPLETE_SIGN_NOTIF_TO_SIGNERS");
        if (null == settings) {
            return false;
        }

        return "1".equals(settings.getSettingValue());
    }

    private boolean sendToDocOwner(MsTenant tenant) {
        MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, "SEND_COMPLETE_SIGN_NOTIF_TO_DOC_OWNER");
        if (null == settings) {
            return false;
        }

        return "1".equals(settings.getSettingValue());
    }
    
}
