package com.adins.esignhubjob.dataaccess.api;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;

public interface BalanceMutationDao {
    void insertBalanceMutation(TrBalanceMutation balanceMutation);
    void insertBalanceMutationNewTran(TrBalanceMutation balanceMutation);
    void updateBalanceMutationNewTran(TrBalanceMutation balanceMutation);
    void deleteBalanceMutationNewTran(MsTenant tenant, MsVendor vendor, MsLov lovBalanceType, MsLov lovTrxType, Date startTime, Date endTime);
    TrBalanceMutation getBalanceMutationByTrxNo(String trxNo);
    TrBalanceMutation getBalanceMutationByVendorTrxNo(String vendorTrxNo);
    TrBalanceMutation getBalanceMutationByVendorTrxNoNewTran(String vendorTrxNo);
    TrBalanceMutation getUsersLatestTrx(long idMsUser);
    TrBalanceMutation getBalanceMutationById(long idBalanceMutation);

    // Check current balance
    BigInteger getCurrentBalance(MsTenant tenant, MsVendor vendor, MsLov lovBalanceType);
	BigInteger getSingleBalanceByVendorAndTenant(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov);
    List<TrBalanceMutation> getListBalanceMutationDailyRecapTopUpByDate (MsTenant tenant,MsVendor vendor, MsLov balanceType , String date);
    void insertBalanceMutationNativeString(long idBalancemutation, long idTopUp, String usrUpd, String notes, int qty);
    
    List<TrBalanceMutation> getListBalanceMutationDailyRecapTopUpRecountByDate (MsTenant tenant,MsVendor vendor, MsLov balanceType , String date);

    // Get Data Check Balance Expired
    TrBalanceMutation getVendorTenantLovByIdBalMut(Long idBalMut);

    // For daily recap
    List<Map<String, Object>> getListMutationForTopupBinding(MsTenant tenant, MsVendor vendor, MsLov lovBalanceType, Date trxDate, long availableBalance);
    void updateBalanceMutationIdBalanceTopup(List<Object> idBalanceMutations, long idBalanceToup, String usrUpd);
    
}
