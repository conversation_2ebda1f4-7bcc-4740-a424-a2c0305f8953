package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SmsJatisLogic;
import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;
import com.adins.esignhubjob.model.webservice.jatis.JatisSmsResponse;
import com.adins.exceptions.StatusCode;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;

@Component
public class GenericSmsJatisLogic extends BaseLogic implements SmsJatisLogic {

	@Autowired private PersonalDataEncryptionLogic encryptionLogic;

    @Override
    public void sendSmsAndCutBalance(JatisSmsRequestBean request, TrDocumentH documentH, TrDocumentD document, AmMsuser user, String notes, Context context) {
        JatisSmsResponse response = sendSms(request, context);
		if (!"1".equals(response.getStatus())) {
			return;
		}

		String balanceTypeCode = request.isOtpSms() ? Constants.BALANCE_TYPE_CODE_OTP : Constants.BALANCE_TYPE_CODE_SMS;
		String trxTypeCode = request.isOtpSms() ? Constants.TRX_TYPE_CODE_UOTP : Constants.TRX_TYPE_CODE_USMS;

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);
		MsLov lovBalanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);
		MsLov lovTrxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, trxTypeCode);

		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getTrxNo());
		mutation.setTrxDate(new Date());
		mutation.setQty(-1);
		mutation.setMsLovByLovTrxType(lovTrxType);
		mutation.setMsLovByLovBalanceType(lovBalanceType);
		mutation.setUsrCrt(context.getRequestId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(request.getTenant());
		mutation.setMsVendor(vendor);
		mutation.setVendorTrxNo(response.getMessageId());
		mutation.setNotes(StringUtils.isBlank(notes) ? "Send SMS to " + request.getPhoneNumber() : notes);

		if (null != document) {
			mutation.setTrDocumentD(document);
		}
		
		if (null != documentH) {
			mutation.setRefNo(documentH.getRefNumber());
			mutation.setTrDocumentH(documentH);
		}
		
		if (null != user) {
			mutation.setAmMsuser(user);
		}
		
		if (null != request.getOffice()) {
			mutation.setMsOffice(request.getOffice());
		}
		
		if (null != request.getBusinessLine()) {
			mutation.setMsBusinessLine(request.getBusinessLine());
		}
		
		if (StringUtils.isNotBlank(request.getRefNo()) && null == documentH) {
			mutation.setRefNo(request.getRefNo());
		}

		daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
    }

	private void logSmsRequest(MsTenant tenant, RequestBody body, Context context) {
		Buffer buffer = new Buffer();
		try {
			body.writeTo(buffer);
			String requestBodyString = buffer.readUtf8();
			context.getLogger().info(String.format("Send %1$s SMS Jatis request: %2$s", tenant.getTenantCode(), requestBodyString));
		} catch (Exception e) {
			context.getLogger().error("Failed to log Jatis SMS request: " + e.getLocalizedMessage());
			context.getLogger().error(ExceptionUtils.getStackTrace(e));
		}
	}

    private JatisSmsResponse sendSms(JatisSmsRequestBean request, Context context) {

        MsTenant tenant = request.getTenant();
        String phoneNumber = request.getPhoneNumber();
        String smsMessage = request.getSmsMessage();
        boolean isSmsOtp = request.isOtpSms();
        String batchname = "SMS eSignHub " + tenant.getTenantCode();
        String formattedPhone = Tools.changePrefixTo62(phoneNumber);

        String id = null;
		String password = null;
		String sender = null;
		String division = null;

        MsTenantSettings tsId = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_SMS_JATIS_ID);
		MsTenantSettings tsPass = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_SMS_JATIS_PASSWORD);
		MsTenantSettings tsSender = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_SMS_JATIS_SENDER);
		MsTenantSettings tsDivision = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_SMS_JATIS_DIVISION);
		
		if (null != tsId && StringUtils.isNotBlank(tsId.getSettingValue()) && 
				null != tsPass && StringUtils.isNotBlank(tsPass.getSettingValue()) && 
				null != tsSender && StringUtils.isNotBlank(tsSender.getSettingValue()) && 
				null != tsDivision && StringUtils.isNotBlank(tsDivision.getSettingValue())) {
			id = tsId.getSettingValue();
			password = tsPass.getSettingValue();
			sender = tsSender.getSettingValue();
			division = tsDivision.getSettingValue();
		} else {
			id = System.getenv(Constants.ENV_VAR_JATIS_SMS_ID);
			password = System.getenv(Constants.ENV_VAR_JATIS_SMS_PASSWORD);
			sender = System.getenv(Constants.ENV_VAR_JATIS_SMS_SENDER);
			division = System.getenv(Constants.ENV_VAR_JATIS_SMS_DIVISION);
		}

		RequestBody formBody = new FormBody.Builder()
			.add("userid", id)
			.add("password", password)
			.add("msisdn", formattedPhone)
			.add("message", smsMessage)
			.add("sender", sender)
			.add("division", division)
			.add("batchname", batchname)
			.add("uploadby", "ESIGNHUB SYSTEM")
			.add("channel", isSmsOtp ? "2" : "0")
			.build();

		RequestBody logFormBody = new FormBody.Builder()
			.add("msisdn", formattedPhone)
			.add("message", smsMessage)
			.add("sender", sender)
			.add("division", division)
			.add("batchname", batchname)
			.add("uploadby", "ESIGNHUB SYSTEM")
			.add("channel", isSmsOtp ? "2" : "0")
			.build();
		
		logSmsRequest(tenant, logFormBody, context);

		Request okHttpRequest = new Request.Builder()
			.url(System.getenv(Constants.ENV_VAR_JATIS_SMS_URL))
			.post(formBody)
			.build();

		OkHttpClient client = new OkHttpClient.Builder()
			.connectTimeout(10L, TimeUnit.SECONDS)
			.readTimeout(30L, TimeUnit.SECONDS)
			.build();

		try {

			Date startTime = new Date();
			Response okHttpResponse = client.newCall(okHttpRequest).execute();
			Date finishTime = new Date();
			logProcessDuration("Send " + tenant.getTenantCode() + " Jatis SMS", startTime, finishTime, context);

			String rawResponse = okHttpResponse.body().string();
			context.getLogger().info(String.format("Send %1$s SMS Jatis response: %2$s", tenant.getTenantCode(), rawResponse));
			return JatisSmsResponse.parseString(rawResponse);

		} catch (Exception e) {

			context.getLogger().error(String.format("Send %1$s SMS Jatis exception: %2$s", tenant.getTenantCode(), e.getLocalizedMessage()));
			context.getLogger().error(ExceptionUtils.getStackTrace(e));

			JatisSmsResponse jatisResponse = new JatisSmsResponse();
			jatisResponse.setStatus(String.valueOf(StatusCode.UNKNOWN));
			jatisResponse.setMessageId("Unknown system error");
			return jatisResponse;

		}
    }

	@Override
	public void sendSmsAndCutBalance(JatisSmsRequestBean request, TrDocumentH documentH, TrDocumentD document, AmMsuser user, String notes, SigningProcessAuditTrailBean auditTrail, Context context) {
		JatisSmsResponse response = sendSms(request, context);
		if (!"1".equals(response.getStatus())) {
			insertAuditTrail(auditTrail, false, response.getMessageId(), context);
			return;
		}

		String balanceTypeCode = request.isOtpSms() ? Constants.BALANCE_TYPE_CODE_OTP : Constants.BALANCE_TYPE_CODE_SMS;
		String trxTypeCode = request.isOtpSms() ? Constants.TRX_TYPE_CODE_UOTP : Constants.TRX_TYPE_CODE_USMS;

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);
		MsLov lovBalanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);
		MsLov lovTrxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, trxTypeCode);

		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getTrxNo());
		mutation.setTrxDate(new Date());
		mutation.setQty(-1);
		mutation.setMsLovByLovTrxType(lovTrxType);
		mutation.setMsLovByLovBalanceType(lovBalanceType);
		mutation.setUsrCrt(context.getRequestId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(request.getTenant());
		mutation.setMsVendor(vendor);
		mutation.setVendorTrxNo(response.getMessageId());
		mutation.setNotes(StringUtils.isBlank(notes) ? "Send SMS to " + request.getPhoneNumber() : notes);

		if (null != document) {
			mutation.setTrDocumentD(document);
		}
		
		if (null != documentH) {
			mutation.setRefNo(documentH.getRefNumber());
			mutation.setTrDocumentH(documentH);
		}
		
		if (null != user) {
			mutation.setAmMsuser(user);
		}
		
		if (null != request.getOffice()) {
			mutation.setMsOffice(request.getOffice());
		}
		
		if (null != request.getBusinessLine()) {
			mutation.setMsBusinessLine(request.getBusinessLine());
		}
		
		if (StringUtils.isNotBlank(request.getRefNo()) && null == documentH) {
			mutation.setRefNo(request.getRefNo());
		}

		daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);

		insertAuditTrail(auditTrail, true, null, context);
	}

	private TrSigningProcessAuditTrail insertAuditTrail(SigningProcessAuditTrailBean auditTrailBean, boolean sendSuccess, String notes, Context context) {
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(encryptionLogic.encryptFromString(auditTrailBean.getPhone()));
		trail.setHashedPhoneNo(Tools.getHashedString(auditTrailBean.getPhone()));
		trail.setEmail(StringUtils.upperCase(auditTrailBean.getEmail()));
		trail.setAmMsUser(auditTrailBean.getUser());
		trail.setMsTenant(auditTrailBean.getTenant());
		trail.setMsVendor(auditTrailBean.getVendorPsre());
		trail.setNotificationMedia("SMS");
		trail.setNotificationVendor(daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SMS_GATEWAY, Constants.CODE_LOV_SMS_GATEWAY_JATIS).getDescription());
		trail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		trail.setLovProcessType(auditTrailBean.getLovProcessType());
		trail.setResultStatus(sendSuccess ? "1" : "0");
		trail.setNotes(notes);
		trail.setUsrCrt(context.getRequestId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

		if (CollectionUtils.isEmpty(auditTrailBean.getDocumentDs())) {
			return trail;
		}

		for (TrDocumentD documentD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail detail = new TrSigningProcessAuditTrailDetail();
			detail.setSigningProcessAuditTrail(trail);
			detail.setTrDocumentD(documentD);
			detail.setUsrCrt(context.getRequestId());
			detail.setDtmCrt(new Date());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTrx(detail);
		}

		return trail;
	}
    
}
