package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.util.Date;
import java.util.List;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.TrProcessAutosignBmH;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.aliyun.fc.runtime.Context;
import com.aliyuncs.fc.client.FunctionComputeClient;
import com.aliyuncs.fc.constants.Const;
import com.aliyuncs.fc.request.InvokeFunctionRequest;
import com.aliyuncs.fc.response.InvokeFunctionResponse;

public class ProcessImportAutosignDataJob extends BaseJobHandler {

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        Date schedulerStartTime = new Date();
        List<TrProcessAutosignBmH> processAutosignBmHs = daoFactory.getProcessAutosignBmDao().getProcessAutosignBmHs("0", "NEXT DAY");
        context.getLogger().info("Processing " + processAutosignBmHs.size() + " tr_process_autosign_bm_h(s)");

        for (TrProcessAutosignBmH processAutosignBmH : processAutosignBmHs) {
            String region = System.getenv(Constants.ENV_VAR_ALICLOUD_REGION);
            String uid = System.getenv(Constants.ENV_VAR_ALICLOUD_UID);
            String accessKeyId = System.getenv(Constants.ENV_OSS_ACCESSKEYID);
            String accessKeySecret = System.getenv(Constants.ENV_OSS_ACCESSKEYSECRET);
            String serviceName = System.getenv(Constants.ENV_VAR_ALICLOUD_SERVICE_NAME);
            String functionName = "process_autosign_data";
            String payload = String.valueOf(processAutosignBmH.getIdProcessAutosignBmH());

            InvokeFunctionRequest invReq = new InvokeFunctionRequest(serviceName, functionName);
            invReq.setInvocationType(Const.INVOCATION_TYPE_ASYNC);
            invReq.setPayload(payload.getBytes());

            FunctionComputeClient fcClient = new FunctionComputeClient(region, uid, accessKeyId, accessKeySecret);
            InvokeFunctionResponse invRes = fcClient.invokeFunction(invReq);

            if (HttpURLConnection.HTTP_ACCEPTED == invRes.getStatus()) {
                context.getLogger().info("Asynchronous invocation accepted, request ID: " + invRes.getRequestId());
            } else {
                context.getLogger().warn("Asynchronous invocation failed");
		    }
        }
        
        Date schedulerFinishTime = new Date();

        MsLov schedulerType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.LOV_CODE_SCHEDULER_DAILY);
        MsLov jobType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_JOB_TYPE, Constants.CODE_LOV_JOB_TYPE_PROCESS_IMPORT_AUTOSIGN_BM);

        TrSchedulerJob schedulerJob = new TrSchedulerJob();
        schedulerJob.setSchedulerStart(schedulerStartTime);
        schedulerJob.setSchedulerEnd(schedulerFinishTime);
        schedulerJob.setMsLovBySchedulerType(schedulerType);
        schedulerJob.setMsLovByJobType(jobType);
        schedulerJob.setDataProcessed((long) processAutosignBmHs.size());
        schedulerJob.setUsrCrt(context.getRequestId());
        schedulerJob.setDtmCrt(new Date());
        schedulerJob.setMailReminderCount((short) 0);
        daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
    }
    
}
