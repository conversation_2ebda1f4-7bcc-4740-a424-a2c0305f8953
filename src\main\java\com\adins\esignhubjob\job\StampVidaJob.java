package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.AuditDataType;
import com.adins.esignhubjob.model.custom.adins.StampingErrorDetailBean;
import com.adins.esignhubjob.model.custom.adins.VidaStampingTokenBean;
import com.adins.esignhubjob.model.custom.client.womf.WomfUploadDocumentRequestBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrStampDuty;
import com.adins.esignhubjob.model.webservice.adins.ClientDocumentUploadRequest;
import com.adins.esignhubjob.model.webservice.adins.ClientDocumentUploadResponse;
import com.adins.esignhubjob.model.webservice.client.cfi.CfiUploadDocumentRequest;
import com.adins.esignhubjob.model.webservice.client.cfi.CfiUploadDocumentResponse;
import com.adins.esignhubjob.model.webservice.client.womf.WomfUploadDocumentRequest;
import com.adins.esignhubjob.model.webservice.vida.VidaCheckStampStatusResponse;
import com.adins.esignhubjob.model.webservice.vida.VidaStampResponse;
import com.adins.exceptions.EsignhubJobException;
import com.adins.exceptions.Status;
import com.adins.util.IOUtils;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class StampVidaJob extends BaseJobHandler {

    // Lokasi error stamp Privy (Untuk keperluan kirim email notifikasi error)
    private static final String ERR_LOC_VIDA_LOG_ON = "Vida Log On";
    private static final String ERR_LOC_UPL_DOC = "Upload Base Document";
    private static final String ERR_LOC_STM_SDT = "Verify Stamp Result";
    private static final String ERR_LOC_UPL_CON = "Upload Stamped Document";
    private static final String ERR_LOC_FINAL_VAL = "Final Validation";

    // Detail error stamp Privy (Untuk keperluan kirim email notifikasi error)
    private static final String ERR_LOC_DETAIL_VALIDATION = "Failed Validation";
    private static final String ERR_LOC_DETAIL_EXCEPTION = "Exception Occurred";
    private static final String ERR_LOC_DETAIL_UPL_BASE_DOC = "Failed to Upload Base Document";
    private static final String ERR_LOC_DETAIL_CHECK_STATUS_FAILED = "Failed to Validate Stamp Result";
    private static final String ERR_LOC_DETAIL_UPL_CLIENT = "Failed to Upload Stamped Document";

    // Error message stamp Privy (Untuk keperluan kirim email notifikasi error)
    private static final String ERR_MSG_FAIL_RESPONSE =  "Failed Response";
    
    private static final String DUMMY_BASE64_CONTENT = "base64Document";

    private static final String GS_CODE_VIDA_CHECK_STAMP_STATUS_ITERATION = "VIDA_CHECK_STAMP_STATUS_ITERATION";
    private static final String GS_CODE_VIDA_CHECK_STAMP_STATUS_WAIT_TIME = "VIDA_CHECK_STAMP_STATUS_WAIT_TIME";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {
        String input = IOUtils.toString(inputStream);
        context.getLogger().info("Stamp VIDA for id_document_h: " + input);
        
        Long idDocumentH = Long.valueOf(input);
        TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByIdDocumentH(idDocumentH);
        if (null == documentH) {
            context.getLogger().error("Stamp VIDA: ID " + input + " not found");
            return;
        } 

        if (!"1".equals(documentH.getIsActive()) || !Short.valueOf("74").equals(documentH.getProsesMaterai())) {
            context.getLogger().error(String.format("Tenant %1$s, Kontrak %2$s flagged as failed stamp, is_active status: %3$s, proses_materai status: %4$s", documentH.getMsTenant().getTenantCode(), documentH.getRefNumber(), documentH.getIsActive(), documentH.getProsesMaterai()));
            logicFactory.getCommonStampingLogic().updateDocumentHMeteraiProcess(documentH, "71", context);
            return;
        }

        doVidaStamping(documentH, context);
    }

    private void doVidaStamping(TrDocumentH documentH, Context context) throws IOException {
        logicFactory.getCommonStampingLogic().updateDocumentHMeteraiProcess(documentH, "75", context);

        MsTenant tenant = documentH.getMsTenant();
        
        List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, processing %3$s documents(s)", tenant.getTenantCode(), documentH.getRefNumber(), documents.size()));

        VidaStampingTokenBean token = new VidaStampingTokenBean();
        for (TrDocumentD document : documents) {
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, stamping process: %4$s/%5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), document.getTotalStamping(), document.getTotalMaterai()));

            if (null == document.getTotalMaterai() || 0 == document.getTotalMaterai()) {
                logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_SDT_FIN, context);
                continue;
            }

            if (Constants.STEP_STAMPING_SDT_FIN.equals(document.getSdtProcess())) {
                context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, already stamped.", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
                continue;
            }
            
            if (Constants.STEP_STAMPING_NOT_STR.equals(document.getSdtProcess())) {
                logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_UPL_DOC, context);
            }

            List<TrDocumentDStampduty> documentDStampduties = daoFactory.getDocumentDao().getNotStampedDocumentDStampdutiesNewTrx(document);

            for (TrDocumentDStampduty documentStampduty : documentDStampduties) {
                token = generateTokenVida(token, documentH, document, context);
                if (!token.isSuccessful()) {
                    return;
                }

                String reservedTrxNo = StringUtils.isNotBlank(documentStampduty.getReservedTrxNo()) ? documentStampduty.getReservedTrxNo() : String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

                if (Constants.STEP_STAMPING_UPL_DOC.equals(document.getSdtProcess())) {
                    boolean isSuccessful = uploadStampingDocument(documentStampduty, document, token, reservedTrxNo, context);
                    if (!isSuccessful) {
                        return;
                    }
                }

                token = generateTokenVida(token, documentH, document, context);
                if (!token.isSuccessful()) {
                    return;
                }

                if (Constants.STEP_STAMPING_STM_SDT.equals(document.getSdtProcess())) {
                    boolean isSuccessful = checkDocumentStampStatus(document, documentStampduty, reservedTrxNo, token.getAccessToken(), context);
                    if (!isSuccessful) {
                        return;
                    }
                }
            }

            if (Constants.STEP_STAMPING_UPL_CON.equals(document.getSdtProcess())) {
                boolean isSuccessful = uploadStampedDocument(document, context);
                if (!isSuccessful) {
                    return;
                }
            }
            
        }

        if (logicFactory.getCommonStampingLogic().allDocumentsProcessed(documents, context)) {
            documentH.setCallbackProcess((short) 901);
            documentH.setProsesMaterai((short) 73);
            documentH.setDtmUpd(new Date());
			documentH.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
            daoFactory.getDocumentDao().updateDocumentHeaderNewTran(documentH);
            return;
        }

        StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
        errorDetailBean.setErrorLocation(ERR_LOC_FINAL_VAL);
        errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_VALIDATION);
        errorDetailBean.setErrorMessage("Unreachable process");
        logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountVida(documentH, null, errorDetailBean, context);
    }

    private VidaStampingTokenBean generateTokenVida(VidaStampingTokenBean token, TrDocumentH documentH, TrDocumentD document, Context context) throws IOException {
        MsTenant tenant = document.getMsTenant();
        long diff = null == token.getAccessTokenGenTime() ? 300000L : new Date().getTime() - token.getAccessTokenGenTime().getTime();
        if (TimeUnit.MILLISECONDS.toMinutes(diff) >= 4) {
            try {
                String accessToken = logicFactory.getVidaLogic().vidaLogOn(context);
                Date accessTokenGenTime = new Date();
                context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, generate new Token", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
                token.setAccessToken(accessToken);
                token.setAccessTokenGenTime(accessTokenGenTime);
                token.setSuccessful(true);
            } catch (Exception e) {
                context.getLogger().error(ExceptionUtils.getStackTrace(e));

                StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
                errorDetailBean.setErrorLocation(ERR_LOC_VIDA_LOG_ON);
                errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_EXCEPTION);
                errorDetailBean.setErrorMessage(e.getLocalizedMessage());
                errorDetailBean.setException(e);
                logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountVida(documentH, document, errorDetailBean, context);
                
                token.setAccessToken(null);
                token.setAccessTokenGenTime(null);
                token.setSuccessful(false);
                return token;
            }
        }
        
        return token;
    }
    
    /**
     * 
     * @return <code>true</code> if upload process to VIDA is successful. Else, <code>false</code>.
     * @throws IOException 
     */
    private boolean uploadStampingDocument(TrDocumentDStampduty documentDStampduty, TrDocumentD document, VidaStampingTokenBean token, String reservedTrxNo, Context context) throws IOException {
        TrDocumentH documentH = document.getTrDocumentH();
        boolean enoughBalance = logicFactory.getCommonStampingLogic().enoughSdtBalance(document, 1, context);
        if (!enoughBalance) {
            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_UPL_DOC);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_VALIDATION);
            errorDetailBean.setErrorMessage("Insufficient Balance");

            // tidak insert reserved trx no karena belum insert balance mutation
            logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountVida(documentH, document, errorDetailBean, context);
            return false;
        }

        MsTenant tenant = document.getMsTenant();

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SDT);
        MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USDT);
        MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        documentDStampduty.setStartStampProcess(new Date());
        documentDStampduty.setUsrUpd(context.getRequestId());
        documentDStampduty.setDtmUpd(new Date());
        daoFactory.getDocumentDao().updateDocumentDStampdutyNewTrx(documentDStampduty);

        TrBalanceMutation mutation = new TrBalanceMutation();
        mutation.setTrxNo(reservedTrxNo);
        mutation.setTrxDate(new Date());
        mutation.setRefNo(documentH.getRefNumber());
        mutation.setQty(0);
        mutation.setMsLovByLovBalanceType(balanceType);
        mutation.setMsLovByLovTrxType(trxType);
        mutation.setMsTenant(tenant);
        mutation.setMsVendor(vendor);
        mutation.setTrDocumentD(document);
        mutation.setTrDocumentH(documentH);
        mutation.setNotes("Processing stamp " + reservedTrxNo + StringUtils.remove(document.getDocumentId(), '-'));
        mutation.setDtmCrt(new Date());
        mutation.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
        mutation.setMsOffice(documentH.getMsOffice());
        mutation.setMsBusinessLine(documentH.getMsBusinessLine());
        daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);

        try {
            VidaStampResponse response = logicFactory.getVidaLogic().uploadStampVida(token.getAccessToken(), document, documentDStampduty, reservedTrxNo, context);
            if (200 != response.getResponseCode()) {
                context.getLogger().error(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, stamp failed (upload document failed)", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
    
                mutation.setNotes("Upload failed " + reservedTrxNo + StringUtils.remove(document.getDocumentId(), '-'));
                mutation.setDtmUpd(new Date());
                mutation.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
                daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(mutation);
                    
                StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
                errorDetailBean.setErrorLocation(ERR_LOC_UPL_DOC);
                errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_UPL_BASE_DOC);
                errorDetailBean.setErrorMessage(ERR_MSG_FAIL_RESPONSE);
                errorDetailBean.setJsonResponse(gson.toJson(response));

                //tidak insert reserved trx no agar pakai trx no baru menghindari error di vida
                logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountVida(documentH, document, errorDetailBean, context);
                    
                return false;
            }

            mutation.setNotes("Upload success " + reservedTrxNo + StringUtils.remove(document.getDocumentId(), '-'));
            mutation.setDtmUpd(new Date());
            mutation.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
            daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(mutation);

            documentDStampduty.setReservedTrxNo(reservedTrxNo);
            daoFactory.getDocumentDao().updateDocumentDStampdutyNewTrx(documentDStampduty);

            logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_STM_SDT, context);

            return true;    
        } catch (Exception e) {
            context.getLogger().error(ExceptionUtils.getStackTrace(e));

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_UPL_DOC);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_EXCEPTION);
            errorDetailBean.setErrorMessage(e.getLocalizedMessage());
            errorDetailBean.setException(e);
            logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountVida(documentH, document, errorDetailBean, context);
            return false;
        }
    }

    /**
     * @return <code>true</code> if document is successfully stamped by PRIVY. Else, <code>false</code>.
     */
    private boolean checkDocumentStampStatus(TrDocumentD document, TrDocumentDStampduty documentDStampduty, String reservedTrxNo, String accessToken, Context context) {

        TrDocumentH documentH = document.getTrDocumentH();
        TrBalanceMutation mutation = daoFactory.getBalanceMutationDao().getBalanceMutationByTrxNo(reservedTrxNo);

        StampingErrorDetailBean errorDetail = checkStampStatus(document, reservedTrxNo, accessToken, context);
        boolean isStampSuccessful = StringUtils.isBlank(errorDetail.getErrorLocation());
        
        mutation.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
        mutation.setDtmUpd(new Date());
        
        if (!isStampSuccessful) {
            mutation.setQty(0);
            daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(mutation);

            logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountVida(documentH, document, errorDetail, context);
            return false;
        }
        
        TrStampDuty stampDuty = daoFactory.getStampDutyDao().getStampDutyByTrxNo(reservedTrxNo);
        mutation.setNotes(stampDuty.getStampDutyNo());
        mutation.setTrStampDuty(stampDuty);
        mutation.setQty(-1);
        daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(mutation);
        
        short stamped = document.getTotalStamping();
        stamped += 1;
        
        context.getLogger().info(String.format("Document %1$s Old Total Stamping : %2$s, New Total Stamping : %3$s", document.getDocumentId(), document.getTotalStamping(), stamped));
        document.setTotalStamping(stamped);
        document.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
        document.setDtmUpd(new Date());
        daoFactory.getDocumentDao().updateDocumentDNewTran(document);
        
        documentDStampduty.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
        documentDStampduty.setDtmUpd(new Date());
        documentDStampduty.setStampingDate(new Date());
        daoFactory.getDocumentDao().updateDocumentDStampdutyNewTrx(documentDStampduty);
        
        if (document.getTotalMaterai().equals(document.getTotalStamping())) {
            logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_UPL_CON, context);
        } else {
            logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_UPL_DOC, context);
        }
        
        return true;
    }

    /**
     * @return <code>true</code> if document is successfully stamped. Else, <code>false</code>.
     */
    private StampingErrorDetailBean checkStampStatus(TrDocumentD document, String reservedTrxNo, String accessToken, Context context) {

        int retryAttempts = getRetryAttemptsAmount();
        long delayMs = getRetryDelay();
        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();

        try {

            while(retryAttempts > 0) {
                
                Thread.sleep(delayMs);
                context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, check document stamp status retry attempts left: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), retryAttempts));
                
                VidaCheckStampStatusResponse response = logicFactory.getVidaLogic().checkStampStatus(accessToken, document, reservedTrxNo, context);
                
                if (null == response || (200 != response.getResponseCode() && 31 != response.getResponseCode() && 9 != response.getResponseCode())) {
                    StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
                    errorDetailBean.setErrorLocation(ERR_LOC_STM_SDT);
                    errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_CHECK_STATUS_FAILED);
                    errorDetailBean.setErrorMessage("Unsuccessful Response");
                    return errorDetailBean;
                }

                if (null != response.getData() && StringUtils.isNotBlank(response.getData().getDocument())) {
                    storeStampedDocumentToOss(document, response.getData().getDocument(), context);
                    insertStampDuty(tenant, response, reservedTrxNo, context);
                    return new StampingErrorDetailBean();
                }

                retryAttempts -= 1;

            }

            context.getLogger().error(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, check document stamp status failed (max attempts reached)", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_STM_SDT);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_CHECK_STATUS_FAILED);
            errorDetailBean.setErrorMessage("Reached the maximum number of attempts to validate stamp status");
            return errorDetailBean;

        } catch (InterruptedException e) {

            context.getLogger().error(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, check document stamp status failed (thread interrupted)", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
            Thread.currentThread().interrupt();

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_STM_SDT);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_CHECK_STATUS_FAILED);
            errorDetailBean.setErrorMessage("Thread interrupted");
            errorDetailBean.setException(e);
            return errorDetailBean;

        } catch (Exception e) {

            context.getLogger().error(ExceptionUtils.getStackTrace(e));
            context.getLogger().error(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, check document stamp status failed (exception occurred)", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId()));
            
            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_STM_SDT);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_CHECK_STATUS_FAILED);
            errorDetailBean.setErrorMessage(e.getLocalizedMessage());
            errorDetailBean.setException(e);
            return errorDetailBean;

        }

    }

    private void insertStampDuty(MsTenant tenant, VidaCheckStampStatusResponse response, String reservedTrxNo, Context context) {
        MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);
        MsLov lov = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_STAMP_DUTY_STATUS, Constants.LOV_CODE_STAMP_DUTY_STATUS_GO_LIVE);

        TrStampDuty stampDuty = new TrStampDuty();
        stampDuty.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
        stampDuty.setDtmCrt(new Date());
        stampDuty.setTrxNo(reservedTrxNo);
        stampDuty.setStampDutyNo(response.getData().getSerialNumber());
        stampDuty.setMsLov(lov);
        stampDuty.setMsTenant(tenant);
        stampDuty.setMsVendor(vendor);
        stampDuty.setStampDutyFee(10000);
        stampDuty.setTransactionId(response.getData().getRefId());
        daoFactory.getStampDutyDao().insertStampDuty(stampDuty);
    }

    private int getRetryAttemptsAmount() {
        String iteration = daoFactory.getGeneralSettingDao().getGsValueByCode(GS_CODE_VIDA_CHECK_STAMP_STATUS_ITERATION);
        if (StringUtils.isBlank(iteration)) {
            return 20;
        }

        try {
            return Integer.valueOf(iteration);
        } catch (Exception e) {
            return 20;
        }
    }

    /**
     * @return value in milliseconds
     */
    private long getRetryDelay() {
        String delayS = daoFactory.getGeneralSettingDao().getGsValueByCode(GS_CODE_VIDA_CHECK_STAMP_STATUS_WAIT_TIME);
        if (StringUtils.isBlank(delayS)) {
            return 10000L;
        }

        try {
            return TimeUnit.SECONDS.toMillis(Long.valueOf(delayS));
        } catch (Exception e) {
            return 10000L;
        }
    }

    private boolean uploadStampedDocument(TrDocumentD document, Context context) {
        StampingErrorDetailBean errorDetailBean = uploadStampedDocumentToClient(document, context);
        if (StringUtils.isBlank(errorDetailBean.getErrorLocation())) {
            logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_SDT_FIN, context);
            return true;
        }

        // tidak insert reserved trx no karena tidak dibutuhkan lagi
        logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountVida(document.getTrDocumentH(), document, errorDetailBean, context);
        return false;
    }

    private StampingErrorDetailBean uploadStampedDocumentToClient(TrDocumentD document, Context context) {

        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();

        if (StringUtils.isBlank(document.getTrDocumentH().getUrlUpload())) {
            return new StampingErrorDetailBean();
        }

        if ("1".equals(documentH.getIsStandardUploadUrl())) {
            return uploadStampedDocumentToClientWithStandardRequest(document, context);
        }

        if (Constants.TENANT_CODE_WOMF.equals(tenant.getTenantCode()) && !"1".equals(documentH.getIsManualUpload())) {
            return uploadStampedDocumentToWomfDms(document, context);
        }

        if (Constants.TENANT_CODE_CFI.equals(tenant.getTenantCode()) && "1".equals(documentH.getIsManualUpload())) {
            return uploadStampedDocumentToCfiDms(document, context);
        }

        StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
        errorDetailBean.setErrorLocation(ERR_LOC_UPL_CON);
        errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_VALIDATION);
        errorDetailBean.setErrorMessage("Unreachable process");
        return errorDetailBean;
    }

    private StampingErrorDetailBean uploadStampedDocumentToClientWithStandardRequest(TrDocumentD document, Context context) {
        
        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();

        try {
            String url = document.getTrDocumentH().getUrlUpload();
            String clientToken = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, AmGlobalKey.GS_TOKEN_CLIENT_URL_UPLOAD).getSettingValue();
            if (StringUtils.isBlank(clientToken)) {
				throw new EsignhubJobException("Tenant setting" + AmGlobalKey.GS_TOKEN_CLIENT_URL_UPLOAD + "untuk tenant " + tenant.getTenantCode() + " tidak ditemukan");
			}
            String base64Document = logicFactory.getCommonStampingLogic().getStampedDocument(document, context);

            Map<String, String> header = new HashMap<>();
            header.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            header.put(HttpHeaders.KEY_ACCEPT, HttpHeaders.APPLICATION_JSON);
            header.put("token", clientToken);
            Headers headers = Headers.of(header);

            ClientDocumentUploadRequest request = new ClientDocumentUploadRequest();
            request.setAudit(new AuditDataType(context.getRequestId()));
            request.setDocFile(DUMMY_BASE64_CONTENT);
            request.setDocNumber(documentH.getRefNumber());
            request.setTenantCode(tenant.getTenantCode());
            request.setDocId(document.getDocumentId());

            String jsonRequestLog = gson.toJson(request);
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to client request: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), jsonRequestLog));
            
            request.setDocFile(base64Document);
            String jsonRequest = gson.toJson(request);
            RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

            Request okHttpRequest = new Request.Builder()
                .headers(headers)
                .url(url)
                .post(body).build();
                
            OkHttpClient client = Tools.getUnsafeOkHttpClient(10, 60);
            Response okHttpResponse = client.newCall(okHttpRequest).execute();
            String jsonResponse = okHttpResponse.body().string();
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to client response code: %4$s, body: %5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), okHttpResponse.code(), jsonResponse));
            ClientDocumentUploadResponse response = gson.fromJson(jsonResponse, ClientDocumentUploadResponse.class);

            if (response.getStatus().getCode() == 0) {
                return new StampingErrorDetailBean();
            }

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_UPL_CON);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_UPL_CLIENT);
            errorDetailBean.setErrorMessage("Failed");
            errorDetailBean.setJsonResponse(jsonResponse);
            return errorDetailBean;

        } catch (Exception e) {
            context.getLogger().error(ExceptionUtils.getStackTrace(e));

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_UPL_CON);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_EXCEPTION);
            errorDetailBean.setErrorMessage(e.getLocalizedMessage());
            errorDetailBean.setException(e);
            return errorDetailBean;

        }

    }

    private StampingErrorDetailBean uploadStampedDocumentToCfiDms(TrDocumentD document, Context context) {

        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();
        
        // Headers
        Map<String, String> header = new HashMap<>();
        header.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        header.put("Integration", logicFactory.getCommonStampingLogic().getIntegrationValue(document, context));
        Headers headers = Headers.of(header);
        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to CFI request header: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), header));

        // Body
        CfiUploadDocumentRequest request = new CfiUploadDocumentRequest();
        request.setDokumenPeruri(document.getMsPeruriDocType().getDocName());
        request.setDokumenDate(Tools.formatDateToStringIn(document.getRequestDate(), "yyyy/MM/dd"));
        request.setFilename(document.getDocumentName() + ".pdf");
        request.setContent(DUMMY_BASE64_CONTENT); // Hardcoded for shorter logging
        request.setNotes(document.getTrDocumentH().getMsLov().getDescription());
        request.setDocumentId(document.getDocumentId());

        String jsonRequestLog = gson.toJson(request);
        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to CFI request body: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), jsonRequestLog));
        
        request.setContent(logicFactory.getCommonStampingLogic().getStampedDocument(document, context));
        String jsonRequest = gson.toJson(request);
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        // URL
        String url = document.getTrDocumentH().getUrlUpload();
        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(url)
            .post(body)
            .build();
        
        try {
            OkHttpClient client = Tools.getUnsafeOkHttpClient(10, 60);
            Response okHttpResponse = client.newCall(okHttpRequest).execute();
            String jsonResponse = okHttpResponse.body().string();
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to CFI response code: %4$s, body: %5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), okHttpResponse.code(), jsonResponse));

            CfiUploadDocumentResponse response = gson.fromJson(jsonResponse, CfiUploadDocumentResponse.class);
            if ("200".equals(response.getStatusCode())) {
                return new StampingErrorDetailBean();
            }

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_UPL_CON);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_UPL_CLIENT);
            errorDetailBean.setErrorMessage(ERR_MSG_FAIL_RESPONSE);
            errorDetailBean.setJsonResponse(jsonResponse);
            return errorDetailBean;

        } catch (Exception e) {

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_UPL_CON);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_EXCEPTION);
            errorDetailBean.setErrorMessage(e.getLocalizedMessage());
            errorDetailBean.setException(e);
            return errorDetailBean;

        }

    }

    private StampingErrorDetailBean uploadStampedDocumentToWomfDms(TrDocumentD document, Context context) {

        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();

        // Headers
        Map<String, String> header = new HashMap<>();
        header.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        Headers headers = Headers.of(header);

        // Body
        WomfUploadDocumentRequest request = prepareWomfUploadDocumentRequest(document, context);
        String jsonRequest = gson.toJson(request);
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        // URL
        String url = document.getTrDocumentH().getUrlUpload();
        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(url)
            .post(body)
            .build();

        try {
            OkHttpClient client = Tools.getUnsafeOkHttpClient(10, 60);
            Response okHttpResponse = client.newCall(okHttpRequest).execute();
            String jsonResponse = okHttpResponse.body().string();
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to WOMF response code: %4$s, body: %5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), okHttpResponse.code(), jsonResponse));

            if (jsonResponse.contains("Message")) {
                jsonResponse = jsonResponse.replace("Message", "message");
            }
            jsonResponse = jsonResponse.replace("\\", "");

            if (jsonResponse.startsWith("\"")) {
                jsonResponse = jsonResponse.substring(1, jsonResponse.length() - 1);
            }
            Status status = gson.fromJson(jsonResponse, Status.class);

            if (status.getCode() == 200) {
                return new StampingErrorDetailBean();
            }

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_UPL_CON);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_UPL_CLIENT);
            errorDetailBean.setErrorMessage(ERR_MSG_FAIL_RESPONSE);
            errorDetailBean.setJsonResponse(jsonResponse);
            return errorDetailBean;
            
        } catch (Exception e) {

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_UPL_CON);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_EXCEPTION);
            errorDetailBean.setErrorMessage(e.getLocalizedMessage());
            errorDetailBean.setException(e);
            return errorDetailBean;

        }

    }

    private WomfUploadDocumentRequest prepareWomfUploadDocumentRequest(TrDocumentD document, Context context) {

        WomfUploadDocumentRequestBean documentBean = new WomfUploadDocumentRequestBean();
        documentBean.setDocTypeTc(document.getMsDocTemplate().getDocTemplateCode());
        documentBean.setDisplayName(document.getMsDocTemplate().getDocTemplateName());
        documentBean.setContent(DUMMY_BASE64_CONTENT);
        documentBean.setFileName("DOC_" + document.getDocumentId() + ".pdf");

        List<WomfUploadDocumentRequestBean> documents = new ArrayList<>();
        documents.add(documentBean);

        WomfUploadDocumentRequest request = new WomfUploadDocumentRequest();
        request.setRefNo(document.getTrDocumentH().getRefNumber());
        request.setDocumentObjs(documents);

        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();
        String jsonRequest = gson.toJson(request);
        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to WOMF request: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), jsonRequest));

        String base64Document = logicFactory.getCommonStampingLogic().getStampedDocument(document, context);
        request.getDocumentObjs().get(0).setContent(base64Document);
        return request;
    }

    private void storeStampedDocumentToOss(TrDocumentD document, String base64Document, Context context) {
        if (base64Document.startsWith(Constants.PDF_PREFIX)) {
            base64Document = base64Document.substring(Constants.PDF_PREFIX.length());
        }
        byte[] documentByteArray = Base64.getDecoder().decode(base64Document);
        logicFactory.getAliyunOssCloudStorageLogic().storeStampedDocument(document, documentByteArray, context);
    }
}
