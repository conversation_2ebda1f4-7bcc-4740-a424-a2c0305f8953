package com.adins.esignhubjob.dataaccess.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.UseroftenantDao;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsUseroftenant;
import com.adins.util.Tools;

@Component
@Transactional
public class UseroftenantDaoHbn extends BaseDaoHbn implements UseroftenantDao {

    @Override
    public MsUseroftenant getUserTenantByIdMsUserAndTenantCode(Long idMsUser, String tenantCode) {
        Object[][] params = new Object[][] {
			{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsUseroftenant ut "
				+ "join fetch ut.amMsuser u "
				+ "join fetch ut.msTenant t "
				+ "where u.idMsUser = :idMsUser and t.tenantCode = :tenantCode ", 
				params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MsUseroftenant getUserTenantByIdMsUserAndTenantCodeNewTran(Long idMsUser, String tenantCode) {
        Object[][] params = new Object[][] {
			{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsUseroftenant ut "
				+ "join fetch ut.amMsuser u "
				+ "join fetch ut.msTenant t "
				+ "where u.idMsUser = :idMsUser and t.tenantCode = :tenantCode ", 
				params);
    }

    @Override
    public void insertUseroftenant(MsUseroftenant useroftenant) {
        useroftenant.setUsrCrt(Tools.maskData(useroftenant.getUsrCrt()));
        managerDAO.insert(useroftenant);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertUseroftenantNewTran(MsUseroftenant useroftenant) {
        useroftenant.setUsrCrt(Tools.maskData(useroftenant.getUsrCrt()));
        managerDAO.insert(useroftenant);
    }
    
}
