package com.adins.esignhubjob.model.webservice.pajakku;

import com.adins.esignhubjob.model.custom.pajakku.AttachEmeteraiErrorDetail;

public class UploadDocPajakkuResponseBean extends AttachEmeteraiErrorDetail {
    private String status;
	private String saveAs;
	private String id;
	private String name;
	private String onlyName;
	private String linkUrl;
    
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public String getSaveAs() {
        return saveAs;
    }
    public void setSaveAs(String saveAs) {
        this.saveAs = saveAs;
    }
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getOnlyName() {
        return onlyName;
    }
    public void setOnlyName(String onlyName) {
        this.onlyName = onlyName;
    }
    public String getLinkUrl() {
        return linkUrl;
    }
    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }
}
