package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_registration_liveness_result")
public class TrRegistrationLivenessResult extends CreatableAndUpdatableEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private long idRegistrationLivenessResult;
    private String trxNo;
    private MsTenant msTenant;
    private MsLov lovProcessResult;
    private String livenessResult;
    private String responseMessage;
    
    @Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_registration_liveness_result", unique = true, nullable = false)
    public long getIdRegistrationLivenessResult() {
        return this.idRegistrationLivenessResult;
    }

    public void setIdRegistrationLivenessResult(long idRegistrationLivenessResult) {
        this.idRegistrationLivenessResult = idRegistrationLivenessResult;
    }

    @Column(name = "trx_no", length = 45)
    public String getTrxNo() {
        return this.trxNo;
    }

    public void setTrxNo(String trxNo) {
        this.trxNo = trxNo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
    public MsTenant getMsTenant() {
        return this.msTenant;
    }

    public void setMsTenant(MsTenant msTenant) {
        this.msTenant = msTenant;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_process_result")
    public MsLov getLovProcessResult() {
        return this.lovProcessResult;
    }

    public void setLovProcessResult(MsLov lovProcessResult) {
        this.lovProcessResult = lovProcessResult;
    }

    @Column(name = "liveness_result", length = 1)
    public String getLivenessResult() {
        return this.livenessResult;
    }

    public void setLivenessResult(String livenessResult) {
        this.livenessResult = livenessResult;
    }

    @Column(name = "response_message", length = 100)
    public String getResponseMessage() {
        return this.responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }
}