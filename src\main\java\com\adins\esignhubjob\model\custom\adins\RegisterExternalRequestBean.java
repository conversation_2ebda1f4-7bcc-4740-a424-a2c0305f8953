package com.adins.esignhubjob.model.custom.adins;

import java.io.Serializable;

public class RegisterExternalRequestBean implements Serializable {
    private static final long serialVersionUID = 1L;
	private String nama;
	private String email;
	private String tmpLahir;
	private String tglLahir;
	private String jenisKelamin;
	private String tlp;
	private String idKtp;
	private String alamat;
	private String kecamatan;
	private String kelurahan;
	private String kota;
	private String provinsi;
	private String kodePos;
	private String selfPhoto;
	private String idPhoto;
	private String password;
	private Long idEmailHosting; // parameter used only for code, not used in API parameter
	
	public String getNama() {
		return nama;
	}
	public void setNama(String nama) {
		this.nama = nama;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getTmpLahir() {
		return tmpLahir;
	}
	public void setTmpLahir(String tmpLahir) {
		this.tmpLahir = tmpLahir;
	}
	public String getTglLahir() {
		return tglLahir;
	}
	public void setTglLahir(String tglLahir) {
		this.tglLahir = tglLahir;
	}
	public String getJenisKelamin() {
		return jenisKelamin;
	}
	public void setJenisKelamin(String jenisKelamin) {
		this.jenisKelamin = jenisKelamin;
	}
	public String getTlp() {
		return tlp;
	}
	public void setTlp(String tlp) {
		this.tlp = tlp;
	}
	public String getIdKtp() {
		return idKtp;
	}
	public void setIdKtp(String idKtp) {
		this.idKtp = idKtp;
	}
	public String getAlamat() {
		return alamat;
	}
	public void setAlamat(String alamat) {
		this.alamat = alamat;
	}
	public String getKecamatan() {
		return kecamatan;
	}
	public void setKecamatan(String kecamatan) {
		this.kecamatan = kecamatan;
	}
	public String getKelurahan() {
		return kelurahan;
	}
	public void setKelurahan(String kelurahan) {
		this.kelurahan = kelurahan;
	}
	public String getKota() {
		return kota;
	}
	public void setKota(String kota) {
		this.kota = kota;
	}
	public String getProvinsi() {
		return provinsi;
	}
	public void setProvinsi(String provinsi) {
		this.provinsi = provinsi;
	}
	public String getKodePos() {
		return kodePos;
	}
	public void setKodePos(String kodePos) {
		this.kodePos = kodePos;
	}
	public String getSelfPhoto() {
		return selfPhoto;
	}
	public void setSelfPhoto(String selfPhoto) {
		this.selfPhoto = selfPhoto;
	}
	public String getIdPhoto() {
		return idPhoto;
	}
	public void setIdPhoto(String idPhoto) {
		this.idPhoto = idPhoto;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public Long getIdEmailHosting() {
		return idEmailHosting;
	}
	public void setIdEmailHosting(Long idEmailHosting) {
		this.idEmailHosting = idEmailHosting;
	}
}
