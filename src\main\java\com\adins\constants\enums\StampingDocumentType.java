package com.adins.constants.enums;

public enum StampingDocumentType {
    PAYMENT_RECEIPT("Payment Receipt"),
	PAYMENT_RECEIPT_ON_PREM("Payment Receipt On-Premise"),
	REGULAR_DOCUMENT("Regular Document");
	
	private final String description;
	
	private StampingDocumentType(String description) {
		this.description = description;
	}
	
	@Override
	public String toString() {
		return this.description;
	}
}
