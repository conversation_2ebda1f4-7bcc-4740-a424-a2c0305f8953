package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esignhubjob.model.webservice.digisign.DigisignReconcileRequest;
import com.adins.esignhubjob.model.webservice.digisign.DigisignReconcileResponse;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
public class GenericDigisignLogic implements DigisignLogic {

    @Autowired Gson gson;

    @Override
    public DigisignReconcileResponse getReconcileData(DigisignReconcileRequest request, String token, Context context) {
        try {
            // Prepare header
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Content-Type", "application/json");
            headerMap.put("Authorization", "Bearer " + token);
            Headers headers = Headers.of(headerMap);

            // Prepare body
            String jsonRequest = gson.toJson(request);
            context.getLogger().info("Digisign reconsile request: " + jsonRequest);
            RequestBody body = RequestBody.create(jsonRequest, MediaType.parse("application/json"));
            
            // Prepare request
            Request okHttpRequest = new Request.Builder()
                .headers(headers)
                .url(System.getenv(Constants.ENV_VAR_DIGI_RECON_URL))
                .post(body)
                .build();

            // Call API
            OkHttpClient client = Tools.getUnsafeOkHttpClient(20, 90);
            Response okHttpResponse = client.newCall(okHttpRequest).execute();
            String jsonResponse = okHttpResponse.body().string();
            
            try {
                DigisignReconcileResponse response = gson.fromJson(jsonResponse, DigisignReconcileResponse.class);
                if (!Constants.DIGISIGN_SUCCESS_CODE.equals(response.getResult())) {
                    context.getLogger().info("Digisign reconsile response: " + jsonResponse);
                    return response;
                }
                
                context.getLogger().info("Digisign reconsile total response: " + response.getTransactions().size() + " record(s)");
                return response;
            } catch (Exception e) {
                context.getLogger().info("Digisign reconsile response: " + jsonResponse);
                DigisignReconcileResponse response = new DigisignReconcileResponse();
                response.setInformation(e.getLocalizedMessage());
                response.setResult(Constants.DIGISIGN_FAIL_CODE);
                return response;
            }

        } catch (Exception e) {
            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);
            
            // Set fail response
            DigisignReconcileResponse response = new DigisignReconcileResponse();
            response.setResult(Constants.DIGISIGN_FAIL_CODE);
            response.setInformation(e.getLocalizedMessage());
            return response;
        }
    }
    
}
