package com.adins.esignhubjob.model.custom.adins;

import java.util.Date;

public class ProcessAutosignBean {
    private String nik;
    private String name;
    private String pob;
    private String dob;
    private String email;
    private String phone;
    private String gender;
    private String poaId;
    private String cvv;
    private String secretKey;
    private String tempCertExpiredDate;
    private Date certExpiredDate;


    public String getNik() {
        return this.nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPob() {
        return this.pob;
    }

    public void setPob(String pob) {
        this.pob = pob;
    }

    public String getDob() {
        return this.dob;
    }

    public void setDob(String dob) {
        this.dob = dob;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getGender() {
        return this.gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getPoaId() {
        return this.poaId;
    }

    public void setPoaId(String poaId) {
        this.poaId = poaId;
    }

    public String getCvv() {
        return this.cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public String getSecretKey() {
        return this.secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }
    public Date getCertExpiredDate() {
        return certExpiredDate;
    }

    public void setCertExpiredDate(Date certExpiredDate) {
        this.certExpiredDate = certExpiredDate;
    }
    public String getTempCertExpiredDate() {
        return tempCertExpiredDate;
    }

    public void setTempCertExpiredDate(String tempCertExpiredDate) {
        this.tempCertExpiredDate = tempCertExpiredDate;
    }
    
    

}
