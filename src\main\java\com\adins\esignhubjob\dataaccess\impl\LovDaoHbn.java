package com.adins.esignhubjob.dataaccess.impl;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.LovDao;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.util.Tools;

@Component
@Transactional
public class LovDaoHbn extends BaseDaoHbn implements LovDao {

    @Override
    public MsLov getLovByLovGroupAndCode(String lovGroup, String code) {
        Object[][] params = {
            { Restrictions.eq("lovGroup", StringUtils.upperCase(lovGroup)) },
            { Restrictions.eq("code", StringUtils.upperCase(code)) },
            { Restrictions.eq("isActive", "1") }
        };
        return managerDAO.selectOne(MsLov.class, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MsLov getLovByLovGroupAndCodeNewTran(String lovGroup, String code) {
        Object[][] params = {
            { Restrictions.eq("lovGroup", StringUtils.upperCase(lovGroup)) },
            { Restrictions.eq("code", StringUtils.upperCase(code)) }
        };
        return managerDAO.selectOne(MsLov.class, params);
    }

    @Override
    public void insertLov(MsLov msLov) {
        msLov.setUsrCrt(Tools.maskData(msLov.getUsrCrt()));
        managerDAO.insert(msLov);
    }
    
}
