package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_balancevendoroftenant")
public class MsBalancevendoroftenant extends ActivatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	private long idBalancevendoroftenant;
	private MsLov msLov;
	private MsTenant msTenant;
	private MsVendor msVendor;

	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_balancevendoroftenant", unique = true, nullable = false)
	public long getIdBalancevendoroftenant() {
		return this.idBalancevendoroftenant;
	}

	public void setIdBalancevendoroftenant(long idBalancevendoroftenant) {
		this.idBalancevendoroftenant = idBalancevendoroftenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_balance_type", nullable = false)
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return this.msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}
}
