package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.CommonStampingLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esignhubjob.model.custom.privy.PrivySignLocation;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralDocumentHistoryResponseContainer;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralDocumentOwner;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralEmeteraiBean;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralRegisterStatusResponseContainer;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralStampPosition;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralUploadDocumentBean;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralDocumentHistoryRequest;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralDocumentHistoryResponse;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralDocumentStatusRequest;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralDocumentStatusResponse;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralGetTokenRequest;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralGetTokenResponse;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralRegisterStatusRequest;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralRegisterStatusResponse;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralUploadDocumentRequest;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralUploadDocumentResponse;
import com.adins.exceptions.EsignhubJobException;
import com.adins.util.PrivyUtils;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
public class GenericPrivyGeneralLogic extends BaseLogic implements PrivyGeneralLogic {

	@Autowired private CommonStampingLogic commonStampingLogic;

    private Headers buildPrivyApiHeader(String jsonRequest, MsVendoroftenant vendoroftenant, Context context) {
		Date timestamp = new Date();
		String token = getToken(vendoroftenant, context);
		
		Map<String, String> mapHeader = new HashMap<>();
		mapHeader.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		mapHeader.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.buildBearerToken(token));
		mapHeader.put("Timestamp", Tools.formatDateToStringIn(timestamp, "yyyy-MM-dd'T'HH:mm:ssXXX"));
		mapHeader.put("Signature", generateSignature(jsonRequest, timestamp, vendoroftenant, context));
		return Headers.of(mapHeader);
	}
	
	private String generateSignature(String jsonRequest, Date timestamp, MsVendoroftenant vendoroftenant, Context context) {
		String username = vendoroftenant.getClientId();
		String password = vendoroftenant.getClientSecret();
		
		try {
			return PrivyUtils.createSignature(timestamp, username, password, jsonRequest);
		} catch (Exception e) {
            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);
			throw new EsignhubJobException("Failed to generate Privy signature");
		}
		
	}
	
	private String getToken(MsVendoroftenant vendoroftenant, Context context) {
		try {
			PrivyGeneralGetTokenResponse response = callGetTokenApi(vendoroftenant.getClientId(), vendoroftenant.getClientSecret(), context);
			return response.getData().getAccessToken();
		} catch (Exception e) {
            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);
			throw new EsignhubJobException("Failed to get Privy token");
		}
	}
	
	private PrivyGeneralGetTokenResponse callGetTokenApi(String username, String password, Context context) throws IOException {
		
		// Header
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.buildBasicAuthorization(username, password));
		Headers headers = Headers.of(headerMap);
		
		// Body
		PrivyGeneralGetTokenRequest request = new PrivyGeneralGetTokenRequest();
		request.setClientId(username);
		request.setClientSecret(password);
		request.setGrantType("client_credentials");
		String jsonRequest = gson.toJson(request);
		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));
		
		// Prepare request
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_GET_TOKEN_URL))
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(60L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Date startTime = new Date();
		Response okHttpResponse = client.newCall(okHttpRequest).execute();
		Date finishTime = new Date();
		logProcessDuration("Get General Privy token", startTime, finishTime, context);
		
		String jsonResponse = okHttpResponse.body().string();
		context.getLogger().debug("Get General Privy token response code: " + okHttpResponse.code());
		context.getLogger().debug("Get General Privy token response body: " + jsonResponse);
		return gson.fromJson(jsonResponse, PrivyGeneralGetTokenResponse.class);
		
	}

    @Override
    public PrivyGeneralRegisterStatusResponseContainer checkRegisterStatus(TrJobCheckRegisterStatus jobCheckRegisterStatus, MsVendoroftenant vendoroftenant, Context context) {
        try {

			TrBalanceMutation balanceMutation = jobCheckRegisterStatus.getTrBalanceMutation();

            // Prepare json request
            PrivyGeneralRegisterStatusRequest request = new PrivyGeneralRegisterStatusRequest();
            request.setReferenceNumber("ADINS" + balanceMutation.getTrxNo());
            request.setChannelId(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_CHANNEL_ID));
            request.setRegisterToken(balanceMutation.getVendorTrxNo());
            request.setInfo(StringUtils.EMPTY);
            String jsonRequest = gson.toJson(request);
            context.getLogger().info(String.format("Job ID %1$s, Check General Privy register status request: %2$s", jobCheckRegisterStatus.getIdJobCheckRegisterStatus(), jsonRequest));
            RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

            // Headers
            Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, context);

            Request okHttpRequest = new Request.Builder()
                .headers(headers)
                .url(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_CHECK_REGISTER_URL))
                .post(body)
                .build();

            OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(60L, TimeUnit.SECONDS)
                .readTimeout(60L, TimeUnit.SECONDS)
                .build();

            Date startTime = new Date();
            Response response = client.newCall(okHttpRequest).execute();
            Date finishTime = new Date();
            logProcessDuration("Check General Privy register status", startTime, finishTime, context);

            String jsonResponse = response.body().string();
            context.getLogger().info(String.format("Job ID %1$s, Check General Privy register status response: %2$s", jobCheckRegisterStatus.getIdJobCheckRegisterStatus(), jsonResponse));
            
			PrivyGeneralRegisterStatusResponseContainer responseContainer = new PrivyGeneralRegisterStatusResponseContainer();
			responseContainer.setRequestBody(jsonRequest);
			responseContainer.setResponseBody(jsonResponse);
			responseContainer.setResponse(gson.fromJson(jsonResponse, PrivyGeneralRegisterStatusResponse.class));
			return responseContainer;

        } catch (Exception e) {

            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);
            throw new EsignhubJobException("Failed to check Privy General register status");
            
        }
    }

	@Override
	public String getRegisterStatusRejectMessage(PrivyGeneralRegisterStatusResponse response, Context context) {
		if (null == response || null == response.getData()) {
			return StringUtils.EMPTY;
		}

		if ("waiting_verification".equalsIgnoreCase(response.getData().getStatus())) {
			return response.getData().getStatus();
		}
		
		if ("verified".equalsIgnoreCase(response.getData().getStatus()) || "registered".equalsIgnoreCase(response.getData().getStatus())) {
			return "Registrasi berhasil";
		}

		String rejectCode = response.getData().getRejectReason().getCode();
		if ("RC04".equals(rejectCode) || "RC06".equals(rejectCode)) {
			return "Verifikasi user gagal. Foto Diri tidak sesuai.";
		}

		if ("RC12".equals(rejectCode) || "RC13".equals(rejectCode)) {
			return "Verifikasi user gagal. Nama Lengkap tidak sesuai.";
		}

		context.getLogger().info("Unhandled reject code: " + rejectCode);
		return "Verifikasi gagal. " + response.getData().getRejectReason().getReason();

	}

	@Override
	public PrivyGeneralDocumentHistoryResponseContainer checkDocumentHistory(TrDocumentSigningRequest signingRequest, MsVendoroftenant vendoroftenant, TrDocumentD document, Context context) throws IOException {
		
		PrivyGeneralDocumentHistoryRequest request = new PrivyGeneralDocumentHistoryRequest();
		request.setReferenceNumber(StringUtils.remove(document.getDocumentId(), '-'));
		request.setChannelId(vendoroftenant.getVendorChannelId());
		request.setDocumentToken(document.getPsreDocumentId());
		request.setInfo(StringUtils.EMPTY);
		String jsonRequest = gson.toJson(request);
		context.getLogger().info(String.format("Request ID %1$s, Check document history request: %2$s", signingRequest.getIdDocumentSigningRequest(), jsonRequest));
		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

		Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, context);

		Request okHttpRequest = new Request.Builder()
                .headers(headers)
                .url(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_DOC_HISTORY_URL))
                .post(body)
                .build();

		OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(60L, TimeUnit.SECONDS)
                .readTimeout(60L, TimeUnit.SECONDS)
                .build();

		Date startTime = new Date();
        Response response = client.newCall(okHttpRequest).execute();
        Date finishTime = new Date();
        logProcessDuration("Check General Privy document history", startTime, finishTime, context);
		
		String jsonResponse = response.body().string();
		context.getLogger().info(String.format("Request ID %1$s, Check document history response: %2$s", signingRequest.getIdDocumentSigningRequest(), jsonResponse));
		
		PrivyGeneralDocumentHistoryResponseContainer responseContainer = new PrivyGeneralDocumentHistoryResponseContainer();
		responseContainer.setRequestBody(jsonRequest);
		responseContainer.setResponseBody(jsonResponse);
		responseContainer.setResponse(gson.fromJson(jsonResponse, PrivyGeneralDocumentHistoryResponse.class));
		return responseContainer;

	}

	@Override
	public PrivyGeneralDocumentStatusResponse checkDocumentStatus(TrDocumentSigningRequest signingRequest, MsVendoroftenant vendoroftenant, TrDocumentD document, Context context) throws IOException {
		
		PrivyGeneralDocumentStatusRequest request = new PrivyGeneralDocumentStatusRequest();
		request.setReferenceNumber(StringUtils.remove(document.getDocumentId(), '-'));
		request.setChannelId(vendoroftenant.getVendorChannelId());
		request.setDocumentToken(document.getPsreDocumentId());
		request.setInfo(StringUtils.EMPTY);
		String jsonRequest = gson.toJson(request);
		context.getLogger().info(String.format("Request ID %1$s, Check document status request: %2$s", signingRequest.getIdDocumentSigningRequest(), jsonRequest));
		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

		Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, context);

		Request okHttpRequest = new Request.Builder()
                .headers(headers)
                .url(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_DOC_STATUS_URL))
                .post(body)
                .build();

		OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(60L, TimeUnit.SECONDS)
                .readTimeout(60L, TimeUnit.SECONDS)
                .build();

		Date startTime = new Date();
        Response response = client.newCall(okHttpRequest).execute();
        Date finishTime = new Date();
        logProcessDuration("Check General Privy document status", startTime, finishTime, context);
		
		String jsonResponse = response.body().string();
		context.getLogger().info(String.format("Request ID %1$s, Check document status response: %2$s", signingRequest.getIdDocumentSigningRequest(), jsonResponse));
		return gson.fromJson(jsonResponse, PrivyGeneralDocumentStatusResponse.class);
	}

	@Override
	public PrivyGeneralUploadDocumentResponse uploadDocumentStamping(TrDocumentD document, MsVendoroftenant vendoroftenant, String reservedTrxNo, Context context) throws IOException {
		MsTenant tenant = document.getMsTenant();
		TrDocumentH documentH = document.getTrDocumentH();

		PrivyGeneralUploadDocumentRequest request = prepareUploadDocumentStampingRequest(document, reservedTrxNo, context);
		String jsonRequest = gson.toJson(request);
		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

		Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, context);

		Request okHttpRequest = new Request.Builder()
                .headers(headers)
                .url(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_UPLOAD_DOC_URL))
                .post(body)
                .build();

		OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(60L, TimeUnit.SECONDS)
                .readTimeout(60L, TimeUnit.SECONDS)
                .build();

		Date startTime = new Date();
        Response response = client.newCall(okHttpRequest).execute();
        Date finishTime = new Date();
        logProcessDuration("Privy General upload stamp document", startTime, finishTime, context);

		String jsonResponse = response.body().string();
		context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Privy General upload stamp response code: %4$s, response body: %5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), response.code(), jsonResponse));
		return gson.fromJson(jsonResponse, PrivyGeneralUploadDocumentResponse.class);
	}

	private PrivyGeneralUploadDocumentRequest prepareUploadDocumentStampingRequest(TrDocumentD document, String reservedTrxNo, Context context) {
		PrivyGeneralDocumentOwner docOwner = new PrivyGeneralDocumentOwner();
		docOwner.setPrivyId(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_DOC_OWNER_ID));
		docOwner.setEnterpriseToken(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_DOC_OWNER_TOKEN));

		String base64Document = commonStampingLogic.getDocumentFileToUpload(document, context);
		String documentName = null != document.getDocumentName() ? document.getDocumentName() : document.getMsDocTemplate().getDocTemplateName();
		documentName = documentName.replaceAll("[^a-zA-Z0-9\\s]", "");

		PrivyGeneralUploadDocumentBean documentBean = new PrivyGeneralUploadDocumentBean();
		documentBean.setDocumentFile("base64document");
		documentBean.setDocumentName(documentName);
		documentBean.setSignProcess("1");
		documentBean.setBarcodePosition("0");

		List<TrDocumentDStampduty> documentDStampduties = daoFactory.getDocumentDao().getNotStampedDocumentDStampdutiesNewTrx(document);
		List<PrivyGeneralStampPosition> stampPositions = new ArrayList<>();

		PrivyGeneralEmeteraiBean emeteraiBean = new PrivyGeneralEmeteraiBean();
		emeteraiBean.setDocCategory(commonStampingLogic.getDocumentCategoryForUpload(document, context));
		for (TrDocumentDStampduty documentDStampduty : documentDStampduties) {
			PrivySignLocation location = gson.fromJson(documentDStampduty.getPrivySignLocation(), PrivySignLocation.class);

			PrivyGeneralStampPosition position = new PrivyGeneralStampPosition();
			position.setPosX(String.valueOf(location.getX()));
			position.setPosY(String.valueOf(location.getY()));
			position.setPage(String.valueOf(documentDStampduty.getSignPage()));
			position.setDimension(130);
			stampPositions.add(position);
		}
		emeteraiBean.setStampPosition(stampPositions);

		PrivyGeneralUploadDocumentRequest request = new PrivyGeneralUploadDocumentRequest();
		request.setReferenceNumber(reservedTrxNo + StringUtils.remove(document.getDocumentId(), '-'));
		request.setChannelId(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_CHANNEL_ID));
		request.setDocProcess("1");
		request.setInfo(StringUtils.EMPTY);
		request.setDocOwner(docOwner);
		request.setDocument(documentBean);
		request.setEmeterai(emeteraiBean);

		MsTenant tenant = document.getMsTenant();
		TrDocumentH documentH = document.getTrDocumentH();

		String jsonRequest = gson.toJson(request);
		context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Privy General upload stamp request: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), jsonRequest));
		request.getDocument().setDocumentFile(Constants.PDF_PREFIX + base64Document);
		return request;
	}

	@Override
	public PrivyGeneralDocumentStatusResponse checkStampingStatus(TrDocumentD document, MsVendoroftenant vendoroftenant, String reservedTrxNo, Context context) throws IOException {
		
		MsTenant tenant = document.getMsTenant();
		TrDocumentH documentH = document.getTrDocumentH();

		PrivyGeneralDocumentStatusRequest request = new PrivyGeneralDocumentStatusRequest();
		request.setReferenceNumber(reservedTrxNo + StringUtils.remove(document.getDocumentId(), '-'));
		request.setChannelId(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_CHANNEL_ID));
		request.setDocumentToken(document.getPsreDocumentId());
		request.setInfo(StringUtils.EMPTY);
		String jsonRequest = gson.toJson(request);
		context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Privy General check stamp document status request: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), jsonRequest));
		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

		Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, context);

		Request okHttpRequest = new Request.Builder()
                .headers(headers)
                .url(System.getenv(Constants.ENV_VAR_PRIVY_GENERAL_DOC_STATUS_URL))
                .post(body)
                .build();
		
		OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(60L, TimeUnit.SECONDS)
                .readTimeout(60L, TimeUnit.SECONDS)
                .build();

		Date startTime = new Date();
        Response response = client.newCall(okHttpRequest).execute();
        Date finishTime = new Date();
        logProcessDuration("Privy General check stamp document status", startTime, finishTime, context);

		String jsonResponse = response.body().string();
		context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Privy General check stamp document status response code: %4$s, body: %5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), response.code(), jsonResponse));
		return gson.fromJson(jsonResponse, PrivyGeneralDocumentStatusResponse.class);
	}

	@Override
	public String getRegisterStatusShortRejectMessage(PrivyGeneralRegisterStatusResponse response, Context context) {
		if (null == response || null == response.getData()) {
			return StringUtils.EMPTY;
		}

		if ("waiting_verification".equalsIgnoreCase(response.getData().getStatus())) {
			return response.getData().getStatus();
		}
		
		if ("verified".equalsIgnoreCase(response.getData().getStatus()) || "registered".equalsIgnoreCase(response.getData().getStatus())) {
			return "Registrasi berhasil";
		}

		String rejectCode = response.getData().getRejectReason().getCode();
		if ("RC04".equals(rejectCode) || "RC06".equals(rejectCode)) {
			return "Foto Diri tidak sesuai.";
		}

		if ("RC12".equals(rejectCode) || "RC13".equals(rejectCode)) {
			return "Nama Lengkap tidak sesuai.";
		}

		context.getLogger().info("Unhandled reject code: " + rejectCode);
		return response.getData().getRejectReason().getReason();
	}
    
}
