package com.adins.esignhubjob.businesslogic.impl;

import java.util.Iterator;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.stringtemplate.v4.ST;

import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.MessageTemplateLogic;
import com.adins.esignhubjob.model.table.MsMsgTemplate;

@Component
public class GenericMessageTemplateLogic extends BaseLogic implements MessageTemplateLogic {

    private char delimiterStartChar = '{';
	private char delimiterStopChar = '}';

    @Override
    @Transactional(readOnly = true)
    public MsMsgTemplate getAndParseContent(String msgTemplateCode, Map<String, Object> templateParameters) {
        if (StringUtils.isBlank(msgTemplateCode)) {
			throw new IllegalArgumentException("Argument msgTemplateCode is required");
		}
				
		MsMsgTemplate template = this.daoFactory.getMsgTemplateDao().getTemplateByCode(msgTemplateCode);
		
		if (null == template) {
			return null;
		}
		
		if (StringUtils.isBlank(template.getBody())) {
			return template;
		}
		
		MsMsgTemplate returnTemplate = new MsMsgTemplate(); //init new object bcoz dirty object committed if use same object
						
		ST templateBody = new ST(template.getBody(), delimiterStartChar, delimiterStopChar);
		this.setParamsToSt(templateParameters, templateBody);					
		String formattedContent = templateBody.render();
		returnTemplate.setBody(formattedContent);
		returnTemplate.setWaTemplateCode(template.getWaTemplateCode());
					
		if (StringUtils.isNotBlank(template.getSubject())) {
			ST templateSubject = new ST(template.getSubject(), delimiterStartChar, delimiterStopChar);			
			this.setParamsToSt(templateParameters, templateSubject);
			String formattedSubject = templateSubject.render(); 
			returnTemplate.setSubject(formattedSubject);
		}		
		
		return returnTemplate;
    }

    private void setParamsToSt(Map<String, Object> templateParameters, ST template) {
		for (Iterator<String> iterator = templateParameters.keySet().iterator(); iterator.hasNext();) {
			String key = iterator.next();
			template.add(key, templateParameters.get(key));
		}
	}
    
}
