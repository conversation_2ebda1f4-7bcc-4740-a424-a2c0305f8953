package com.adins.esignhubjob.model.custom.privy;

import java.util.List;

public class PrivyDocumentStatusBean {
    private String title;
    private String docToken;
    private List<PrivyDocumentRecipientBean> recipients;
    private String documentStatus;
    private String urlDocument;
    private PrivyDownloadDocumentBean download;
    private String expiredAt;

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDocToken() {
        return this.docToken;
    }

    public void setDocToken(String docToken) {
        this.docToken = docToken;
    }

    public List<PrivyDocumentRecipientBean> getRecipients() {
        return this.recipients;
    }

    public void setRecipients(List<PrivyDocumentRecipientBean> recipients) {
        this.recipients = recipients;
    }

    public String getDocumentStatus() {
        return this.documentStatus;
    }

    public void setDocumentStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    public String getUrlDocument() {
        return this.urlDocument;
    }

    public void setUrlDocument(String urlDocument) {
        this.urlDocument = urlDocument;
    }

    public PrivyDownloadDocumentBean getDownload() {
        return this.download;
    }

    public void setDownload(PrivyDownloadDocumentBean download) {
        this.download = download;
    }

    public String getExpiredAt() {
        return this.expiredAt;
    }

    public void setExpiredAt(String expiredAt) {
        this.expiredAt = expiredAt;
    }   

}
