package com.adins.esignhubjob.job;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.custom.adins.ProcessAutosignBean;
import com.adins.esignhubjob.model.table.AmMemberofrole;
import com.adins.esignhubjob.model.table.AmMsrole;
import com.adins.esignhubjob.model.table.MsOffice;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.AmUserPersonalData;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsUseroftenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.PersonalDataBean;
import com.adins.esignhubjob.model.table.TrProcessAutosignBmD;
import com.adins.esignhubjob.model.table.TrProcessAutosignBmH;
import com.adins.esignhubjob.model.table.custom.ZipcodeCityBean;
import com.adins.exceptions.EsignhubJobException;
import com.adins.util.ExcelUtils;
import com.adins.util.IOUtils;
import com.adins.util.PasswordHash;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class ImportAutosignDataJob extends BaseJobHandler {

    private static final String MSG_NIK_USED_BY_OTHER_PHONE_EMAIL = "NIK sudah digunakan oleh no telp dan email yang berbeda dari data yang dikirim";
    private static final String MSG_NIK_USED_BY_OTHER_EMAIL = "NIK sudah digunakan oleh email yang berbeda dari data yang dikirim";
    private static final String MSG_EMAIL_NOT_OWNED_BY_NIK = "Email bukan milik NIK ";
    private static final String MSG_PHONE_NOT_OWNED_BY_NIK = "No Telp bukan milik NIK ";
    
    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {

        String input = IOUtils.toString(inputStream);
        context.getLogger().info("Importing Autosign data for ID: " + input);

        long idProcessAutosignBmH = Long.parseLong(input);
        TrProcessAutosignBmH processAutosignBmH = daoFactory.getProcessAutosignBmDao().getProcessAutosignBmHNewTrx(idProcessAutosignBmH);
        if (null == processAutosignBmH) {
            context.getLogger().error("ID " + input + " not found");
            return;
        }

        if (!"0".equals(processAutosignBmH.getStatus())) {
            context.getLogger().error(String.format("ID %1$s with status %2$s will not be processed", input, processAutosignBmH.getStatus()));
            return;
        }

        processAutosignBmH.setStatus("1");
        processAutosignBmH.setDtmUpd(new Date());
        processAutosignBmH.setUsrUpd(context.getRequestId());
        daoFactory.getProcessAutosignBmDao().updateProcessAutosignBmHNewTrx(processAutosignBmH);

        try {
            importDataFromExcel(processAutosignBmH, context);
        } catch (Exception e) {
            context.getLogger().error(String.format("ID %1$s, import data from excel error: %2$s", input, e.getLocalizedMessage()));
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
        }

        processAutosignBmH.setStatus("2");
        processAutosignBmH.setDtmUpd(new Date());
        processAutosignBmH.setUsrUpd(context.getRequestId());
        daoFactory.getProcessAutosignBmDao().updateProcessAutosignBmHNewTrx(processAutosignBmH);
    }

    /**
     * @return <code>true</code> when import 1 autosign row is successful
     * @throws IOException
     */
    private void importDataFromExcel(TrProcessAutosignBmH processAutosignBmH, Context context) throws IOException {
        byte[] excelByteArray = logicFactory.getAliyunOssCloudStorageLogic().getAutosignImportExcel(processAutosignBmH, context);
        try (Workbook workbook = new XSSFWorkbook(new ByteArrayInputStream(excelByteArray))) {
            Sheet sheet = workbook.getSheetAt(0);
            context.getLogger().info(String.format("ID %1$s, processing %2$s rows (including header label)", processAutosignBmH.getIdProcessAutosignBmH(), sheet.getPhysicalNumberOfRows()));

            for (int i = 0; i < sheet.getPhysicalNumberOfRows(); i++) {
                // Skip processing header label
                if (i == 0) {
                    continue;
                }

                ProcessAutosignBean bean = getAutosignData(sheet.getRow(i));
                context.getLogger().info(String.format("ID %1$s, row %2$s, processing user %3$s", processAutosignBmH.getIdProcessAutosignBmH(), i+1, bean.getEmail()));

                boolean isSuccessful = true;
                String notes = null;
        
                try {
                    MsVendor vendor = processAutosignBmH.getMsVendor();
                    bean.setCertExpiredDate(getExpiredDate(vendor, bean.getTempCertExpiredDate()));
                } catch (EsignhubJobException e) {
                    throw e;
                } catch (Exception e) {
                    throw new EsignhubJobException("Tanggal certificate expired date harus dalam format yyyy-MM-dd");
                }

                try {
                    importDataFromExcelRow(processAutosignBmH, bean, context);
                } catch (Exception e) {
                    isSuccessful = false;
                    notes = e.getLocalizedMessage();
                    context.getLogger().error(String.format("ID %1$s, row %2$s, exception occurred: %3$s", processAutosignBmH.getIdProcessAutosignBmH(), i+1, e.getLocalizedMessage()));
                    context.getLogger().error(ExceptionUtils.getStackTrace(e));
                }

                String insertedEmail = StringUtils.left(StringUtils.upperCase(bean.getEmail()), 80);
                String insertedPhone = StringUtils.left(Tools.maskData(bean.getPhone()), 20);
                String insertedNik = StringUtils.left(Tools.maskData(bean.getNik()), 50);
                String insertedPob = StringUtils.left(StringUtils.upperCase(bean.getPob()), 80);
                String insertedGender = StringUtils.left(StringUtils.upperCase(bean.getGender()), 1);

                TrProcessAutosignBmD processAutosignBmD = new TrProcessAutosignBmD();
                processAutosignBmD.setTrProcessAutosignBmH(processAutosignBmH);
                processAutosignBmD.setEmail(insertedEmail);
                processAutosignBmD.setPhone(insertedPhone);
                processAutosignBmD.setNik(insertedNik);
                processAutosignBmD.setPlaceOfBirth(insertedPob);
                processAutosignBmD.setDateOfBirth(Tools.formatStringToDate(bean.getDob(), Constants.DATE_FORMAT));
                processAutosignBmD.setGender(insertedGender);
                processAutosignBmD.setKeyUser(Tools.maskData(bean.getSecretKey()));
                processAutosignBmD.setCvv(bean.getCvv());
                processAutosignBmD.setPoaId(Tools.maskData(bean.getPoaId()));
                processAutosignBmD.setStatus(isSuccessful ? "3" : "2");
                processAutosignBmD.setNotes(notes);
                processAutosignBmD.setUsrCrt(context.getRequestId());
                processAutosignBmD.setCertExpiredDate(bean.getCertExpiredDate());
                processAutosignBmD.setDtmCrt(new Date());
                daoFactory.getProcessAutosignBmDao().insertProcessAutosignBmD(processAutosignBmD);
                
            }

            processAutosignBmH.setStatus("2");
            processAutosignBmH.setUsrUpd(context.getRequestId());
            processAutosignBmH.setDtmUpd(new Date());
            daoFactory.getProcessAutosignBmDao().updateProcessAutosignBmHNewTrx(processAutosignBmH);
        }

    }

    private Date getExpiredDate(MsVendor vendor, String tempCertExpiredDate) throws ParseException {
        String defaultCertExpiredDateVida = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_VIDA_CERTIFICATE_EXPIRE_TIME);
        String defaultCertExpiredDatePrivy = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_PRIVY_CERTIFICATE_EXPIRE_TIME);
        String defaultCertExpiredDateDigisign = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_DIGISIGN_CERTIFICATE_EXPIRE_TIME);
                
        if (defaultCertExpiredDateVida == null || defaultCertExpiredDatePrivy == null || defaultCertExpiredDateDigisign == null) {
            throw new EsignhubJobException("At least one general setting expired time not found");
        }

        if (vendor.getVendorCode().equals(Constants.VENDOR_CODE_VIDA) && tempCertExpiredDate == null) {
            return DateUtils.addDays(new Date(), Integer.parseInt(defaultCertExpiredDateVida));
        } else if (vendor.getVendorCode().equals(Constants.VENDOR_CODE_PRIVY) && tempCertExpiredDate == null) {
            return DateUtils.addDays(new Date(), Integer.parseInt(defaultCertExpiredDatePrivy));
        } else if (vendor.getVendorCode().equals(Constants.VENDOR_CODE_DIGISIGN) && tempCertExpiredDate == null) {
            return DateUtils.addDays(new Date(), Integer.parseInt(defaultCertExpiredDateDigisign));
        } else {
            return validateDateFormat(tempCertExpiredDate, "yyyy-mm-dd");
        }
    }

    private void importDataFromExcelRow(TrProcessAutosignBmH processAutosignBmH, ProcessAutosignBean bean, Context context) {        
        validateProcessAutosignBean(bean);
        validateNikPhoneEmail(bean.getNik(), bean.getPhone(), bean.getEmail(), processAutosignBmH.getMsVendor(), context);


        context.getLogger().info("cert expired to ms_vendor is" + bean.getCertExpiredDate().toString());

        MsTenant tenant = processAutosignBmH.getMsTenant();
        MsVendor vendor = processAutosignBmH.getMsVendor();
        String password = Tools.generateRandomCharacters(Constants.CHRS, 8);
        String hashedPhone = Tools.getHashedString(bean.getPoaId());
        String hashedNik = Tools.getHashedString(bean.getNik());
        boolean isNewUser = false;

        AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(bean.getNik());
        if (null == user) {
            context.getLogger().info("user null nik");
            user = daoFactory.getUserDao().getUserByEmailNewTran(bean.getEmail());
            if (null == user) {
                context.getLogger().info("user null email");
                isNewUser = true;
                MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCodeNewTran(tenant.getTenantCode());
                String[] separatedName = bean.getName().split(" ");
    
                user = new AmMsuser();
                user.setIsActive("1");
                user.setIsDeleted("0");
                user.setLoginId(StringUtils.upperCase(bean.getEmail()));
                user.setFullName(StringUtils.upperCase(bean.getName()));
                user.setInitialName(StringUtils.upperCase(separatedName[0]));
                user.setLoginProvider("DB");
                user.setPassword(PasswordHash.createHash(password));
                user.setFailCount(0);
                user.setIsLoggedIn("0");
                user.setIsLocked("0");
                user.setIsDormant("0");
                user.setMsOffice(office);
                user.setChangePwdLogin("1");
                user.setUsrCrt(context.getRequestId());
                user.setDtmCrt(new Date());
                user.setEmailService("0");
                user.setHashedPhone(hashedPhone);
                user.setHashedIdNo(hashedNik);
                daoFactory.getUserDao().insertUserNewTran(user);
            } else {
                context.getLogger().info("user not null email");
                if (null == user.getHashedIdNo() || user.getHashedIdNo().equals(hashedNik)) {
                    context.getLogger().info("user not null email, nik null user management");
                    user.setIsDormant("0");
                    user.setUsrUpd(context.getRequestId());
                    user.setHashedIdNo(hashedNik);
                    user.setHashedPhone(hashedPhone);
                    user.setDtmUpd(new Date());
                    daoFactory.getUserDao().updateUserNewTran(user);
                } else if (!user.getHashedIdNo().equals(hashedNik)) {
                    context.getLogger().info("user not null email, nik not same with nik excel");
                    throw new EsignhubJobException(MSG_EMAIL_NOT_OWNED_BY_NIK + Tools.maskData(hashedNik));
                }
            }
        } else {
            context.getLogger().info("user not null nik, update user");
            user.setIsDormant("0");
            user.setUsrUpd(context.getRequestId());
            user.setLoginId(StringUtils.upperCase(bean.getEmail()));
            user.setHashedPhone(hashedPhone);
            user.setDtmUpd(new Date());
            daoFactory.getUserDao().updateUserNewTran(user);
        }

        insertUnregisteredUseroftenant(user, tenant, context);
        insertUnregisteredVendorUser(user, vendor, bean, context);
        insertUnregisteredPersonalData(user, bean, context);

        AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode("MF", tenant.getTenantCode());
        insertUnregisteredMemberofrole(user, role, context);

        if (isNewUser) {
            sendAccountInformationEmail(bean.getName(), bean.getEmail(), password, context);
        }

    }

    public static Date validateDateFormat(String dateStr, String expectedFormat) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Date outputDate = null;

        try {
            outputDate = dateFormat.parse(dateStr);
        } catch (ParseException e) {
            throw new EsignhubJobException("error parsing date");
        }
        
        return outputDate;
    }

    private void sendAccountInformationEmail(String fullname, String email, String password, Context context) {
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("email", StringUtils.upperCase(email));
        userMap.put("password", password);
        userMap.put("fullname", StringUtils.upperCase(fullname));
        userMap.put("link", System.getProperty(Constants.ENV_VAR_ESIGNHUB_LOGIN_URL));

        Map<String, Object> templateParam = new HashMap<>();
        templateParam.put("user", userMap);

        MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent("INFO_AKUN", templateParam);

        EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setSubject(template.getSubject());
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setTo(new String[] {email});
        logicFactory.getEmailSenderLogic().sendEmail(emailInfo, null, context);
    }

    private void validateNikPhoneEmail(String nik, String phone, String email, MsVendor vendor, Context context) {
        validateNikPhoneEmailWithCurrentVendor(nik, phone, email, vendor, context);
        validateNikPhoneEmailWithOtherVendor(nik, phone, email, vendor, context);
    }

    private void validateNikPhoneEmailWithCurrentVendor(String nik, String phone, String email, MsVendor vendor, Context context) {
        String vendorCode = vendor.getVendorCode();
		MsVendorRegisteredUser nikUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNo(nik, vendorCode);
		MsVendorRegisteredUser phoneUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhone(phone, vendorCode);
		MsVendorRegisteredUser emailUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByEmail(email, vendorCode);

        // NIK sudah terpakai
		if (null != nikUser) {
			    // NIK sudah terpakai, HP belum terpakai
			if (null == phoneUser) {
				// NIK sudah terpakai, HP belum terpakai, Email belum terpakai
				if (null == emailUser) {
					// throw new EsignhubJobException(MSG_NIK_USED_BY_OTHER_PHONE_EMAIL);
                    context.getLogger().info("NIK used, No Telp & Email unused (continue)");
                    return;
				}
				
				// NIK sudah terpakai, HP belum terpakai, Email sudah terpakai, NIK dan Email dipakai orang yang sama
				if (nikUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()) {
					// throw new EsignhubJobException("Email dan NIK sudah digunakan oleh No Telp yang berbeda dari data yang dikirim");
                    context.getLogger().info("Email & NIK used by the same person, No Telp unused (continue)");
                    return;
				}
				
				// NIK sudah terpakai, HP belum terpakai, Email sudah terpakai, NIK dan Email dipakai 2 orang yang berbeda
				throw new EsignhubJobException(MSG_NIK_USED_BY_OTHER_EMAIL);
			}
			
			// NIK sudah terpakai, HP sudah terpakai
			
			// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai
			if (null == emailUser) {
				// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai, NIK dan HP dipakai orang yang sama
				if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser()) {
					// throw new EsignhubJobException("No Telp dan NIK sudah digunakan oleh Email yang berbeda dari data yang dikirim");
                    context.getLogger().info("No Telp & NIK used by the same person, Email unused (continue)");
                    return;
				}
				
				// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai, NIK dan HP dipakai 2 orang yang berbeda
				throw new EsignhubJobException("NIK sudah digunakan oleh no telp yang berbeda dari data yang dikirim");
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK HP dan Email dipakai orang yang sama
			if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser() && phoneUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()) {
				// Kalau sudah terdaftar, akan update PoA
                return;
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK HP dan Email dipakai 3 orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() != phoneUser.getAmMsuser().getIdMsUser()
					&& phoneUser.getAmMsuser().getIdMsUser() != emailUser.getAmMsuser().getIdMsUser()
					&& emailUser.getAmMsuser().getIdMsUser() != nikUser.getAmMsuser().getIdMsUser()) {
				throw new EsignhubJobException(MSG_NIK_USED_BY_OTHER_PHONE_EMAIL);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK dan HP digunakan orang yang sama, tapi Email dipakai orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser()
					&& nikUser.getAmMsuser().getIdMsUser() != emailUser.getAmMsuser().getIdMsUser()) {
				throw new EsignhubJobException(MSG_NIK_USED_BY_OTHER_EMAIL);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK dan Email digunakan orang yang sama, tapi HP dipakai orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()
					&& nikUser.getAmMsuser().getIdMsUser() != phoneUser.getAmMsuser().getIdMsUser()) {
                        throw new EsignhubJobException(MSG_NIK_USED_BY_OTHER_EMAIL);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, HP dan Email digunakan orang yang sama, tapi NIK dipakai orang yang berbeda
			if (phoneUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()
					&& phoneUser.getAmMsuser().getIdMsUser() != nikUser.getAmMsuser().getIdMsUser()) {
				throw new EsignhubJobException(MSG_NIK_USED_BY_OTHER_PHONE_EMAIL);
			}
		}
		
		// NIK belum terpakai
		
		// NIK belum terpakai, HP sudah terpakai
		if (null != phoneUser) {
			// NIK belum terpakai, HP sudah terpakai, Email belum terpakai
			if (null == emailUser) {
				throw new EsignhubJobException("No Telp sudah digunakan oleh NIK dan email yang berbeda dari data yang dikirim");
			}
			
			// NIK belum terpakai, HP sudah terpakai, Email sudah terpakai
			throw new EsignhubJobException("No Telp dan Email sudah digunakan oleh NIK yang berbeda dari data yang dikirim");
		}
		
		// NIK belum terpakai, HP belum terpakai
		
		// NIK belum terpakai, HP belum terpakai, Email sudah terpakai
		if (null != emailUser) {
            context.getLogger().info("NIK TIDAK KETEMU EMAIL KETEMU");
            AmMsuser user = daoFactory.getUserDao().getUserByIdMsUser(emailUser.getAmMsuser().getIdMsUser());
            String hashedNik = Tools.getHashedString(nik);

            context.getLogger().info("nik" + user.getHashedIdNo());
            
            // NIK 0, PHONE0, EMAIL NIK 0, USER MANAGEMENT
            if (user.getHashedIdNo() == null || hashedNik.equals(user.getHashedIdNo())) {
                return;
            // EMAIL NIK != NIK REJECT
            } else if (!hashedNik.equals(user.getHashedIdNo())) {
                throw new EsignhubJobException(MSG_EMAIL_NOT_OWNED_BY_NIK + Tools.maskData(nik));
            }
        }
		
		// NIK belum terpakai, HP belum terpakai, Email belum terpakai, lanjut registrasi
    }

    private void validateNikPhoneEmailWithOtherVendor(String nik, String phone, String email, MsVendor vendor, Context context) {
        Long idNikUser = daoFactory.getUserDao().getIdMsUserRegisteredInOtherVendorByNik(nik, vendor);
		Long idPhoneUser = daoFactory.getUserDao().getIdMsUserRegisteredInOtherVendorByPhone(phone, vendor);
		Long idEmailUser = daoFactory.getUserDao().getIdMsUserRegisteredInOtherVendorByEmail(email, vendor);
		
		// NIK 0
		if (null == idNikUser) {
			// NIK 0, PHONE 0
			if (null == idPhoneUser) {
				// NIK 0, PHONE 0, EMAIL 1
				if (null != idEmailUser) {
                    AmMsuser user = daoFactory.getUserDao().getUserByIdMsUser(idEmailUser);
                    String hashedNik = Tools.getHashedString(nik);
                    
                    // NIK 0, PHONE0, EMAIL NIK 0, USER MANAGMENET
                    if (user.getHashedIdNo() == null || hashedNik.equals(user.getHashedIdNo())) {
                        return;
                    // EMAIL NIK != NIK REJECT
                    } else if (!hashedNik.equals(user.getHashedIdNo())) {
                        throw new EsignhubJobException(MSG_EMAIL_NOT_OWNED_BY_NIK + Tools.maskData(nik));
                    }
				}
				
				// NIK 0, PHONE 0, EMAIL 0
				return;
			}
			
			// NIK 0, PHONE 1
			
			// NIK 0, PHONE 1, EMAIL 0
			if (null == idEmailUser) {
				throw new EsignhubJobException(MSG_PHONE_NOT_OWNED_BY_NIK + Tools.maskData(nik));
			}
			
			// NIK 0, PHONE 1, EMAIL 1
			throw new EsignhubJobException("No Telp dan email bukan milik NIK " + Tools.maskData(nik));
		}
		
		// NIK 1
		
		// NIK 1, PHONE 0
		if (null == idPhoneUser) {
			// NIK 1, PHONE 0, EMAIL 0
			if (null == idEmailUser) {
                AmMsuser user = daoFactory.getUserDao().getUserByIdMsUser(idNikUser);

                context.getLogger().info(user.getLoginId());
               
                // NIK 1, PHONE 0, EMAIL EXCEL != EMAIL NIK REJECT
                if (!user.getLoginId().equals(email)) {
                    throw new EsignhubJobException(MSG_NIK_USED_BY_OTHER_EMAIL + Tools.maskData(user.getLoginId()));
                }
				return;
			}
			
			// NIK 1, PHONE 0, EMAIL 1
			
			// NIK 1, PHONE 0, EMAIL 1, NIK dan EMAIL dipakai orang yang sama
			if (idEmailUser.equals(idNikUser)) {
				return;
			}
			
			// NIK 1, PHONE 0, EMAIL 1, NIK dan EMAIL dipakai orang yang berbeda
			throw new EsignhubJobException(MSG_EMAIL_NOT_OWNED_BY_NIK + Tools.maskData(nik));
		}
		
		// NIK 1, PHONE 1
		
		// NIK 1, PHONE 1, EMAIL 0
		if (null == idEmailUser) {
			// NIK 1, PHONE 1, EMAIL 0, NIK dan PHONE dipakai orang yang sama
			if (idNikUser.equals(idPhoneUser)) {
				return;
			}
			
			// NIK 1, PHONE 1, EMAIL 0, NIK dan PHONE dipakai orang yang berbeda
			throw new EsignhubJobException(MSG_PHONE_NOT_OWNED_BY_NIK + Tools.maskData(nik));
			
		}
		
		// NIK 1, PHONE 1, EMAIL 1
		boolean nikPhoneSameUser = idNikUser.equals(idPhoneUser);
		boolean nikEmailSameUser = idNikUser.equals(idEmailUser);
		
		if (!nikPhoneSameUser) {
			if (!nikEmailSameUser) {
				throw new EsignhubJobException("No Telp dan email bukan milik NIK " + Tools.maskData(nik));
			}
			
			throw new EsignhubJobException(MSG_PHONE_NOT_OWNED_BY_NIK + Tools.maskData(nik));
		}
		
		if (!nikEmailSameUser) {
			throw new EsignhubJobException(MSG_EMAIL_NOT_OWNED_BY_NIK + Tools.maskData(nik));
		}
    }

    private void insertUnregisteredUseroftenant(AmMsuser user, MsTenant tenant, Context context) {
        MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
        if (null == useroftenant) {
            useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(context.getRequestId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
        }
    }

    private void insertUnregisteredVendorUser(AmMsuser user, MsVendor vendor, ProcessAutosignBean bean, Context context) {
        MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserNewTran(user, vendor);
        Date activatedDate = new Date();
        byte[] phoneBytea = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getPhone());
        context.getLogger().info("cert expired to ms_vendor is" + bean.getCertExpiredDate().toString());

        if (null == vendorUser) {
            vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(bean.getEmail()));
			vendorUser.setIsActive("1");
			vendorUser.setUsrCrt(context.getRequestId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(vendor);
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertPoaExpiredDate(bean.getCertExpiredDate());
			vendorUser.setHashedSignerRegisteredPhone(Tools.getHashedString(bean.getPhone()));
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService("0");
            vendorUser.setPoaId(bean.getPoaId());
			vendorUser.setVendorUserAutosignCvv(bean.getCvv());
            vendorUser.setVendorUserAutosignKey(bean.getSecretKey());
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);

            return;
        }

        // Kalau credentials sama, update activation & expired date
        if (bean.getCvv().equals(vendorUser.getVendorUserAutosignCvv()) && bean.getPoaId().equals(vendorUser.getPoaId()) && bean.getSecretKey().equals(vendorUser.getVendorUserAutosignKey())) {

            vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(bean.getEmail()));
            vendorUser.setHashedSignerRegisteredPhone(Tools.getHashedString(bean.getPhone()));
			vendorUser.setPhoneBytea(phoneBytea);
            vendorUser.setIsRegistered("1");
            vendorUser.setIsActive("1");
            vendorUser.setEmailService("0");
            vendorUser.setUsrUpd(context.getRequestId());
            vendorUser.setDtmUpd(new Date());
            daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUserNewTran(vendorUser);
            return;
        }

        vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(bean.getEmail()));
        vendorUser.setHashedSignerRegisteredPhone(Tools.getHashedString(bean.getPhone()));
        vendorUser.setPhoneBytea(phoneBytea);
        vendorUser.setIsRegistered("1");
        vendorUser.setIsActive("1");
        vendorUser.setEmailService("0");
        vendorUser.setActivatedDate(activatedDate);
        vendorUser.setPoaId(bean.getPoaId());
        vendorUser.setVendorUserAutosignCvv(bean.getCvv());
        vendorUser.setVendorUserAutosignKey(bean.getSecretKey());
        vendorUser.setUsrUpd(context.getRequestId());
        vendorUser.setDtmUpd(new Date());
        vendorUser.setCertPoaExpiredDate(bean.getCertExpiredDate());
        daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUserNewTran(vendorUser);

    }

    private void insertUnregisteredPersonalData(AmMsuser user, ProcessAutosignBean bean, Context context) {
        PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false, context);
        if (null == personalData || null == personalData.getUserPersonalData()) {
            Date dateOfBirth = Tools.formatStringToDate(bean.getDob(), Constants.DATE_FORMAT);

            ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();

            PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(context.getRequestId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(bean.getGender()));
			userPersonalData.setDateOfBirth(dateOfBirth);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(bean.getPob()));
			userPersonalData.setEmail(StringUtils.upperCase(bean.getEmail()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setIdNoRaw(bean.getNik());
			personalDataBean.setPhoneRaw(bean.getPhone());
            daoFactory.getUserDao().insertUserPersonalDataNewTrx(personalDataBean, context);
        } else if (null == personalData.getIdNoRaw()) {
            Date dateOfBirth = Tools.formatStringToDate(bean.getDob(), Constants.DATE_FORMAT);

            personalData.getUserPersonalData().setGender(StringUtils.upperCase(bean.getGender()));
			personalData.getUserPersonalData().setDateOfBirth(dateOfBirth);
			personalData.getUserPersonalData().setAmMsuser(user);
			personalData.getUserPersonalData().setPlaceOfBirth(StringUtils.upperCase(bean.getPob()));
			personalData.getUserPersonalData().setEmail(StringUtils.upperCase(bean.getEmail()));
			personalData.setIdNoRaw(bean.getNik());
			personalData.setPhoneRaw(bean.getPhone());

            daoFactory.getUserDao().updateUserPersonalDataNewTrx(personalData, context);
        } else {
            personalData.getUserPersonalData().setUsrUpd(context.getRequestId());
            personalData.getUserPersonalData().setDtmUpd(new Date());
            personalData.getUserPersonalData().setEmail(StringUtils.upperCase(bean.getEmail()));
            personalData.setPhoneRaw(bean.getPhone());
            daoFactory.getUserDao().updateUserPersonalDataNewTrx(personalData, context);
        }
    }

    private void insertUnregisteredMemberofrole(AmMsuser user, AmMsrole role, Context context) {
        AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
        if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(context.getRequestId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}
    }

    private ProcessAutosignBean getAutosignData(Row row) {
        String nik = ExcelUtils.getCellValue(row.getCell(2)).trim();
        String name = ExcelUtils.getCellValue(row.getCell(3)).trim();
        String pob = ExcelUtils.getCellValue(row.getCell(4)).trim();
        String dob = ExcelUtils.getCellValue(row.getCell(5)).trim();
        String email = ExcelUtils.getCellValue(row.getCell(6)).trim();
        String phone = ExcelUtils.getCellValue(row.getCell(7)).trim();
        String gender = ExcelUtils.getCellValue(row.getCell(8)).trim();
        String poaId = ExcelUtils.getCellValue(row.getCell(9)).trim();
        String cvv = ExcelUtils.getCellValue(row.getCell(10)).trim();
        String secretKey = ExcelUtils.getCellValue(row.getCell(11)).trim();
        Cell cell = row.getCell(12);

        String certExpiredDate = (cell == null || ExcelUtils.getCellValue(cell).trim().isEmpty()) 
                                 ? null 
                                 : ExcelUtils.getCellValue(cell).trim();
        
        ProcessAutosignBean bean = new ProcessAutosignBean();
        bean.setNik(nik);
        bean.setName(name);
        bean.setPob(pob);
        bean.setDob(dob);
        bean.setEmail(email);
        bean.setPhone(phone);
        bean.setGender(gender);
        bean.setPoaId(poaId);
        bean.setCvv(cvv);
        bean.setSecretKey(secretKey);
        bean.setTempCertExpiredDate(certExpiredDate);
 
        return bean;
    }

    private void validateProcessAutosignBean(ProcessAutosignBean bean) {
        validateNik(bean.getNik());
        validateNotEmpty(bean.getName(), "Nama");
        validateNotEmpty(bean.getPob(), "Tempat Lahir");
        validateDob(bean.getDob());
        validateEmail(bean.getEmail());
        validatePhone(bean.getPhone());
        validateGender(bean.getGender());
        validateNotEmpty(bean.getPoaId(), "PoA ID");
        validateCvv(bean.getCvv());
        validateNotEmpty(bean.getSecretKey(), "Secret Key");
    }

    private void validateNik(String nik) {
        validateNotEmpty(nik, "NIK");

        if (!StringUtils.isNumeric(nik)) {
            throw new EsignhubJobException("NIK harus diisi angka");
        }

        if (nik.length() != 16) {
            throw new EsignhubJobException("NIK harus 16 digit");
        }
    }

    private void validateDob(String dob) {
        validateNotEmpty(dob, "Tanggal Lahir");
        SimpleDateFormat sdf = new SimpleDateFormat(Constants.DATE_FORMAT);
        sdf.setLenient(false);
        try {
            sdf.parse(dob);
        } catch (Exception e) {
            throw new EsignhubJobException("Tanggal Lahir harus dalam format yyyy-MM-dd");
        }
    }

    private void validateEmail(String email) {
        validateNotEmpty(email, "Email");
        String emailRegex = daoFactory.getGeneralSettingDao().getGsValueByCode("AM_EMAIL_FORMAT");
        if (!email.matches(emailRegex)) {
            throw new EsignhubJobException("Format <NAME_EMAIL>");
        }
    }

    private void validatePhone(String phone) {
        validateNotEmpty(phone, "No HP");
        String phoneRegex = daoFactory.getGeneralSettingDao().getGsValueByCode("AM_PHONE_FORMAT");
        if (!phone.matches(phoneRegex)) {
            throw new EsignhubJobException("Format No HP tidak valid");
        }
    }

    private void validateGender(String gender) {
        validateNotEmpty(gender, "Jenis Kelamin");
        if (!"M".equals(gender) && !"F".equals(gender)) {
            throw new EsignhubJobException("Jenis Kelamin harus diisi M atau F");
        }
    }

    private void validateCvv(String cvv) {
        validateNotEmpty(cvv, "CVV");
        if (!StringUtils.isNumeric(cvv)) {
            throw new EsignhubJobException("CVV harus diisi angka");
        }

        if (cvv.length() != 3) {
            throw new EsignhubJobException("CVV harus 3 digit");
        }
    }

    private void validateNotEmpty(String object, String objectName) {
        if (StringUtils.isBlank(object)) {
            throw new EsignhubJobException(objectName + " harus diisi");
        }
    }
}
