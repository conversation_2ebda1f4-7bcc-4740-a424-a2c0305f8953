package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;

import com.adins.esignhubjob.BaseJobHandler;
import com.aliyun.fc.runtime.Context;

public class TemporaryRetrySignVidaJob  extends BaseJobHandler{

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        BigInteger requestProcessed = daoFactory.getCommonDao().temporaryRetrySignVida();
        if (null == requestProcessed || requestProcessed.longValue() == 0) {
            context.getLogger().info("0 data processed");
            return;
        }

        context.getLogger().info(requestProcessed.longValue() + " data(s) processed");
    }
    
}
