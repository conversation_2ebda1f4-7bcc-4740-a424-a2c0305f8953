package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.StampDutyDao;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrStampDuty;
import com.adins.util.Tools;

@Transactional
@Component
public class StampDutyDaoHbn extends BaseDaoHbn implements StampDutyDao {

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertStampDuty(TrStampDuty stampDuty) {
        stampDuty.setUsrCrt(Tools.maskData(stampDuty.getUsrCrt()));
        managerDAO.insert(stampDuty);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrStampDuty getStampDutyByTrxNo(String trxNo) {
        Map<String, Object> params = new HashMap<>();
		params.put("trxNo", trxNo);

        return managerDAO.selectOne(
            "from TrStampDuty "
            + "where trxNo = :trxNo ", params);
    }
    
    @Override
	public BigInteger countAvailableStampDutyForDocument(Long idDocumentD) {
		StringBuilder query = new StringBuilder();
		Object[][] params = new Object[][] {{ TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD }};
		
		query
			.append(" select count(*) from tr_stamp_duty sd ")
			.append(" join lateral ( ")
				.append(" select id_lov from ms_lov ")
				.append(" where lov_group = '" + Constants.LOV_GROUP_STAMP_DUTY_STATUS + "' ")
				.append(" and code = '" + Constants.LOV_CODE_STAMP_DUTY_STATUS_AVAILABLE + "' ")
				.append(" and sd.lov_stamp_duty_status = id_lov ")
			.append(" ) status on true ")
			.append(" join lateral ( ")
				.append(" select id_balance_mutation ")
				.append(" from tr_balance_mutation bm ")
				.append(" where bm.id_stamp_duty = sd.id_stamp_duty ")
//				.append(" and bm.notes = sd.stamp_duty_no ")
				.append(" and bm.qty = -1 ")
				.append(" and bm.id_document_d = :idDocumentD ")
				.append(" limit 1 ")
			.append(" ) mutation on true ");
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

    @Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateTrStampDutyNewTran(TrStampDuty stampDuty) {
		stampDuty.setUsrUpd(Tools.maskData(stampDuty.getUsrUpd()));
		this.managerDAO.update(stampDuty);
	}
}
