package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.VendoroftenantDao;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendoroftenant;

@Component
@Transactional
public class VendoroftenantDaoHbn extends BaseDaoHbn implements VendoroftenantDao {

    @Override
    @SuppressWarnings("unchecked")
    public List<MsVendoroftenant> getListVendoroftenant(String vendorCode) {
        Object[][] params = {{ MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode) }};
        return (List<MsVendoroftenant>) managerDAO.list(
            "from MsVendoroftenant vot "
            + "join fetch vot.msTenant mt "
            + "join fetch vot.msVendor mv "
            + "where mv.vendorCode = :vendorCode ", params);
    }

    @Override
    public MsVendoroftenant getVendoroftenant(String tenantCode, String vendorCode) {
        Map<String, Object> params = new HashMap<>();
        params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
        params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));

        return managerDAO.selectOne(
            "from MsVendoroftenant vot "
            + "join fetch vot.msTenant mt "
            + "join fetch vot.msVendor mv "
            + "where mt.tenantCode = :tenantCode "
            + "and mv.vendorCode = :vendorCode ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MsVendoroftenant getVendoroftenantNewTran(String tenantCode, String vendorCode) {
        Map<String, Object> params = new HashMap<>();
        params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
        params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));

        return managerDAO.selectOne(
            "from MsVendoroftenant vot "
            + "join fetch vot.msTenant mt "
            + "join fetch vot.msVendor mv "
            + "where mt.tenantCode = :tenantCode "
            + "and mv.vendorCode = :vendorCode ", params);
    }
}
