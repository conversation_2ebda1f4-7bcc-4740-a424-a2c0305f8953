package com.adins.esignhubjob.businesslogic.factory.api;

import com.adins.esignhubjob.businesslogic.api.CallbackLogic;
import com.adins.esignhubjob.businesslogic.api.CommonLogic;
import com.adins.esignhubjob.businesslogic.api.CommonStampingLogic;
import com.adins.esignhubjob.businesslogic.api.EmailSenderLogic;
import com.adins.esignhubjob.businesslogic.api.MessageTemplateLogic;
import com.adins.esignhubjob.businesslogic.api.NotificationSenderLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.SignImageLogic;
import com.adins.esignhubjob.businesslogic.api.SigningProcessAuditTrailLogic;
import com.adins.esignhubjob.businesslogic.api.TenantLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.BalanceValidatorLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SchedulerLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SmsJatisLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SmsVfirstLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.DocumentLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.EmateraiPajakkuLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.PrivyLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.VidaLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.WhatsAppJatisLogic;
public interface LogicFactory {

    AliyunOssCloudStorageLogic getAliyunOssCloudStorageLogic();

    CallbackLogic getCallbackLogic();

    CommonStampingLogic getCommonStampingLogic();

    DigisignLogic getDigisignLogic();

    DocumentLogic getDocumentLogic();

    EmailSenderLogic getEmailSenderLogic();

    EmateraiPajakkuLogic getEmateraiPajakkuLogic();

    PrivyLogic getPrivyLogic();

    PrivyGeneralLogic getPrivyGeneralLogic();

    SchedulerLogic getSchedulerLogic();

    SignImageLogic getSignImageLogic();

    VidaLogic getVidaLogic();

    MessageTemplateLogic getMessageTemplateLogic();

    TenantLogic getTenantLogic();

    SmsJatisLogic getSmsJatisLogic();

    SmsVfirstLogic getSmsVfirstLogic();

    PersonalDataEncryptionLogic getPersonalDataEncryptionLogic();

    WhatsAppJatisLogic getWhatsAppJatisLogic();

    CommonLogic getCommonLogic();

    NotificationSenderLogic getNotificationSenderLogic();

    WhatsAppHalosisLogic getWhatsAppHalosisLogic();

    SigningProcessAuditTrailLogic getSigningProcessAuditTrailLogic();

    BalanceValidatorLogic getBalanceValidatorLogic();

}
