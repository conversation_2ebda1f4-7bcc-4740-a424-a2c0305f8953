package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_document_d")
public class TrDocumentD extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	public static final String ID_DOCUMENT_D_HBM = "idDocumentD";
	public static final String DOCUMENT_ID_HBM = "documentId";
	public static final String TENANT_TRANSACTION_ID_HBM = "tenantTransactionId";
	
	private long idDocumentD;
	private MsDocTemplate msDocTemplate;
	private MsLov msLovByLovSignStatus;
	private MsLov msLovByLovPaymentSignType;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private TrDocumentH trDocumentH;
	private String isSequence;
	private String documentId;
	private Date requestDate;
	private Date completedDate;
	private String repositoryUrl;
	private Short totalSign;
	private Short totalSigned;
	private Short totalMaterai;
	private Short totalStamping;
	private String transactionId;
	private String sdtProcess;
	private String documentIdSdt;
	private String documentNameSdt;
	private String documentSdtLink;
	private Short sendStatus;
	private String psreDocumentId;
	private String documentName;
	private Double documentNominal;
	private String idName;
	private String idNo;
	private MsPeruriDocType msPeruriDocType;
	private MsLov msLovIdType;
	private String tenantTransactionId;
	private String signingProcess;
	private String documentStorageStatus;
	private String useSignQr;
	private Date documentDeletedDate;
	private Date documentArchivedDate;
	private String archiveDocumentStatus;
	private byte[] idNameBytea;
	private byte[] idNoBytea;

	private Set<TrDocumentDSign> trDocumentDSigns = new HashSet<>(0);
	private Set<TrFeedback> trFeedbacks = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);
	private Set<TrFaceVerify> trFaceVerifies = new HashSet<>(0);
	private Set<TrDocumentDStampduty> trDocumentDStampduties = new HashSet<>(0);
	private Set<TrDocumentHStampdutyError> trDocumentHStampdutyErrors = new HashSet<>(0);
	private Set<TrDocumentSigningRequest> trDocumentSigningRequests = new HashSet<>(0);
	private Set<TrDocumentSigningRequestDetail> trDocumentSigningRequestDetails = new HashSet<>(0);
	private Set<TrClientCallbackRequest> trClientCallbackRequests = new HashSet<>(0);

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_document_d", unique = true, nullable = false)
	public long getIdDocumentD() {
		return this.idDocumentD;
	}

	public void setIdDocumentD(long idDocumentD) {
		this.idDocumentD = idDocumentD;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_doc_template")
	public MsDocTemplate getMsDocTemplate() {
		return this.msDocTemplate;
	}

	public void setMsDocTemplate( MsDocTemplate msDocTemplate) {
		this.msDocTemplate = msDocTemplate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sign_status", nullable = false)
	public MsLov getMsLovByLovSignStatus() {
		return this.msLovByLovSignStatus;
	}

	public void setMsLovByLovSignStatus(MsLov msLovByLovSignStatus) {
		this.msLovByLovSignStatus = msLovByLovSignStatus;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_payment_sign_type")
	public MsLov getMsLovByLovPaymentSignType() {
		return this.msLovByLovPaymentSignType;
	}
	
	public void setMsLovByLovPaymentSignType(MsLov msLovByLovPaymentSignType) {
		this.msLovByLovPaymentSignType = msLovByLovPaymentSignType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_id_type")
	public MsLov getMsLovIdType() {
		return msLovIdType;
	}

	public void setMsLovIdType(MsLov msLovIdType) {
		this.msLovIdType = msLovIdType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return this.msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_h", nullable = false)
	public TrDocumentH getTrDocumentH() {
		return this.trDocumentH;
	}

	public void setTrDocumentH(TrDocumentH trDocumentH) {
		this.trDocumentH = trDocumentH;
	}

	@Column(name = "is_sequence", length = 1)
	public String getIsSequence() {
		return this.isSequence;
	}

	public void setIsSequence(String isSequence) {
		this.isSequence = isSequence;
	}

	@Column(name = "document_id", nullable = false, length = 50)
	public String getDocumentId() {
		return this.documentId;
	}

	public void setDocumentId(String documentId) {
		this.documentId = documentId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "request_date", length = 29)
	public Date getRequestDate() {
		return this.requestDate;
	}

	public void setRequestDate(Date requestDate) {
		this.requestDate = requestDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "completed_date", length = 29)
	public Date getCompletedDate() {
		return this.completedDate;
	}

	public void setCompletedDate(Date completedDate) {
		this.completedDate = completedDate;
	}

	@Column(name = "repository_url", length = 200)
	public String getRepositoryUrl() {
		return this.repositoryUrl;
	}

	public void setRepositoryUrl(String repositoryUrl) {
		this.repositoryUrl = repositoryUrl;
	}

	@Column(name = "total_sign")
	public Short getTotalSign() {
		return this.totalSign;
	}

	public void setTotalSign(Short totalSign) {
		this.totalSign = totalSign;
	}

	@Column(name = "total_signed")
	public Short getTotalSigned() {
		return this.totalSigned;
	}

	public void setTotalSigned(Short totalSigned) {
		this.totalSigned = totalSigned;
	}

	@Column(name = "total_materai")
	public Short getTotalMaterai() {
		return this.totalMaterai;
	}

	public void setTotalMaterai(Short totalMaterai) {
		this.totalMaterai = totalMaterai;
	}

	@Column(name = "total_stamping")
	public Short getTotalStamping() {
		return this.totalStamping;
	}

	public void setTotalStamping(Short totalStamping) {
		this.totalStamping = totalStamping;
	}

	@Column(name = "transaction_id", length = 300)
	public String getTransactionId() {
		return this.transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}
	
	@Column(name = "sdt_process", length = 20)
	public String getSdtProcess() {
		return this.sdtProcess;
	}

	public void setSdtProcess(String sdtProcess) {
		this.sdtProcess = sdtProcess;
	}
	
	@Column(name = "document_id_sdt", length = 300)
	public String getDocumentIdSdt() {
		return documentIdSdt;
	}

	public void setDocumentIdSdt(String documentIdSdt) {
		this.documentIdSdt = documentIdSdt;
	}

	@Column(name = "document_name_sdt", length = 350)
	public String getDocumentNameSdt() {
		return this.documentNameSdt;
	}

	public void setDocumentNameSdt(String documentNameSdt) {
		this.documentNameSdt = documentNameSdt;
	}

	@Column(name = "document_sdt_link", length = 700)
	public String getDocumentSdtLink() {
		return this.documentSdtLink;
	}
	
	public void setDocumentSdtLink(String documentSdtLink) {
		this.documentSdtLink = documentSdtLink;
	}
	
	@Column(name = "send_status")
	public Short getSendStatus() {
		return sendStatus;
	}

	public void setSendStatus(Short sendStatus) {
		this.sendStatus = sendStatus;
	}

	@Column(name = "psre_document_id")
	public String getPsreDocumentId() {
		return psreDocumentId;
	}

	public void setPsreDocumentId(String psreDocumentId) {
		this.psreDocumentId = psreDocumentId;
	}
	
	@Column(name = "document_name", length = 100)
	public String getDocumentName() {
		return documentName;
	}

	public void setDocumentName(String documentName) {
		this.documentName = documentName;
	}

	@Column(name = "document_nominal")
	public Double getDocumentNominal() {
		return documentNominal;
	}

	public void setDocumentNominal(Double documentNominal) {
		this.documentNominal = documentNominal;
	}

	@Column(name = "id_name", length = 60)
	public String getIdName() {
		return idName;
	}

	public void setIdName(String idName) {
		this.idName = idName;
	}

	@Column(name = "id_no", length = 30)
	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	
	@Column(name = "tenant_transaction_id", length = 50)
	public String getTenantTransactionId() {
		return tenantTransactionId;
	}

	public void setTenantTransactionId(String tenantTransactionId) {
		this.tenantTransactionId = tenantTransactionId;
	}
	
	@Column(name = "signing_process", length = 1)
	public String getSigningProcess() {
		return signingProcess;
	}

	public void setSigningProcess(String signingProcess) {
		this.signingProcess = signingProcess;
	}

	@Column(name = "document_storage_status", length = 1)
	public String getDocumentStorageStatus() {
		return documentStorageStatus;
	}

	public void setDocumentStorageStatus(String documentStorageStatus) {
		this.documentStorageStatus = documentStorageStatus;
	}

	@Column(name = "use_sign_qr", length = 1)
	public String getUseSignQr() {
		return useSignQr;
	}

	public void setUseSignQr(String useSignQr) {
		this.useSignQr = useSignQr;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "document_deleted_date", length = 29)
	public Date getDocumentDeletedDate() {
		return documentDeletedDate;
	}

	public void setDocumentDeletedDate(Date documentDeletedDate) {
		this.documentDeletedDate = documentDeletedDate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_peruri_doc_type")
	public MsPeruriDocType getMsPeruriDocType() {
		return msPeruriDocType;
	}

	public void setMsPeruriDocType(MsPeruriDocType msPeruriDocType) {
		this.msPeruriDocType = msPeruriDocType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrDocumentDSign> getTrDocumentDSigns() {
		return this.trDocumentDSigns;
	}

	public void setTrDocumentDSigns(Set<TrDocumentDSign> trDocumentDSigns) {
		this.trDocumentDSigns = trDocumentDSigns;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrFeedback> getTrFeedbacks() {
		return this.trFeedbacks;
	}

	public void setTrFeedbacks(Set<TrFeedback> trFeedbacks) {
		this.trFeedbacks = trFeedbacks;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return this.trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrFaceVerify> getTrFaceVerifies() {
		return this.trFaceVerifies;
	}

	public void setTrFaceVerifies(Set<TrFaceVerify> trFaceVerifies) {
		this.trFaceVerifies = trFaceVerifies;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrDocumentDStampduty> getTrDocumentDStampduties() {
		return this.trDocumentDStampduties;
	}

	public void setTrDocumentDStampduties(Set<TrDocumentDStampduty> trDocumentDStampduties) {
		this.trDocumentDStampduties = trDocumentDStampduties;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrDocumentHStampdutyError> getTrDocumentHStampdutyErrors() {
		return trDocumentHStampdutyErrors;
	}

	public void setTrDocumentHStampdutyErrors(Set<TrDocumentHStampdutyError> trDocumentHStampdutyErrors) {
		this.trDocumentHStampdutyErrors = trDocumentHStampdutyErrors;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrDocumentSigningRequest> getTrDocumentSigningRequests() {
		return trDocumentSigningRequests;
	}

	public void setTrDocumentSigningRequests(Set<TrDocumentSigningRequest> trDocumentSigningRequests) {
		this.trDocumentSigningRequests = trDocumentSigningRequests;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrDocumentSigningRequestDetail> getTrDocumentSigningRequestDetails() {
		return trDocumentSigningRequestDetails;
	}

	public void setTrDocumentSigningRequestDetails(Set<TrDocumentSigningRequestDetail> trDocumentSigningRequestDetails) {
		this.trDocumentSigningRequestDetails = trDocumentSigningRequestDetails;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentD")
	public Set<TrClientCallbackRequest> getTrClientCallbackRequests() {
		return trClientCallbackRequests;
	}

	public void setTrClientCallbackRequests(Set<TrClientCallbackRequest> trClientCallbackRequests) {
		this.trClientCallbackRequests = trClientCallbackRequests;
	}

	@Column(name = "document_archive_date")
	public Date getDocumentArchivedDate() {
		return this.documentArchivedDate;
	}

	public void setDocumentArchivedDate(Date documentArchivedDate) {
		this.documentArchivedDate = documentArchivedDate;
	}

	@Column(name = "archive_document_status", length = 1)
	public String getArchiveDocumentStatus() {
		return this.archiveDocumentStatus;
	}

	public void setArchiveDocumentStatus(String archiveDocumentStatus) {
		this.archiveDocumentStatus = archiveDocumentStatus;
	}

	@Column(name = "id_name_bytea")
	public byte[] getIdNameBytea() {
		return idNameBytea;
	}

	public void setIdNameBytea(byte[] idNameBytea) {
		this.idNameBytea = idNameBytea;
	}

	@Column(name = "id_no_bytea")
	public byte[] getIdNoBytea() {
		return idNoBytea;
	}

	public void setIdNoBytea(byte[] idNoBytea) {
		this.idNoBytea = idNoBytea;
	}
}
