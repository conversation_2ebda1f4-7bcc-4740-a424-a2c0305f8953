package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.MessageDeliveryReportDao;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrMessageDeliveryReport;
import com.adins.util.Tools;

@Component
@Transactional
public class MessageDeliveryReportDaoHbn extends BaseDaoHbn implements MessageDeliveryReportDao {

    @Override
    public void insertMessageDeliveryReport(TrMessageDeliveryReport report) {
        report.setUsrCrt(Tools.maskData(report.getUsrCrt()));
        managerDAO.insert(report);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertMessageDeliveryReportNewTrx(TrMessageDeliveryReport report) {
        report.setUsrCrt(Tools.maskData(report.getUsrCrt()));
        managerDAO.insert(report);
    }

    @Override
    public TrMessageDeliveryReport getMessageDeliveryReport(String vendorTrxNo, Date reportTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("vendorTrxNo", vendorTrxNo);
        params.put("reportTime", reportTime);

        return managerDAO.selectOne(
            "from TrMessageDeliveryReport dr "
            + "where dr.vendorTrxNo = :vendorTrxNo "
            + "and dr.reportTime = :reportTime ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrMessageDeliveryReport getMessageDeliveryReportNewTrx(String vendorTrxNo, Date reportTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("vendorTrxNo", vendorTrxNo);
        params.put("reportTime", reportTime);

        return managerDAO.selectOne(
            "from TrMessageDeliveryReport dr "
            + "where dr.vendorTrxNo = :vendorTrxNo "
            + "and dr.reportTime = :reportTime ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrMessageDeliveryReport getMessageDeliveryReportByTrxNo(String trxNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("trxNo", trxNo);
        
        return managerDAO.selectOne(
            "from TrMessageDeliveryReport dr "
            + "where dr.trxNo = :trxNo ", params);
    }

    @Override
    public TrMessageDeliveryReport getLatestWhatsAppMessageDeliveryReport(MsTenant tenant, String phone, String deliveryStatus) {
        Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
		params.put("phone", phone);
		params.put(MsLov.LOV_GROUP_HBM, "MESSAGE_MEDIA");
		params.put(MsLov.CODE_HBM, "WA");
		params.put("deliveryStatus", deliveryStatus);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_message_delivery_report ")
			.append("from tr_message_delivery_report mdr ")
			.append("join ms_lov ml on mdr.lov_message_media = ml.id_lov ")
			.append("where mdr.id_ms_tenant = :idMsTenant ")
			.append("and mdr.recipient_detail = :phone ")
			.append("and ml.lov_group = :lovGroup ")
			.append("and ml.code = :code ")
			.append("and mdr.delivery_status = :deliveryStatus ")
			.append("order by mdr.id_message_delivery_report desc limit 1 ");
		
		BigInteger idMessageDeliveryReport = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMessageDeliveryReport) {
			return null;
		}
		
		Map<String, Object> trueParam = new HashMap<>();
		trueParam.put("idMessageDeliveryReport", idMessageDeliveryReport.longValue());
		return managerDAO.selectOne("from TrMessageDeliveryReport mdr "
				+ "where mdr.idMessageDeliveryReport = :idMessageDeliveryReport ", trueParam);
    }
    
}
