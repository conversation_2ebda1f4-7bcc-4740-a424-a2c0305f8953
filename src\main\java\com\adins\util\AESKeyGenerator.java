package com.adins.util;

import java.security.SecureRandom;

import org.bouncycastle.crypto.CipherParameters;
import org.bouncycastle.crypto.digests.SHA1Digest;
import org.bouncycastle.crypto.generators.PKCS5S1ParametersGenerator;
import org.bouncycastle.crypto.params.KeyParameter;

public class AESKeyGenerator {

	private static final SecureRandom sr = new SecureRandom();
	
	public AESKeyGenerator() {
		super();
	}

	public KeyParameter generateNewAESKey(byte[] seeds) {
		return new KeyParameter(seeds);
	}

	public byte[] generateNewSeeds() {
		return sr.generateSeed(32);
	}

	public byte[] generateAESKey(String phrase, byte[] salt, int count) {
		PKCS5S1ParametersGenerator paramGen = new PKCS5S1ParametersGenerator(new SHA1Digest());

		byte[] phraseInBytes = org.bouncycastle.crypto.PBEParametersGenerator
				.PKCS5PasswordToBytes(phrase.toCharArray());

		paramGen.init(phraseInBytes, salt, count);

		CipherParameters ciphParams = paramGen.generateDerivedParameters(64);

		KeyParameter keyParams = (KeyParameter) ciphParams;

		return keyParams.getKey();
	}
}
