package com.adins.esignhubjob.model.custom.vida;

public class VidaDeviceBean {
    private String os;
    private String model;
    private String uniqueId;
    private String networkProvider;

    public VidaDeviceBean() {
    }

    public VidaDeviceBean(String os, String model, String uniqueId, String networkProvider) {
        this.os = os;
        this.model = model;
        this.uniqueId = uniqueId;
        this.networkProvider = networkProvider;
    }

    public static VidaDeviceBean getDummyInstance() {
        return new VidaDeviceBean("R", "KB2001", "AF7KI0bly3aPIsYJ4+O+2QE", "IND idea");
    }

    public String getOs() {
        return this.os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUniqueId() {
        return this.uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getNetworkProvider() {
        return this.networkProvider;
    }

    public void setNetworkProvider(String networkProvider) {
        this.networkProvider = networkProvider;
    }
}