package com.adins.esignhubjob.model.custom.privy;

import com.google.gson.annotations.SerializedName;

public class PrivyRejectHandlerBean {
    private String category;
    private String handler;
    @SerializedName("file_support") private Object fileSupport;

    public String getCategory() {
        return this.category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getHandler() {
        return this.handler;
    }

    public void setHandler(String handler) {
        this.handler = handler;
    }

    public Object getFileSupport() {
        return this.fileSupport;
    }

    public void setFileSupport(Object fileSupport) {
        this.fileSupport = fileSupport;
    }

}
