package com.adins.esignhubjob.businesslogic.api;

import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.aliyun.fc.runtime.Context;

public interface CallbackLogic {
	/**
	 * <AUTHOR>
	 * 
	 * @param tenant Mandatory, will be used to insert tr_client_callback_request
	 * @param lovCallbackType Mandatory, will be used to insert tr_client_callback_request
	 * @param vendorUser Mandatory, will be used to insert tr_client_callback_request
	 * @param document Optional, will be used to insert tr_client_callback_request if filled
	 * @param callbackMessage Callback message that will be sent to client (Example: "Success", "Verification failed", etc.)
	 * <br><br>
	 * Insert tr_client_callback_request with <code>Propagation.REQUIRES_NEW</code> transaction.<br>
	 * If database session error occurred, may be needed to get <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>endorRegisteredUser, or TrDocumentD with <code>Propagation.REQUIRES_NEW</code> transaction.
	 */
	void executeCallbackToClient(MsTenant tenant, MsLov lovCallbackType, MsVendorRegisteredUser vendorUser, TrDocumentD document, TrDocumentH documentH, String callbackMessage, Context context);
	void executeFailedVerificationCallbackToClient(MsTenant tenant, String email, String phone, String rejectMessage, Context context);

	void executeSendSignCompleteNotification(TrDocumentH documentH, Context context);
}
