package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDRestore;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.aliyun.fc.runtime.Context;

public class CheckRestoreExpiredDocumentJob extends BaseJobHandler {

    private static final String AUDIT = "FC_CHECK_RESTORE_EXP_DOC";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {

        context.getLogger().info("Fetching scheduler type and job type");
        MsLov schedulerType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.CODE_LOV_SCHED_TYPE_DAILY);
        context.getLogger().info(String.format("Scheduler type %s fetched.", schedulerType.getCode()));
        MsLov jobType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_JOB_TYPE, Constants.CODE_LOV_JOB_TYPE_CHECK_RESTORE_EXPIRED_DOCUMENT);
        context.getLogger().info(String.format("Job type %s fetched.", jobType.getCode()));

        Date startDate = new Date();
        Long dataProcessed = 0L;

        context.getLogger().info("Job Check Document Restore Expired Started");

        List<TrDocumentDRestore> expiredRestores = daoFactory.getDocumentDao().getExpiredDocumentRestores(new Date());
        context.getLogger().info(String.format("Found %d restored document that is expired", expiredRestores.size()));

        if (expiredRestores.isEmpty()) {
            context.getLogger().info("There is no expired restored document");
        } else {
            for (TrDocumentDRestore restore : expiredRestores) {
                TrDocumentD docD = restore.getTrDocumentD();
                
                context.getLogger().info(String.format("Updating TrDocumentD for restored expired document with document ID: %s", docD.getDocumentId()));
                docD.setArchiveDocumentStatus("1");
                docD.setUsrUpd(AUDIT);
                docD.setDtmUpd(new Date());
                daoFactory.getDocumentDao().updateDocumentDNewTran(docD);

                context.getLogger().info(String.format("Updating TrDocumentDRestore for restored expired document with document ID: %s", docD.getDocumentId()));
                restore.setIsActive("0");
                restore.setUsrUpd(AUDIT);
                restore.setDtmUpd(new Date());
                daoFactory.getDocumentDao().updateDocumentDRestoreNewTran(restore);

                dataProcessed++;
                context.getLogger().info(String.format("Document ID %s archive status updated to 1", docD.getDocumentId()));
            }
        }

        Date endDate = new Date();

        context.getLogger().info("Scheduler finished, inserting tr_scheduler_job");
        TrSchedulerJob schedulerJob = new TrSchedulerJob();
        schedulerJob.setSchedulerStart(startDate);
        schedulerJob.setSchedulerEnd(endDate);
        schedulerJob.setMsLovBySchedulerType(schedulerType);
        schedulerJob.setMsLovByJobType(jobType);
        schedulerJob.setDataProcessed(dataProcessed);
        schedulerJob.setUsrCrt(AUDIT);
        schedulerJob.setDtmCrt(new Date());
        schedulerJob.setNotes("Done");
        daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
    }
}
