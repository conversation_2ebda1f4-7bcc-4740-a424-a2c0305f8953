package com.adins.esignhubjob.businesslogic;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.factory.api.DaoFactory;
import com.aliyun.fc.runtime.Context;
import com.google.gson.Gson;

@Component
@Transactional
public abstract class BaseLogic {
    @Autowired protected DaoFactory daoFactory;
    @Autowired protected Gson gson;

    /**
	 * <AUTHOR>
	 * 
	 * @param processName Process name for log
	 * @param startTime Process start time
	 * @param finishTime Process finish time
	 */
	protected void logProcessDuration(String processName, Date startTime, Date finishTime, Context context) {
		long durationMs = finishTime.getTime() - startTime.getTime();
		context.getLogger().info(processName + " duration: " + durationMs + " ms");
	}
}
