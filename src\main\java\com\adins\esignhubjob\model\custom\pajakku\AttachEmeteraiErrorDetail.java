package com.adins.esignhubjob.model.custom.pajakku;

public class AttachEmeteraiErrorDetail extends EmeteraiPajakkuResponseHeader{
    private String errorLocation;
	private String errorLocationDetail;
	private String errorMsg;
	private Exception exception;
	private String jsonRequest;
	private String jsonResponse;

    public String getErrorLocation() {
        return this.errorLocation;
    }

    public void setErrorLocation(String errorLocation) {
        this.errorLocation = errorLocation;
    }

    public String getErrorLocationDetail() {
        return this.errorLocationDetail;
    }

    public void setErrorLocationDetail(String errorLocationDetail) {
        this.errorLocationDetail = errorLocationDetail;
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Exception getException() {
        return this.exception;
    }

    public void setException(Exception exception) {
        this.exception = exception;
    }

    public String getJsonRequest() {
        return this.jsonRequest;
    }

    public void setJsonRequest(String jsonRequest) {
        this.jsonRequest = jsonRequest;
    }

    public String getJsonResponse() {
        return this.jsonResponse;
    }

    public void setJsonResponse(String jsonResponse) {
        this.jsonResponse = jsonResponse;
    }

}
