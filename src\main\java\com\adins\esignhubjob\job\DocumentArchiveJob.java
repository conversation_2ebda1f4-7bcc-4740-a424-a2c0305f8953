package com.adins.esignhubjob.job;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailAttachmentBean;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;
public class DocumentArchiveJob extends BaseJobHandler {
    private static final String AUDIT = "FC_DOC_ARCHIVE";
    private static final String ARCHIVING_DOC_DONE_MSG = "Archiving doc with document ID %1$s done";
    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {
        context.getLogger().info("fetching scheduler type and job type");
        MsLov schedulerType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.CODE_LOV_SCHED_TYPE_DAILY);
        context.getLogger().info(String.format("Scheduler type %s fetched.", schedulerType.getCode()));
        MsLov jobType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_JOB_TYPE, Constants.CODE_LOV_JOB_TYPE_DOCUMENT_ARCHIVE);
        context.getLogger().info(String.format("Job type %s fetched.", jobType.getCode()));
        Date startDate = new Date();
        Long dataProcessed = 0L;
        try {
            context.getLogger().info("Job Document Archive Started");
            context.getLogger().info("fetching list of tenants");
            List<MsTenant> tenants = daoFactory.getTenantDao().getActiveTenants();
            for (MsTenant tenant : tenants) {
                context.getLogger().info(String.format("Archiving docs for tenant %1$s", tenant.getTenantCode()));
                dataProcessed += archiveDocumentSignAndStampComplete(tenant, context);
                dataProcessed += archiveDocumentSignCompleteAndNoStamp(tenant, context);
            }
            Date endDate = new Date();
           
            context.getLogger().info("scheduler finished, inserting tr_scheduler_job");
            TrSchedulerJob schedulerJob = new TrSchedulerJob();
            schedulerJob.setSchedulerStart(startDate);
            schedulerJob.setSchedulerEnd(endDate);
            schedulerJob.setMsLovBySchedulerType(schedulerType);
            schedulerJob.setMsLovByJobType(jobType);
            schedulerJob.setDataProcessed(dataProcessed);
            schedulerJob.setUsrCrt(AUDIT);
            schedulerJob.setDtmCrt(new Date());
            schedulerJob.setNotes("Done");
            daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
            context.getLogger().info("Job Document Archive Finished");
        } catch (Exception e) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode("PIC_OPERATION_ADINS");
            String[] recipient = gs.getGsValue().split(";");
            EmailAttachmentBean[] attachments = null;
            byte[] stackTraceFile = buildStackTraceTextFile(e);
            String filename = buildStackTraceFileName(jobType.getDescription(), schedulerType.getDescription());
            EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
            attachments = new EmailAttachmentBean[] {attachment};
            EmailInformationBean emailBean = new EmailInformationBean();
            emailBean.setTo(recipient);
            emailBean.setBodyMessage("There was an error in Job " + schedulerType.getDescription() + " " + jobType.getDescription() + ". Stack Trace is attached on this email.");
            emailBean.setSubject("Job " + schedulerType.getDescription() + " " + jobType.getDescription());
            logicFactory.getEmailSenderLogic().sendEmail(emailBean, attachments, context);
            context.getLogger().info("Exception Thrown : " + e.getClass().getName() + " " + e.getMessage());
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
            TrSchedulerJob schedulerJob = new TrSchedulerJob();
            schedulerJob.setSchedulerStart(startDate);
            schedulerJob.setSchedulerEnd(new Date());
            schedulerJob.setMsLovBySchedulerType(schedulerType);
            schedulerJob.setMsLovByJobType(jobType);
            schedulerJob.setDataProcessed(dataProcessed);
            schedulerJob.setUsrCrt(AUDIT);
            schedulerJob.setDtmCrt(new Date());
            schedulerJob.setNotes("Not Done: Exception found");
            daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
        }
    }
    private Long archiveDocumentSignAndStampComplete(MsTenant tenant, Context context) {
        int restoredDuration = 1;
        MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_RETENTION_DURATION);
        if (null == ts || StringUtils.isBlank(ts.getSettingValue())) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_RETENTION_DURATION);
            restoredDuration = Integer.parseInt(gs.getGsValue());
        } else {
            restoredDuration = Integer.parseInt(ts.getSettingValue());
        }
        
        Date lastArchiveDocumentDate = DateUtils.addDays(new Date(), -restoredDuration);
        
        BigInteger limitData = new BigInteger(System.getenv(Constants.ENV_OSS_ARCHIVE_LIMIT_DATA));
        List<TrDocumentD> docsToArchive = daoFactory.getDocumentDao().getListCompletedStampDocumentDInTenant(tenant.getIdMsTenant(), lastArchiveDocumentDate, limitData);
        
        Long dataProcessed = Long.valueOf(docsToArchive.size());
        if(0 == dataProcessed){
            context.getLogger().info(String.format("No Docs with complete stamp for tenant %1$s with latest stamping date before %2$s", tenant.getTenantCode(), lastArchiveDocumentDate));
            return dataProcessed;
        }
        context.getLogger().info(String.format("Archiving %1s Docs with complete sign and stamp with latest stamping date before %2$s for tenant %3$s started.", docsToArchive.size(), lastArchiveDocumentDate, tenant.getTenantCode()));
        for (TrDocumentD docD : docsToArchive) {
            try {
                context.getLogger().info(String.format("Archiving Doc with Document Id %1$s, request date: %2$s, completed date: %3$s", docD.getDocumentId(), docD.getRequestDate(), docD.getCompletedDate()));
                TrDocumentH docH = docD.getTrDocumentH();
                
                logicFactory.getAliyunOssCloudStorageLogic().storeArchiveStampedDocument(docD, context);
                logicFactory.getAliyunOssCloudStorageLogic().storeArchiveSignedDocument(docD, context);
                logicFactory.getAliyunOssCloudStorageLogic().storeArchiveManualStamp(docD, context);
                logicFactory.getAliyunOssCloudStorageLogic().storeArchiveStampingDocument(tenant.getTenantCode(), docH.getRefNumber(), docD.getDocumentId(), context);
                logicFactory.getAliyunOssCloudStorageLogic().storeArchiveBaseSignDocument(docD, context);
                
                docD.setDocumentArchivedDate(new Date());
                docD.setArchiveDocumentStatus("1");
                docD.setUsrUpd(AUDIT);
                docD.setDtmUpd(new Date());
                daoFactory.getDocumentDao().updateDocumentDNewTran(docD);
                context.getLogger().info(String.format(ARCHIVING_DOC_DONE_MSG, docD.getDocumentId()));
            } catch (Exception e) {
                context.getLogger().error(String.format("Error archiving document %s: %s", docD.getDocumentId(), e.getMessage()));
                context.getLogger().error(ExceptionUtils.getStackTrace(e));
            }
        }
        context.getLogger().info(String.format("Archiving Docs with complete stamp for tenant %1$s ended.", tenant.getTenantCode()));
        return dataProcessed;
    }
    private Long archiveDocumentSignCompleteAndNoStamp(MsTenant tenant, Context context) {
        int restoredDuration = 1;
        MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_RETENTION_DURATION);
        if (null == ts || StringUtils.isBlank(ts.getSettingValue())) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_RETENTION_DURATION);
            restoredDuration = Integer.parseInt(gs.getGsValue());
        } else {
            restoredDuration = Integer.parseInt(ts.getSettingValue());
        }
        Date lastArchiveDocumentDate = DateUtils.addDays(new Date(), -restoredDuration);
        
        BigInteger limitData = new BigInteger(System.getenv(Constants.ENV_OSS_ARCHIVE_LIMIT_DATA));
        List<TrDocumentD> docsToArchive = daoFactory.getDocumentDao().getListCompletedSignNoStampDocumentDInTenant(tenant.getIdMsTenant(), lastArchiveDocumentDate, limitData);
        
        Long dataProcessed = Long.valueOf(docsToArchive.size());
        if(0 == dataProcessed){
            context.getLogger().info(String.format("No Docs with complete sign and no stamp for tenant %1$s with completed date before %2$s", tenant.getTenantCode(), lastArchiveDocumentDate));
            return dataProcessed;
        }
        context.getLogger().info(String.format("Archiving %1s Docs with complete sign and no stamp with completed date before %2$s for tenant %3$s started.", docsToArchive.size(), lastArchiveDocumentDate,tenant.getTenantCode()));
        for (TrDocumentD docD : docsToArchive) {
            try {
                context.getLogger().info(String.format("Archiving Document with Document Id %1$s, completed date: %2$s", docD.getDocumentId(), docD.getCompletedDate()));
                
                logicFactory.getAliyunOssCloudStorageLogic().storeArchiveSignedDocument(docD, context);
                logicFactory.getAliyunOssCloudStorageLogic().storeArchiveBaseSignDocument(docD, context);
                docD.setDocumentArchivedDate(new Date());
                docD.setArchiveDocumentStatus("1"); 
                docD.setUsrUpd(AUDIT);
                docD.setDtmUpd(new Date());
                daoFactory.getDocumentDao().updateDocumentDNewTran(docD);
                context.getLogger().info(String.format(ARCHIVING_DOC_DONE_MSG, docD.getDocumentId()));
            } catch (Exception e) {
                context.getLogger().error(String.format("Error archiving document %s: %s", docD.getDocumentId(), e.getMessage()));
                context.getLogger().error(ExceptionUtils.getStackTrace(e));
            }
        }
        context.getLogger().info(String.format("Archiving Docs with complete sign and no stamp for tenant %1$s ended.", tenant.getTenantCode()));
        return dataProcessed;
    }
    
    private byte[] buildStackTraceTextFile(Exception e) {
        String stackTrace = ExceptionUtils.getStackTrace(e);
        String base64 = Base64.getEncoder().encodeToString(stackTrace.getBytes());
        return Base64.getDecoder().decode(base64);
    }
    private String buildStackTraceFileName(String jobType, String schedulerType) {
        String currentTime = Tools.formatDateToStringIn(new Date(), Constants.DATE_TIME_FORMAT_SEQ);
        StringBuilder filename = new StringBuilder()
            .append(StringUtils.upperCase(jobType)).append("_")
            .append(StringUtils.upperCase(schedulerType)).append("_")
            .append(currentTime)
            .append(".txt");
        return filename.toString();
    }
}