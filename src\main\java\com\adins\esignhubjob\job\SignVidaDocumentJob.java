package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.vida.VidaSigningResponseContainer;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;
import com.adins.esignhubjob.model.webservice.vida.VidaDocumentStatusResponse;
import com.adins.esignhubjob.model.webservice.vida.VidaSignResponse;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class SignVidaDocumentJob extends BaseJobHandler {

    private static final String AUDIT = "FC SIGN VIDA";
    private static final String CONST_SUCCESS = "Success";

    private static final int MAX_CHECK_ATTEMPTS = 30;
    private static final long CHECK_DELAY       = 100L;

    private long getCheckDelay() {
        String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_VIDA_JOB_SIGN_WAIT_TIME);
        if (StringUtils.isBlank(gsValue)) {
            return CHECK_DELAY;
        }

        try {
            return Long.valueOf(gsValue);
        } catch (Exception e) {
            return CHECK_DELAY;
        }
    }

    private int getCheckingMaxAttempts() {
        String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_VIDA_JOB_SIGN_ITERATION);
        if (StringUtils.isBlank(gsValue)) {
            return MAX_CHECK_ATTEMPTS;
        }

        try {
            return Integer.valueOf(gsValue);
        } catch (Exception e) {
            return MAX_CHECK_ATTEMPTS;
        }
    }

    private void processSuccessfulSignature(TrDocumentSigningRequest documentSigningRequest, Context context) {
        TrDocumentD document = documentSigningRequest.getTrDocumentD();
        TrDocumentH documentH = document.getTrDocumentH();

        documentSigningRequest.setRequestEnd(new Date());
        documentSigningRequest.setRequestStatus((short) 3);
        documentSigningRequest.setDtmUpd(new Date());
        documentSigningRequest.setUsrUpd(AUDIT);
        daoFactory.getDocumentSigningRequestDao().updateDocumentSigningRequestNewTran(documentSigningRequest);

        document.setSigningProcess("0");
        document.setDtmUpd(new Date());
        document.setUsrUpd(AUDIT);
        daoFactory.getDocumentDao().updateDocumentDNewTran(document);

        daoFactory.getDocumentDao().updateNativeStringDocumentHSigningProcessNewTrx(documentH, "0", AUDIT);

        // Trigger sign callback
        MsTenant tenant = document.getMsTenant();
        MsLov lovCallbackType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_CALLBACK_TYPE, Constants.LOV_CODE_CALLBACK_TYPE_SIGN_COMPLETE);
        AmMsuser user = documentSigningRequest.getAmMsuser();
        MsVendor vendor = document.getMsVendor();
        MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserNewTran(user, vendor);
        logicFactory.getCallbackLogic().executeCallbackToClient(tenant, lovCallbackType, vendorUser, document, null, CONST_SUCCESS, context);

        if (!document.getTotalSign().equals(document.getTotalSigned())) {
            return;
        }

        // Handling dokumen sudah selesai ttd semua
        storeFullySignedDocumentToOss(document, context);
        
        MsLov lovSignStatus = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGN_STATUS, Constants.LOV_CODE_SIGN_STATUS_COMPLETE);
        document.setCompletedDate(new Date());
        document.setMsLovByLovSignStatus(lovSignStatus);
        document.setDtmUpd(new Date());
        document.setUsrUpd(AUDIT);
        daoFactory.getDocumentDao().updateDocumentDNewTran(document);

        // Callback document sign complete
        MsLov documentCompleteCallbackLov = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_CALLBACK_TYPE, Constants.LOV_CODE_CALLBACK_TYPE_DOCUMENT_SIGN_COMPLETE);
        logicFactory.getCallbackLogic().executeCallbackToClient(tenant, documentCompleteCallbackLov, null, document, documentH, CONST_SUCCESS, context);

        String functionResponse = daoFactory.getCommonDao().incrementDocumentHTotalSignedNewTran(documentH.getIdDocumentH());
        if (functionResponse.endsWith("(callback_process updated)")) {
            // Trigger send sign complete notification
            logicFactory.getCallbackLogic().executeSendSignCompleteNotification(documentH, context);
            
            MsLov allDocumentCompleteCallbackLov = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_CALLBACK_TYPE, Constants.LOV_CODE_CALLBACK_TYPE_ALL_DOCUMENT_SIGN_COMPLETE);
            logicFactory.getCallbackLogic().executeCallbackToClient(tenant, allDocumentCompleteCallbackLov, null, document, documentH, CONST_SUCCESS, context);
        }
        context.getLogger().info(String.format("Kontrak %1$s: %2$s", documentH.getRefNumber(), functionResponse));
    }

    private void processFailedSignature(TrDocumentSigningRequest documentSigningRequest, TrDocumentH documentH, TrDocumentD document) {
       
        documentSigningRequest.setRequestEnd(new Date());
        documentSigningRequest.setRequestStatus((short) 2);
        documentSigningRequest.setDtmUpd(new Date());
        documentSigningRequest.setUsrUpd(AUDIT);
        daoFactory.getDocumentSigningRequestDao().updateDocumentSigningRequestNewTran(documentSigningRequest);

        daoFactory.getDocumentDao().updateNativeStringDocumentHSigningProcessNewTrx(documentH, "0", AUDIT);

        document.setSigningProcess("0");
        document.setDtmUpd(new Date());
        document.setUsrUpd(AUDIT);
        daoFactory.getDocumentDao().updateDocumentDNewTran(document);
    }

    /**
     * @return Document download URL if successful, otherwise return {@code null}
     */
    private String getSignedDocumentUrl(TrDocumentDSign documentDSign, MsVendorRegisteredUser vendorRegisteredUser, TrBalanceMutation balanceMutation, String trackStatusId, Context context) {
        TrDocumentD document = documentDSign.getTrDocumentD();
        TrDocumentH documentH = document.getTrDocumentH();
        VidaSigningResponseContainer container = null;
        try {
            boolean success = false;
            int attempts = getCheckingMaxAttempts();
            long delayMs = getCheckDelay();

            while (!success) {
                if (attempts <= 0) {
                    context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, failed to get signed document URL (max attempt reached)", documentH.getRefNumber(), document.getDocumentId()));
                    if (null != container) {
                        insertAuditTrail(vendorRegisteredUser, document, false, Constants.SIGNING_PROCESS_TYPE_SIGNING_SUCCESS, container, context);
                    }
                    return null;
                }

                Thread.sleep(delayMs);
                context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, %3$s attempts left to get signed document URL", documentH.getRefNumber(), document.getDocumentId(), attempts));
                container = logicFactory.getVidaLogic().getDocumentStatus(documentDSign, trackStatusId, context);
                VidaDocumentStatusResponse documentStatus = gson.fromJson(container.getResponse(), VidaDocumentStatusResponse.class);
                if (documentStatus.getData().getCode() == 2 && CollectionUtils.isNotEmpty(documentStatus.getData().getSignedDocs())) {
                    balanceMutation.setRefNo(documentH.getRefNumber());
                    balanceMutation.setQty(-1);
                    balanceMutation.setTrDocumentD(document);
                    balanceMutation.setTrDocumentH(documentH);
                    daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(balanceMutation);

                    insertAuditTrail(vendorRegisteredUser, document, true, Constants.SIGNING_PROCESS_TYPE_SIGNING_SUCCESS, container, context);
                    return documentStatus.getData().getSignedDocs().get(0).getDocumentUrl();
                }
                if (documentStatus.getData().getCode() == 3) {
                    balanceMutation.setRefNo(documentH.getRefNumber());
                    balanceMutation.setQty(0);
                    balanceMutation.setTrDocumentD(document);
                    balanceMutation.setTrDocumentH(documentH);
                    daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(balanceMutation);
                    
                    context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, failed to get signed document URL (fail response)", documentH.getRefNumber(), document.getDocumentId()));
                    insertAuditTrail(vendorRegisteredUser, document, false, Constants.SIGNING_PROCESS_TYPE_SIGNING_SUCCESS, container, context);
                    return null;
                }
                attempts -= 1;
            }
            return null;
        } catch (InterruptedException e) {

            context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, failed to get signed document URL with exception: InterruptedException (re-interrupt thread)", documentH.getRefNumber(), document.getDocumentId()));
            Thread.currentThread().interrupt();
            insertAuditTrail(vendorRegisteredUser, document, false, Constants.SIGNING_PROCESS_TYPE_SIGNING_SUCCESS, container, context);
            return null;

        } catch (Exception e) {

            context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, failed to get signed document URL with exception: %3$s", documentH.getRefNumber(), document.getDocumentId(), e.getLocalizedMessage()));
            String stackTrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stackTrace);
            insertAuditTrail(vendorRegisteredUser, document, false, Constants.SIGNING_PROCESS_TYPE_SIGNING_SUCCESS, container, context);
            return null;

        }
    }

    /**
     * @return {@code true} if successfully store signed doc to OSS, otherwise return {@code false}
     */
    private boolean storeSignedDocument(TrDocumentDSign documentDSign, MsVendorRegisteredUser vendorRegisteredUser, TrBalanceMutation balanceMutation, String trackStatusId, Context context) {
        TrDocumentD document = documentDSign.getTrDocumentD();
        TrDocumentH documentH = document.getTrDocumentH();

        String documentUrl = getSignedDocumentUrl(documentDSign, vendorRegisteredUser, balanceMutation, trackStatusId, context);
        if (StringUtils.isBlank(documentUrl)) {
            context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, failed to store signed document (cannot get document URL)", documentH.getRefNumber(), document.getDocumentId()));
            return false;
        }

        try {
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder().url(documentUrl).build();
            Response response = client.newCall(request).execute();
            if (response.code() != 200) {
                context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, failed to store signed document (download failed with code %3$s %4$s)", documentH.getRefNumber(), document.getDocumentId(), response.code(), response.message()));
                return false;
            }

            byte[] documentByteArray = response.body().bytes();
            logicFactory.getAliyunOssCloudStorageLogic().storeBaseSignDocument(documentDSign.getTrDocumentD(), documentByteArray, context);
            return true;
        } catch (Exception e) {
            context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, failed to store signed document with exception: %3$s", documentH.getRefNumber(), document.getDocumentId(), e.getLocalizedMessage()));
            String stackTrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stackTrace);
            return false;
        }
        
    }

    private void storeFullySignedDocumentToOss(TrDocumentD document, Context context) {
        byte[] documentByteArray = logicFactory.getAliyunOssCloudStorageLogic().getBaseSignDocument(document, context);
        logicFactory.getAliyunOssCloudStorageLogic().storeSignedDocument(document, documentByteArray, context);
    }

    /**
     * @return {@code true} if successfully signed a document, otherwise return {@code false}
     */
    private boolean singleSign(TrDocumentSigningRequest documentSigningRequest, TrDocumentDSign documentDSign, Context context) {

        MsTenant tenant = documentDSign.getTrDocumentD().getMsTenant();
        MsVendor vendor = documentDSign.getTrDocumentD().getMsVendor();
        TrDocumentD document = documentSigningRequest.getTrDocumentD();
        TrDocumentH documentH = document.getTrDocumentH();
        AmMsuser user = documentSigningRequest.getAmMsuser();
        MsVendorRegisteredUser vendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUser(user, vendor);

        long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
        String reservedTrxNo = String.valueOf(trxNo);
        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SGN);
        MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USGN);
        
        document.setSigningProcess("1");
        document.setDtmUpd(new Date());
        document.setUsrUpd(AUDIT);
        daoFactory.getDocumentDao().updateDocumentDNewTran(document);

        TrBalanceMutation balanceMutation = new TrBalanceMutation();
        balanceMutation.setTrxNo(reservedTrxNo);
        balanceMutation.setTrxDate(new Date());
        balanceMutation.setQty(0);
        balanceMutation.setMsLovByLovBalanceType(balanceType);
        balanceMutation.setMsLovByLovTrxType(trxType);
        balanceMutation.setUsrCrt(vendorRegisteredUser.getSignerRegisteredEmail());
        balanceMutation.setDtmCrt(new Date());
        balanceMutation.setMsTenant(tenant);
        balanceMutation.setMsVendor(vendor);
        balanceMutation.setAmMsuser(user);
        balanceMutation.setNotes("Sign " + vendorRegisteredUser.getSignerRegisteredEmail());
        balanceMutation.setRefNo(documentH.getRefNumber());
        balanceMutation.setMsOffice(documentH.getMsOffice());
        balanceMutation.setMsBusinessLine(documentH.getMsBusinessLine());
        daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(balanceMutation);

        try {
            VidaSigningResponseContainer container = logicFactory.getVidaLogic().signVida(documentSigningRequest, documentDSign, vendorRegisteredUser, reservedTrxNo, context);
            VidaSignResponse response = gson.fromJson(container.getResponse(), VidaSignResponse.class); 
            if (CollectionUtils.isNotEmpty(response.getErrors())) {
                context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, failed to sign document (fail response)", documentH.getRefNumber(), document.getDocumentId()));
                insertAuditTrail(vendorRegisteredUser, document, false, Constants.SIGNING_PROCESS_TYPE_SIGNING_REQUESTED, container, context);
                return false;
            }

            insertAuditTrail(vendorRegisteredUser, document, true, Constants.SIGNING_PROCESS_TYPE_SIGNING_REQUESTED, container, context);

            balanceMutation.setVendorTrxNo(response.getData().getId());
            boolean isSuccess = storeSignedDocument(documentDSign, vendorRegisteredUser, balanceMutation, response.getData().getId(), context);
            if (!isSuccess) {
                context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, failed to sign document (cannot store signed document)", documentH.getRefNumber(), document.getDocumentId()));
                return false;
            }

            documentDSign.setSignDate(new Date());
            documentDSign.setVendorRegistrationId(vendorRegisteredUser.getVendorRegistrationId());
            documentDSign.setDtmUpd(new Date());
            documentDSign.setUsrUpd(AUDIT);
            daoFactory.getDocumentDao().updateDocumentDSignNewTran(documentDSign);

            short totalSigned = daoFactory.getDocumentDao().countSignedDocumentNewTran(document);
            document.setTotalSigned(totalSigned);
            document.setDtmUpd(new Date());
            document.setUsrUpd(AUDIT);
            daoFactory.getDocumentDao().updateDocumentDNewTran(document);

            context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, sign process: %3$s/%4$s", documentH.getRefNumber(), document.getDocumentId(), document.getTotalSigned(), document.getTotalSign()));
            return true;
        } catch (Exception e) {
            context.getLogger().error(String.format("Kontrak %1$s, Dokumen %2$s, failed to sign VIDA document with exception %3$s", documentH.getRefNumber(), document.getDocumentId(), e.getLocalizedMessage()));
            String stackTrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stackTrace);
            return false;
        }

    }

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String input = IOUtils.toString(inputStream);
        context.getLogger().info("Sign VIDA for request ID: " + input);
        
        Long idDocumentSigningRequest = Long.valueOf(input);
        TrDocumentSigningRequest documentSigningRequest = daoFactory.getDocumentSigningRequestDao().getDocumentSigningRequestNewTran(idDocumentSigningRequest);
        if (null == documentSigningRequest) {
            context.getLogger().warn("Document signing request with ID " + idDocumentSigningRequest + " is not found");
            return;
        }

        TrDocumentD document = documentSigningRequest.getTrDocumentD();
        TrDocumentH documentH = document.getTrDocumentH();
        AmMsuser user = documentSigningRequest.getAmMsuser();
        MsVendor vendor = document.getMsVendor();

        if (!Constants.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
            context.getLogger().warn("Document " + document.getDocumentId() + " is not a VIDA document");

            documentSigningRequest.setRequestStatus((short) 2);
            documentSigningRequest.setUsrUpd(AUDIT);
            documentSigningRequest.setDtmUpd(new Date());
            daoFactory.getDocumentSigningRequestDao().updateDocumentSigningRequestNewTran(documentSigningRequest);
            return;
        }

        List<TrDocumentDSign> documentDSigns = daoFactory.getDocumentDao().getUnsignedListDocumentDSignNewTran(document, user);
        context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, processing %3$s sign(s) for %4$s", documentH.getRefNumber(), document.getDocumentId(), documentDSigns.size(), user.getLoginId()));
        for (TrDocumentDSign documentDSign : documentDSigns) {
            boolean successSign = singleSign(documentSigningRequest, documentDSign, context);
            if (!successSign) {
                processFailedSignature(documentSigningRequest, documentH, document);
                return;
            }
        }

        processSuccessfulSignature(documentSigningRequest, context);
    }

    private void insertAuditTrail(MsVendorRegisteredUser vendorUser, TrDocumentD document, boolean success, String signingProcessType, VidaSigningResponseContainer responseContainer, Context context) {
        
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, signingProcessType);

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(vendorUser.getPhoneBytea());
        trail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
        trail.setEmail(vendorUser.getSignerRegisteredEmail());
        trail.setAmMsUser(vendorUser.getAmMsuser());
        trail.setMsTenant(document.getMsTenant());
        trail.setMsVendor(vendorUser.getMsVendor());
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus(success ? "1" : "0");
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        // Get ID and set notes
        String requestBody = null != responseContainer ? responseContainer.getRequest() : null;
        String responseBody = null != responseContainer ? responseContainer.getResponse() : null;
        String filepath = logicFactory.getSigningProcessAuditTrailLogic().logProcessRequestResponse(trail, Constants.AUDIT_TRAIL_SUBFOLDER_SIGNING_PROCESS, requestBody, responseBody, false, context);
        trail.setNotes(filepath);
        trail.setUsrUpd(context.getRequestId());
        trail.setDtmUpd(new Date());
        daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrailNewTrx(trail);

        TrSigningProcessAuditTrailDetail trailDetail = new TrSigningProcessAuditTrailDetail();
        trailDetail.setSigningProcessAuditTrail(trail);
        trailDetail.setTrDocumentD(document);
        trailDetail.setUsrCrt(context.getRequestId());
        trailDetail.setDtmCrt(new Date());
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTrx(trailDetail);
    }
    
}
