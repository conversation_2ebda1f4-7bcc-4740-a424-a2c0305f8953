package com.adins.esignhubjob.model.webservice.client.womf;

import java.util.List;

import com.adins.esignhubjob.model.custom.client.womf.WomfUploadDocumentRequestBean;
import com.google.gson.annotations.SerializedName;

public class WomfUploadDocumentRequest {
    @SerializedName("RefNo") private String refNo;
    @SerializedName("DocumentObjs") private List<WomfUploadDocumentRequestBean> documentObjs;
    
    public String getRefNo() {
        return this.refNo;
    }

    public void setRefNo(String refNo) {
        this.refNo = refNo;
    }

    public List<WomfUploadDocumentRequestBean> getDocumentObjs() {
        return this.documentObjs;
    }

    public void setDocumentObjs(List<WomfUploadDocumentRequestBean> documentObjs) {
        this.documentObjs = documentObjs;
    }

}
