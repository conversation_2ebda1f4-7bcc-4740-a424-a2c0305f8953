package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "am_generalsetting")
public class AmGeneralsetting extends ActivatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final String GS_CODE_HBM = "gsCode";
	
	private long idGeneralSetting;
	private MsTenant msTenant;
	private String gsCode;
	private String gsPrompt;
	private String gsType;
	private String gsValue;
	private String editable;

    @Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_general_setting", unique = true, nullable = false)
	public long getIdGeneralSetting() {
		return this.idGeneralSetting;
	}

	public void setIdGeneralSetting(long idGeneralSetting) {
		this.idGeneralSetting = idGeneralSetting;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "gs_code", length = 80)
	public String getGsCode() {
		return this.gsCode;
	}

	public void setGsCode(String gsCode) {
		this.gsCode = gsCode;
	}

	@Column(name = "gs_prompt", length = 200)
	public String getGsPrompt() {
		return this.gsPrompt;
	}

	public void setGsPrompt(String gsPrompt) {
		this.gsPrompt = gsPrompt;
	}

	@Column(name = "gs_type", length = 80)
	public String getGsType() {
		return this.gsType;
	}

	public void setGsType(String gsType) {
		this.gsType = gsType;
	}

	@Column(name = "gs_value", length = 2048)
	public String getGsValue() {
		return this.gsValue;
	}

	public void setGsValue(String gsValue) {
		this.gsValue = gsValue;
	}

	@Column(name = "editable", nullable = false, length = 1)
	public String getEditable() {
		return this.editable;
	}

	public void setEditable(String editable) {
		this.editable = editable;
	}
}
