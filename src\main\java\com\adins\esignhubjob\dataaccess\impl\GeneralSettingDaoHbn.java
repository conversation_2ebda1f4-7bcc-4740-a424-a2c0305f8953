package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.GeneralSettingDao;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Component
@Transactional
public class GeneralSettingDaoHbn extends BaseDaoHbn implements GeneralSettingDao{

    @Override
	public AmGeneralsetting getGsObjByCode(String gsCode) {
		if(StringUtils.isBlank(gsCode)) {
			return null;
		}
		
		Object[][] queryParams = { { Restrictions.eq("gsCode", StringUtils.upperCase(gsCode))}, 
									{ Restrictions.eq(ActivatableEntity.IS_ACTIVE_HBM, "1")} };
		
		return this.managerDAO.selectOne(AmGeneralsetting.class, queryParams);
	}

    @Override
	public String getGsValueByCode(String gsCode) {
		if(StringUtils.isBlank(gsCode)) {
			return StringUtils.EMPTY;
		}
		
		Object[][] queryParams = { { Restrictions.eq("gsCode", StringUtils.upperCase(gsCode))}, 
									{ Restrictions.eq(ActivatableEntity.IS_ACTIVE_HBM, "1")} };
    	AmGeneralsetting result = this.managerDAO.selectOne(AmGeneralsetting.class, queryParams);
		if(null != result){
	    	return result.getGsValue();
		}
		else {
			return StringUtils.EMPTY;
		}
	}

	@Override
	public AmGeneralsetting getGsObjByCodeAndTenant(String gsCode, MsTenant tenant) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmGeneralsetting.GS_CODE_HBM, StringUtils.upperCase(gsCode));
		params.put("msTenant", tenant);
		
		return this.managerDAO.selectOne(
				"from AmGeneralsetting gs "
				+ "join fetch gs.msTenant mt "
				+ "where gs.gsCode = :gsCode and gs.msTenant = :msTenant ", params);
	}
    
}
