package com.adins.esignhubjob.httphandler;

import java.io.IOException;
import java.util.Date;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.BaseHttpHandler;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrMessageDeliveryReport;
import com.adins.esignhubjob.model.webservice.halosis.HalosisWhatsAppWebhookContent;
import com.adins.exceptions.Status;
import com.adins.exceptions.StatusCode;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

public class HalosisWebhookHandler extends BaseHttpHandler {

    @Override
    public void doHandleRequest(HttpServletRequest request, HttpServletResponse response, Context context)
            throws IOException, ServletException {
        String input = IOUtils.toString(request.getInputStream());
        String requestClientIP = (String) request.getAttribute("FC_REQUEST_CLIENT_IP");
        context.getLogger().info("Webhook from " + requestClientIP + " received: " + input);
        HalosisWhatsAppWebhookContent webhookContent = gson.fromJson(input, HalosisWhatsAppWebhookContent.class);

        String recipientDetail = webhookContent.getData().get(0).getToPhoneNumber();
        String status = webhookContent.getData().get(0).getStatus();
        String fromPhoneNumber = webhookContent.getData().get(0).getFromPhoneNumber();
        String wamId = webhookContent.getData().get(0).getId();
        if (null == wamId) {
            context.getLogger().info("Wam Id is empty");
            Status responseStatus = new Status(StatusCode.MANDATORY_PARAMETER, "Wam Id is empty");
            response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            response.getWriter().write(gson.toJson(responseStatus));
            return;
        }

        if (null == recipientDetail || null == fromPhoneNumber) {
            context.getLogger().info("Phone number is empty");
            Status responseStatus = new Status(StatusCode.MANDATORY_PARAMETER, "Phone number is empty");
            response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            response.getWriter().write(gson.toJson(responseStatus));
            return;
        }

        if(!recipientDetail.matches("\\d+") || !fromPhoneNumber.matches("\\d+")) {
            context.getLogger().info("Phone number is not in correct format");
            Status responseStatus = new Status(StatusCode.NO_PHONE_IS_NOT_VALID, "Phone number is not in correct format");
            response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            response.getWriter().write(gson.toJson(responseStatus));
            return;
        }

        if (null == status) {
            context.getLogger().info("Status is empty");
            Status responseStatus = new Status(StatusCode.MANDATORY_PARAMETER, "Status is empty");
            response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            response.getWriter().write(gson.toJson(responseStatus));
            return;
        }

        TrBalanceMutation mutation = daoFactory.getBalanceMutationDao().getBalanceMutationByVendorTrxNo(wamId);

        if (null == mutation) {
            context.getLogger().info("Balance Mutation not found");

            Status responseStatus = new Status(StatusCode.BALANCE_MUTATION_NOT_FOUND, "Balance Mutation not found");
            response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            response.getWriter().write(gson.toJson(responseStatus));
            return;
        }

        TrMessageDeliveryReport existingReport = daoFactory.getMessageDeliveryReportDao()
                .getMessageDeliveryReportByTrxNo(mutation.getTrxNo());
        if (null == existingReport){
            context.getLogger().info("No existing report found for transaction number: " + mutation.getTrxNo() + ", skipping insertion");
            return;
        } else {
            context.getLogger().info("Existing report found for transaction number: " + mutation.getTrxNo());
        }
        MsLov lovCredentialType = existingReport.getMsLovCredentialType();
        MsLov lovSendingPoint = existingReport.getMsLovSendingPoint();

        MsTenant tenant = mutation.getMsTenant();
        MsVendor vendor = mutation.getMsVendor();
        MsLov lovMessageMedia = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_MESSAGE_MEDIA,
                Constants.LOV_CODE_MESSAGE_MEDIA_WA);
        MsLov lovMessageGateway = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_WA_GATEWAY,
                Constants.CODE_LOV_WA_GATEWAY_HALOSIS);

        TrMessageDeliveryReport report = new TrMessageDeliveryReport();
        report.setReportTime(new Date());
        report.setMsTenant(tenant);
        report.setMsVendor(vendor);
        report.setRecipientDetail(recipientDetail);
        report.setDeliveryStatus(getFormattedDeliveryStatus(status));
        report.setTrxNo(mutation.getTrxNo());
        report.setVendorTrxNo(wamId);
        report.setMsLov(lovMessageMedia);
        report.setMsLovMessageGateway(lovMessageGateway);
        report.setMsLovCredentialType(lovCredentialType);
        report.setMsLovSendingPoint(lovSendingPoint);
        report.setUsrCrt("FC_WEBHOOK_HALOSIS");
        report.setDtmCrt(new Date());
        report.setRequestTime(mutation.getTrxDate());
        daoFactory.getMessageDeliveryReportDao().insertMessageDeliveryReport(report);

        Status responseStatus = new Status(0, "Success");
        response.setHeader(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        response.getWriter().write(gson.toJson(responseStatus));
    }

    private String getFormattedDeliveryStatus(String originalStatus) {
        switch (originalStatus) {
            case "sent":
                return "1";
            case "delivered":
                return "3";
            case "read":
                return "4";
            default:
                return "2";
        }
    }
}
