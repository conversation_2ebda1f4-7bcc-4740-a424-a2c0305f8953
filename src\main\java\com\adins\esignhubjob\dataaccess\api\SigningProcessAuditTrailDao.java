package com.adins.esignhubjob.dataaccess.api;

import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;

public interface SigningProcessAuditTrailDao {
    // tr_signing_process_audit_trail
    void insertSigningProcessAuditTrailNewTrx(TrSigningProcessAuditTrail auditTrail);
    void updateSigningProcessAuditTrailNewTrx(TrSigningProcessAuditTrail auditTrail);
    TrSigningProcessAuditTrail getLatestAuditTrailNewTrx(String hashedPhone, String email, MsTenant tenant, MsVendor vendor, <PERSON><PERSON>ov lovProcessType);

    // tr_signing_process_audit_trail_detail
    void insertSigningProcessAuditTrailDetailNewTrx(TrSigningProcessAuditTrailDetail auditTrailDetail);
}
