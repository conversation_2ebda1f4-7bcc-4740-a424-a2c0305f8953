package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.aliyun.fc.runtime.Context;
import org.apache.commons.lang3.StringUtils;

public class SendEmailRemainderFailRetryStamping extends BaseJobHandler {

    private static final String[] ERROR_EMAIL_RECEIVERS = { "<EMAIL>" };

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        
        Date startTime = new Date();

        String maxRetry = daoFactory.getGeneralSettingDao()
                .getGsValueByCode(AmGlobalKey.GS_DOCUMENT_MAX_RETRY_STAMPING);

        String receiver = daoFactory.getGeneralSettingDao()
                .getGsValueByCode(AmGlobalKey.GS_SEND_EMAIL_REMAINDER_FAIL_RETRY_STAMPING_RECEPIENT);

        String[] receivers = receiver.split(";");
        if (StringUtils.isBlank(receivers[0])) {
            receivers = ERROR_EMAIL_RECEIVERS;
        }

        Short[] prosesMaterai = {51, 61, 521, 71};

        List<Map<String, Object>> listDocumentFailStamping = daoFactory.getDocumentDao().getDocumentInformationWithMaxRetry(Short.valueOf(maxRetry), prosesMaterai);

        context.getLogger().info(String.valueOf(listDocumentFailStamping.size()));

        if (!listDocumentFailStamping.isEmpty()) {
            context.getLogger().info("document stamp fail reach max count");
            Iterator<Map<String, Object>> itr = listDocumentFailStamping.iterator();
            Map<String, Object> templateParameters = new HashMap<>();

            StringBuilder itemBrBuilder = new StringBuilder();
            int i = 1;
            while (itr.hasNext()) {
                Map<String, Object> map = itr.next();

                itemBrBuilder.append(Constants.TEMPLATE_HTML_TAG_START_TR).append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD).append(String.valueOf(i))
                .append(Constants.TEMPLATE_HTML_TAG_END_TD).append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD).append((String) map.get("d0"))
                .append(Constants.TEMPLATE_HTML_TAG_END_TD).append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD).append(map.get("d1") != null ? map.get("d1").toString() : "")
                .append(Constants.TEMPLATE_HTML_TAG_END_TD).append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD).append((String) map.get("d2"))
                .append(Constants.TEMPLATE_HTML_TAG_END_TD).append(Constants.TEMPLATE_EMAIL_SEND_EMAIL_TAG_START_TD).append((String) map.get("d3"))
                .append(Constants.TEMPLATE_HTML_TAG_END_TD).append(Constants.TEMPLATE_HTML_TAG_END_TR);
                i++;
            }

            templateParameters.put("items", itemBrBuilder.toString());

            MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(
                        Constants.TEMPLATE_SEND_EMAIL_REMINDER_FAIL_STAMPING,
                        templateParameters);
            
            context.getLogger().info(template.getBody());
            context.getLogger().info("Template retrieved successfully");

            EmailInformationBean emailInfo = new EmailInformationBean();
            emailInfo.setSubject(template.getSubject());
            emailInfo.setBodyMessage(template.getBody());
            emailInfo.setTo(receivers);
            emailInfo.setImportance(true);
            logicFactory.getEmailSenderLogic().sendEmail(emailInfo, null, context);
        } else {
            context.getLogger().info("No document stamp fail reach max count");
        }

        String notes = "Done";
        insertTrSchedulerJob(startTime, Constants.LOV_CODE_SCHEDULER_DAILY, Constants.CODE_LOV_JOB_TYPE_PROCESS_SEND_EMAIL_REMINDER_RETRY_STAMPING, listDocumentFailStamping.size(), notes);

        
    }

    private void insertTrSchedulerJob(Date startTime, String lovSchedulerTypeCode, String lovJobTypeCode,
			long dataProcessed, String notes) {
		MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE,
				lovSchedulerTypeCode);
		MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, lovJobTypeCode);

		TrSchedulerJob schedulerJob = new TrSchedulerJob();
		schedulerJob.setSchedulerStart(startTime);
		schedulerJob.setSchedulerEnd(new Date());
		schedulerJob.setMsLovBySchedulerType(lovSchedulerType);
		schedulerJob.setMsLovByJobType(lovJobType);
		schedulerJob.setDataProcessed(dataProcessed);
		schedulerJob.setNotes(notes);
		schedulerJob.setUsrCrt("SH");
		schedulerJob.setDtmCrt(new Date());
		daoFactory.getSchedulerJobDao().insertSchedulerJob(schedulerJob);
	}

}
