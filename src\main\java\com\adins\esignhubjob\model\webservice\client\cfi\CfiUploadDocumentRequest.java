package com.adins.esignhubjob.model.webservice.client.cfi;

import com.google.gson.annotations.SerializedName;

public class CfiUploadDocumentRequest {
    @SerializedName("DokumenPeruri") private String dokumenPeruri;
	@SerializedName("DokumenDate") private String dokumenDate;
	@SerializedName("Filename") private String filename;
	@SerializedName("Content") private String content;
	@SerializedName("Notes") private String notes;
	@SerializedName("EsignId") private String documentId;

    public CfiUploadDocumentRequest(CfiUploadDocumentRequest request) {
		this.dokumenPeruri = request.dokumenPeruri;
		this.dokumenDate = request.dokumenDate;
		this.filename = request.filename;
		this.content = request.content;
		this.notes = request.notes;
		this.documentId = request.documentId;
	}
	
	public CfiUploadDocumentRequest() {
	}

	public static CfiUploadDocumentRequest newInstance(CfiUploadDocumentRequest request) {
		return new CfiUploadDocumentRequest(request);
	}
	
	public String getDokumenPeruri() {
		return dokumenPeruri;
	}
	public void setDokumenPeruri(String dokumenPeruri) {
		this.dokumenPeruri = dokumenPeruri;
	}
	public String getDokumenDate() {
		return dokumenDate;
	}
	public void setDokumenDate(String dokumenDate) {
		this.dokumenDate = dokumenDate;
	}
	public String getFilename() {
		return filename;
	}
	public void setFilename(String filename) {
		this.filename = filename;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getNotes() {
		return notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}

	public String getDocumentId() {
		return documentId;
	}

	public void setDocumentId(String documentId) {
		this.documentId = documentId;
	}
}
