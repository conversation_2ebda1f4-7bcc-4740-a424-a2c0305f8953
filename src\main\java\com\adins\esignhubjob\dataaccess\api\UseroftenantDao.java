package com.adins.esignhubjob.dataaccess.api;

import com.adins.esignhubjob.model.table.MsUseroftenant;

public interface UseroftenantDao {
    
    void insertUseroftenant(MsUseroftenant useroftenant);
    void insertUseroftenantNewTran(MsUseroftenant useroftenant);

    MsUseroftenant getUserTenantByIdMsUserAndTenantCode(Long idMsUser, String tenantCode);
    MsUseroftenant getUserTenantByIdMsUserAndTenantCodeNewTran(Long idMsUser, String tenantCode);
}
