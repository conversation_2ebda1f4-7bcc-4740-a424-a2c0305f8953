package com.adins.esignhubjob.model.webservice.adins;

import java.util.List;

import com.adins.esignhubjob.model.custom.adins.LivenessResponseBean;

public class LivenessResponse {
    private String datetime;
    private String status;
    private String error;
    private List<LivenessResponseBean> result;

    public String getDatetime() {
        return this.datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getError() {
        return this.error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public List<LivenessResponseBean> getResult() {
        return this.result;
    }

    public void setResult(List<LivenessResponseBean> result) {
        this.result = result;
    }

}
