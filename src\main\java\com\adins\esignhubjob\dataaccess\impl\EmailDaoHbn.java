package com.adins.esignhubjob.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.EmailDao;
import com.adins.esignhubjob.model.table.MsEmailHosting;

@Transactional
@Component
public class EmailDaoHbn extends BaseDaoHbn implements EmailDao {

    @Override
    public MsEmailHosting getEmailHostingById(Long id) {
        return managerDAO.selectOne(MsEmailHosting.class, id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MsEmailHosting getEmailHostingByIdNewTrx(Long id) {
        return managerDAO.selectOne(MsEmailHosting.class, id);
    }
    
}
