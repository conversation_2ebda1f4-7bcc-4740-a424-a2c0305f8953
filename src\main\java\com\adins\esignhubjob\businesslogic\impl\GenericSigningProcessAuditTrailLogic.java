package com.adins.esignhubjob.businesslogic.impl;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.SigningProcessAuditTrailLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.util.ZipCompressionUtils;
import com.aliyun.fc.runtime.Context;

@Component
public class GenericSigningProcessAuditTrailLogic extends BaseLogic implements SigningProcessAuditTrailLogic {

    @Autowired private AliyunOssCloudStorageLogic cloudStorageLogic;

    @Override
    public String logProcessRequestResponse(TrSigningProcessAuditTrail trail, String subfolderName, String requestBody, String responseBody, boolean appendExistingLog, Context context) {
        
        StringBuilder builder = new StringBuilder();
		
		if (appendExistingLog) {
			byte[] zippedTextFile = cloudStorageLogic.getZippedApiLogAuditTrail(subfolderName, trail, context);
			if (ArrayUtils.isNotEmpty(zippedTextFile)) {
				String unzippedText = ZipCompressionUtils.unzipText(zippedTextFile, context);
				builder.append(unzippedText);
			}
		}
		
		if (builder.length() > 0) {
			builder.append(System.lineSeparator());
		}
		
		if (StringUtils.isNotBlank(requestBody)) {
			builder.append("REQ: ");
			builder.append(requestBody);
		}
		
		if (builder.length() > 0) {
			builder.append(System.lineSeparator());
		}
		
		if (StringUtils.isNotBlank(responseBody)) {
			builder.append("RES: ");
			builder.append(responseBody);
		}
		
		String appendedLog = builder.toString();
		String textFilename = String.valueOf(trail.getIdSigningProcessAuditTrail()) + ".txt";
		
		byte[] zippedAppendedLog = ZipCompressionUtils.zipText(textFilename, appendedLog, context);
		return cloudStorageLogic.storeZippedApiLogAuditTrail(subfolderName, trail, zippedAppendedLog, context);
    }
    
}
