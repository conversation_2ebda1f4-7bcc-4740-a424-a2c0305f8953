package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsPeruriDocType;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.esignhubjob.model.webservice.vida.VidaGetPeruriDocTypeBean;
import com.adins.esignhubjob.model.webservice.vida.VidaGetPeruriDocumentTypeResponse;
import com.aliyun.fc.runtime.Context;


public class UpdateEmateraiDocumentTypeVida extends BaseJobHandler {

    private static final String AUDIT = "FC_UPDATE_PERURI_DOC_TYPE_VIDA";
    
    private static final String[] EMAIL_RECEIVERS = { "<EMAIL>" };

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {
        
        String partnerId = System.getenv(Constants.ENV_VAR_VIDA_GET_PARTNER_ID);
        
        String receiver = daoFactory.getGeneralSettingDao()
                .getGsValueByCode(AmGlobalKey.GS_SEND_EMAIL_PIC_OPERATION_ADINS);

        String[] receivers = receiver.split(";");
        if (StringUtils.isBlank(receivers[0])) {
            receivers = EMAIL_RECEIVERS;
        }

        VidaGetPeruriDocumentTypeResponse response = logicFactory.getVidaLogic().getPeruriDocumentType(partnerId, context);

        for (VidaGetPeruriDocTypeBean item : response.getData()) {
            
            List<MsPeruriDocType> peruriDocType = daoFactory.getPeruriDocTypeDao().getMsPeruriDocTypeByDocName(item.getNameDocument());

            if (peruriDocType.isEmpty()) {
                context.getLogger().info("New Document Doc Type Found Send Emai!");
                
                Map<String, Object> data = new HashMap<>();
                data.put("documentName", item.getNameDocument());
                data.put("documentCode", item.getCodeDocument());

                MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(Constants.NEW_PERURI_DOC_TYPE, data);
                
                sendEmailUpdateEmateraiDocumentTypeVida(template, receivers, context);
            } else {
                updateEmateraiDocumentTypeVida(peruriDocType, item, context);
            }
        }

        String notes = "Done";
        insertTrSchedulerJob(null, Constants.LOV_CODE_SCHEDULER_WEEKLY, Constants.CODE_LOV_JOB_TYPE_UPDATE_EMATERAI_DOCUMENT_TYPE_VIDA, response.getData().size(), notes);
    }
    
    private void insertTrSchedulerJob(Date startTime, String lovSchedulerTypeCode, String lovJobTypeCode,
			long dataProcessed, String notes) {
		MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE,
				lovSchedulerTypeCode);
		MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, lovJobTypeCode);

		TrSchedulerJob schedulerJob = new TrSchedulerJob();
		schedulerJob.setSchedulerStart(startTime);
		schedulerJob.setSchedulerEnd(new Date());
		schedulerJob.setMsLovBySchedulerType(lovSchedulerType);
		schedulerJob.setMsLovByJobType(lovJobType);
		schedulerJob.setDataProcessed(dataProcessed);
		schedulerJob.setNotes(notes);
		schedulerJob.setUsrCrt(AUDIT);
		schedulerJob.setDtmCrt(new Date());
		daoFactory.getSchedulerJobDao().insertSchedulerJob(schedulerJob);
	}

    private void sendEmailUpdateEmateraiDocumentTypeVida(MsMsgTemplate template, String[] receivers, Context context) {
        EmailInformationBean emailBean = new EmailInformationBean();
        emailBean.setBodyMessage(template.getBody());
        emailBean.setSubject(template.getSubject());
        emailBean.setTo(receivers);

        logicFactory.getEmailSenderLogic().sendEmail(emailBean, null, context);
    }

    private void updateEmateraiDocumentTypeVida(List<MsPeruriDocType> peruriDocType, VidaGetPeruriDocTypeBean item, Context context) {
        for (MsPeruriDocType docType : peruriDocType) {
            if (docType.getIsActive().equals("1")) {
                if (docType.getVidaDocCode() == null || !docType.getVidaDocCode().equals(item.getCodeDocument())) {
                    docType.setVidaDocCode(item.getCodeDocument());
                    docType.setDtmUpd(new Date());
                    docType.setUsrUpd(AUDIT);
                    daoFactory.getPeruriDocTypeDao().updateMsPeruriDocType(docType);
                    context.getLogger().info(String.format("Update PeruriDocType with code : %1$s", docType.getDocCode()));
                }
            } 
        }
    }
}