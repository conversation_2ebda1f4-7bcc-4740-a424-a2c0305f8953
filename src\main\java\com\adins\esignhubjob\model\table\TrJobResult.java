package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_job_result")
public class TrJobResult extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	public static final String ID_JOB_RESULT_HBM = "idJobResult";
	
	private long idJobResult;
	private MsJob msJob;
	private String requestParams;
	private MsTenant msTenant;
	private AmMsuser amMsuser;
	private Date processStartTime;
	private Date processFinishTime;
	private String resultFileLocation;
	private Short processStatus;
	private MsLov msLovJobType;
	private String errorMessage;
	
	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_job_result", unique = true, nullable = false)
	public long getIdJobResult() {
		return idJobResult;
	}
	
	public void setIdJobResult(long idJobResult) {
		this.idJobResult = idJobResult;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_job")
	public MsJob getMsJob() {
		return msJob;
	}
	
	public void setMsJob(MsJob msJob) {
		this.msJob = msJob;
	}
	
	@Column(name = "request_params", length = 500, nullable = false)
	public String getRequestParams() {
		return requestParams;
	}
	
	public void setRequestParams(String requestParams) {
		this.requestParams = requestParams;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return msTenant;
	}
	
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
	public AmMsuser getAmMsuser() {
		return amMsuser;
	}
	
	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "process_start_time", length = 29)
	public Date getProcessStartTime() {
		return processStartTime;
	}
	
	public void setProcessStartTime(Date processStartTime) {
		this.processStartTime = processStartTime;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "process_finish_time", length = 29)
	public Date getProcessFinishTime() {
		return processFinishTime;
	}
	
	public void setProcessFinishTime(Date processFinishTime) {
		this.processFinishTime = processFinishTime;
	}
	
	@Column(name = "result_file_location", length = 300)
	public String getResultFileLocation() {
		return resultFileLocation;
	}
	
	public void setResultFileLocation(String resultFileLocation) {
		this.resultFileLocation = resultFileLocation;
	}

	@Column(name = "process_Status")
	public Short getProcessStatus() {
		return processStatus;
	}

	public void setProcessStatus(Short processStatus) {
		this.processStatus = processStatus;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_job_type")
	public MsLov getMsLovJobType() {
		return msLovJobType;
	}
	
	public void setMsLovJobType(MsLov msLovJobType) {
		this.msLovJobType = msLovJobType;
	}
	
	@Column(name = "error_message", length = 1000)
	public String getErrorMessage() {
		return errorMessage;
	}
	
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
}
