package com.adins.esignhubjob.dataaccess.api;

import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrInvitationLink;

public interface InvitationLinkDao {
    TrInvitationLink getInvitationLinkNewTrx(String receiverDetail, MsVendor vendor, MsTenant tenant);
    void updateInvitationLinkNewTrx(TrInvitationLink invLink);
}
