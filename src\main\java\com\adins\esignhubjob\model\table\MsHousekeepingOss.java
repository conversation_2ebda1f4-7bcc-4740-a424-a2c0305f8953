package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_housekeeping_oss")
public class MsHousekeepingOss extends CreatableAndUpdatableEntity implements Serializable  {
    private static final long serialVersionUID = 1L;

    private long IdMsHousekeepingOss;
    private String path;
    private MsLov LovTimeSchedule;
    private String note;
    private String ExpiredValue;
    private String isActive;

    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_ms_housekeeping_oss", unique = true, nullable = false)
    public long getIdMsHousekeepingOss() {
        return IdMsHousekeepingOss;
    }

    public void setIdMsHousekeepingOss(long idMsHousekeepingOss) {
        IdMsHousekeepingOss = idMsHousekeepingOss;
    }

    @Column(name = "path", length = 200)
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_time_schedule")
    public MsLov getLovTimeSchedule() {
        return LovTimeSchedule;
    }
    public void setLovTimeSchedule(MsLov lovTimeSchedule) {
        LovTimeSchedule = lovTimeSchedule;
    }

    @Column(name = "note", length = 200)
    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    @Column(name = "expired_value", length = 200)
    public String getExpiredValue() {
        return ExpiredValue;
    }

    public void setExpiredValue(String expiredValue) {
        ExpiredValue = expiredValue;
    }

    @Column(name = "is_active", length = 1)
    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }
}
