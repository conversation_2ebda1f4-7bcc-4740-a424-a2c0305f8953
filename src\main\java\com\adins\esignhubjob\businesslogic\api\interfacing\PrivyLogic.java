package com.adins.esignhubjob.businesslogic.api.interfacing;

import java.io.IOException;
import java.util.List;

import com.adins.esignhubjob.model.custom.adins.RegisterExternalRequestBean;
import com.adins.esignhubjob.model.custom.adins.UserBean;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;
import com.adins.esignhubjob.model.webservice.privy.PrivyRegisterStatusV2Response;
import com.adins.esignhubjob.model.webservice.privy.PrivyDocumentStatusResponse;
import com.adins.esignhubjob.model.webservice.privy.PrivyRegisterStatusResponse;
import com.adins.esignhubjob.model.webservice.privy.PrivySignResponse;
import com.aliyun.fc.runtime.Context;

public interface PrivyLogic {
    // Sign related
    PrivySignResponse signDocuments(TrDocumentSigningRequest signingRequest, AmMsuser user, List<TrDocumentDSign> signLocations, MsVendoroftenant vendoroftenant, Context context) throws IOException;
    PrivyDocumentStatusResponse checkDocumentStatus(TrDocumentSigningRequest signingRequest, TrDocumentD document, MsVendoroftenant vendoroftenant, Context context) throws IOException;
    
    // Register related
    PrivyRegisterStatusV2Response checkRegisterStatusV2(TrJobCheckRegisterStatus jobCheckRegisterStatus, MsVendoroftenant vendoroftenant, String userToken, Context context) throws IOException;
    AmMsuser insertRegisteredUser(RegisterExternalRequestBean request, String privyId, MsTenant tenant, MsVendor vendor, Context context);
    AmMsuser insertInvitationRegisteredUser(UserBean userData, String privyId, MsLov lovUserType, MsTenant tenant, MsVendor vendor, Context context);
    String getCheckRegisterStatusMessage(PrivyRegisterStatusV2Response response, Context context);

    // Deprecated by PRIVY (must use the V2)
    PrivyRegisterStatusResponse checkRegisterStatus(MsVendoroftenant vendoroftenant, String requestId, Context context) throws IOException;
    
}
