package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_balance_mutation")
public class TrBalanceMutation extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	public static final String TR_STAMP_DUTY_HBM = "trStampDuty";
	
	private long idBalanceMutation;
	private AmMsuser amMsuser;
	private MsLov msLovByLovBalanceType;
	private MsLov msLovByLovTrxType;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private TrDocumentD trDocumentD;
	private TrDocumentH trDocumentH;
	private TrStampDuty trStampDuty;
	private String trxNo;
	private Date trxDate;
	private String refNo;
	private Integer qty;
	private String notes;
	private String vendorTrxNo;
	private MsOffice msOffice;
	private MsBusinessLine msBusinessLine;
	private TrBalanceTopUp trBalanceTopUp;

	private Set<TrJobCheckRegisterStatus> trJobCheckRegisterStatus = new HashSet<>(0);
    
	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_balance_mutation", unique = true, nullable = false)
	public long getIdBalanceMutation() {
		return this.idBalanceMutation;
	}

	public void setIdBalanceMutation(long idBalanceMutation) {
		this.idBalanceMutation = idBalanceMutation;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_balance_type")
	public MsLov getMsLovByLovBalanceType() {
		return this.msLovByLovBalanceType;
	}

	public void setMsLovByLovBalanceType(MsLov msLovByLovBalanceType) {
		this.msLovByLovBalanceType = msLovByLovBalanceType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_trx_type")
	public MsLov getMsLovByLovTrxType() {
		return this.msLovByLovTrxType;
	}

	public void setMsLovByLovTrxType(MsLov msLovByLovTrxType) {
		this.msLovByLovTrxType = msLovByLovTrxType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return this.msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return this.trDocumentD;
	}

	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_h")
	public TrDocumentH getTrDocumentH() {
		return this.trDocumentH;
	}

	public void setTrDocumentH(TrDocumentH trDocumentH) {
		this.trDocumentH = trDocumentH;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_stamp_duty")
	public TrStampDuty getTrStampDuty() {
		return this.trStampDuty;
	}

	public void setTrStampDuty(TrStampDuty trStampDuty) {
		this.trStampDuty = trStampDuty;
	}

	@Column(name = "trx_no", length = 45)
	public String getTrxNo() {
		return this.trxNo;
	}

	public void setTrxNo(String trxNo) {
		this.trxNo = trxNo;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "trx_date", length = 29)
	public Date getTrxDate() {
		return this.trxDate;
	}

	public void setTrxDate(Date trxDate) {
		this.trxDate = trxDate;
	}

	@Column(name = "ref_no", length = 100)
	public String getRefNo() {
		return this.refNo;
	}

	public void setRefNo(String refNo) {
		this.refNo = refNo;
	}

	@Column(name = "qty")
	public Integer getQty() {
		return this.qty;
	}

	public void setQty(Integer qty) {
		this.qty = qty;
	}

	@Column(name = "notes", length = 200)
	public String getNotes() {
		return this.notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	@Column(name = "vendor_trx_no", length = 200)
	public String getVendorTrxNo() {
		return vendorTrxNo;
	}

	public void setVendorTrxNo(String vendorTrxNo) {
		this.vendorTrxNo = vendorTrxNo;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_office")
	public MsOffice getMsOffice() {
		return msOffice;
	}

	public void setMsOffice(MsOffice msOffice) {
		this.msOffice = msOffice;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_business_line")
	public MsBusinessLine getMsBusinessLine() {
		return msBusinessLine;
	}

	public void setMsBusinessLine(MsBusinessLine msBusinessLine) {
		this.msBusinessLine = msBusinessLine;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trBalanceMutation")
	public Set<TrJobCheckRegisterStatus> getTrJobCheckRegisterStatus() {
		return trJobCheckRegisterStatus;
	}

	public void setTrJobCheckRegisterStatus(Set<TrJobCheckRegisterStatus> trJobCheckRegisterStatus) {
		this.trJobCheckRegisterStatus = trJobCheckRegisterStatus;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_balance_top_up")
	public TrBalanceTopUp getTrBalanceTopUp() {
		return trBalanceTopUp;
	}

	public void setTrBalanceTopUp(TrBalanceTopUp trBalanceTopUp) {
		this.trBalanceTopUp = trBalanceTopUp;
	}
}
