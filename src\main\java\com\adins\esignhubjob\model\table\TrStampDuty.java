package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_stamp_duty")
public class TrStampDuty extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	public static final String ID_STAMP_DUTY_HBM = "idStampDuty";

	private long idStampDuty;
	private MsLov msLov;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private String trxNo;
	private String stampDutyNo;
	private Integer stampDutyFee;
	private String stampQr;
	private String transactionId;
	private String documentNameSdt;
	private String errorMessage;
	
	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);
	private Set<TrDocumentDStampduty> trDocumentDStampduties = new HashSet<>(0);

	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_stamp_duty", unique = true, nullable = false)
	public long getIdStampDuty() {
		return this.idStampDuty;
	}

	public void setIdStampDuty(long idStampDuty) {
		this.idStampDuty = idStampDuty;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_stamp_duty_status")
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return this.msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@Column(name = "trx_no", length = 45)
	public String getTrxNo() {
		return this.trxNo;
	}

	public void setTrxNo(String trxNo) {
		this.trxNo = trxNo;
	}

	@Column(name = "stamp_duty_no", length = 45)
	public String getStampDutyNo() {
		return this.stampDutyNo;
	}

	public void setStampDutyNo(String stampDutyNo) {
		this.stampDutyNo = stampDutyNo;
	}

	@Column(name = "stamp_duty_fee")
	public Integer getStampDutyFee() {
		return this.stampDutyFee;
	}

	public void setStampDutyFee(Integer stampDutyFee) {
		this.stampDutyFee = stampDutyFee;
	}
	
	@Column(name = "stamp_qr", length = 350)
	public String getStampQr() {
		return this.stampQr;
	}

	public void setStampQr(String stampQr) {
		this.stampQr = stampQr;
	}

	@Column(name = "transaction_id", length = 100)
	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	@Column(name = "document_name_sdt", length = 100)
	public String getDocumentNameSdt() {
		return documentNameSdt;
	}

	public void setDocumentNameSdt(String documentNameSdt) {
		this.documentNameSdt = documentNameSdt;
	}

	@Column(name = "error_message", length = 500)
	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trStampDuty")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return this.trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trStampDuty")
	public Set<TrDocumentDStampduty> getTrDocumentDStampduties() {
		return this.trDocumentDStampduties;
	}

	public void setTrDocumentDStampduties(Set<TrDocumentDStampduty> trDocumentDStampduties) {
		this.trDocumentDStampduties = trDocumentDStampduties;
	}
}
