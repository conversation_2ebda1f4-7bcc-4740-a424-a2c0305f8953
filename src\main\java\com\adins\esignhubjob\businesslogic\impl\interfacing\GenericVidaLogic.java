package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.CommonStampingLogic;
import com.adins.esignhubjob.businesslogic.api.SignImageLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.VidaLogic;
import com.adins.esignhubjob.model.custom.adins.SignLocationBean;
import com.adins.esignhubjob.model.custom.vida.VidaDeviceBean;
import com.adins.esignhubjob.model.custom.vida.VidaRequestInfoBean;
import com.adins.esignhubjob.model.custom.vida.VidaSignAppearanceBean;
import com.adins.esignhubjob.model.custom.vida.VidaSignLocationBean;
import com.adins.esignhubjob.model.custom.vida.VidaSigningInfoBean;
import com.adins.esignhubjob.model.custom.vida.VidaSigningResponseContainer;
import com.adins.esignhubjob.model.custom.vida.VidaUserBean;
import com.adins.esignhubjob.model.table.MsPeruriDocType;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.webservice.vida.VidaCheckStampStatusResponse;
import com.adins.esignhubjob.model.webservice.vida.VidaGetPeruriDocumentTypeResponse;
import com.adins.esignhubjob.model.webservice.vida.VidaLogOnResponse;
import com.adins.esignhubjob.model.webservice.vida.VidaSignRequest;
import com.adins.esignhubjob.model.webservice.vida.VidaStampRequest;
import com.adins.esignhubjob.model.webservice.vida.VidaStampResponse;
import com.adins.esignhubjob.model.webservice.vida.VidaTokenResponse;
import com.adins.exceptions.EsignhubJobException;
import com.aliyun.fc.runtime.Context;
import com.google.gson.Gson;

import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

@Component
public class GenericVidaLogic extends BaseLogic implements VidaLogic {

    @Autowired private AliyunOssCloudStorageLogic cloudStorageLogic;
    @Autowired private SignImageLogic signImageLogic;
    @Autowired private CommonStampingLogic commonStampingLogic;

    private long getSignConnectTimeout() {
        String timeoutValue = System.getenv(Constants.ENV_VAR_VIDA_SIGN_CONN_TIMEOUT);
        try {
            return Long.valueOf(timeoutValue);
        } catch (Exception e) {
            return 10L;
        }
    }

    private long getSignReadTimeout() {
        String timeoutValue = System.getenv(Constants.ENV_VAR_VIDA_SIGN_READ_TIMEOUT);
        try {
            return Long.valueOf(timeoutValue);
        } catch (Exception e) {
            return 90L;
        }
    }

    private long getStampConnectTimeout() {
        String timeoutValue = System.getenv(Constants.ENV_VAR_VIDA_STAMP_CONN_TIMEOUT);
        try {
            return Long.valueOf(timeoutValue);
        } catch (Exception e) {
            return 10L;
        }
    }

    private long getStampReadTimeout() {
        String timeoutValue = System.getenv(Constants.ENV_VAR_VIDA_STAMP_READ_TIMEOUT);
        try {
            return Long.valueOf(timeoutValue);
        } catch (Exception e) {
            return 90L;
        }
    }

    private String getToken(MsVendoroftenant vendoroftenant, Context context) throws IOException {
        String url = System.getenv(Constants.ENV_VAR_VIDA_GET_TOKEN_URI);
        String credential = vendoroftenant.getClientId() + ":" + vendoroftenant.getClientSecret();
        String auth = Base64.getEncoder().encodeToString(credential.getBytes());

        OkHttpClient client = new OkHttpClient.Builder().build();

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, "Basic " + auth);
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_X_WWW_FORM_URLENCODED);
        Headers header = Headers.of(headerMap);

        RequestBody formBody = new FormBody.Builder().add("grant_type", "client_credentials").add("scope", "roles").build(); 
        Request request = new Request.Builder().url(url).headers(header).post(formBody).build();
        Response tokenResponse = client.newCall(request).execute();
        ResponseBody body = tokenResponse.body();
        String json = body.string();

        String responseCode = StringUtils.isBlank(tokenResponse.message()) ? String.valueOf(tokenResponse.code()) : tokenResponse.code() + " " + tokenResponse.message();
        context.getLogger().debug(String.format("Generate VIDA token for %1$s response code: %2$s, body: %3$s", vendoroftenant.getMsTenant().getTenantCode(), responseCode, json));

        Gson gson = new Gson();
        VidaTokenResponse token = gson.fromJson(json, VidaTokenResponse.class);
        return token.getAccessToken();
    }

    @Override
    public VidaSigningResponseContainer signVida(TrDocumentSigningRequest signingRequest, TrDocumentDSign documentDSign, MsVendorRegisteredUser vendorRegisteredUser, String reservedTrxNo, Context context) throws IOException {
        VidaSigningResponseContainer container = new VidaSigningResponseContainer();
        MsTenant tenant = documentDSign.getTrDocumentD().getMsTenant();
        MsVendor vendor = documentDSign.getTrDocumentD().getMsVendor();
        TrDocumentD document = signingRequest.getTrDocumentD();
        TrDocumentH documentH = document.getTrDocumentH();

        MsVendoroftenant vendoroftenant = daoFactory.getVendoroftenantDao().getVendoroftenantNewTran(tenant.getTenantCode(), vendor.getVendorCode());        
        String token = getToken(vendoroftenant, context);

        VidaUserBean user = new VidaUserBean();
        user.setVidaEkycEventId(vendorRegisteredUser.getVendorRegistrationId());
        
        long consentTime = signingRequest.getUserSigningConsentTimestamp().getTime() / 1000;
        VidaRequestInfoBean requestInfo = new VidaRequestInfoBean();
        requestInfo.setSrcIp(signingRequest.getUserRequestIp());
        requestInfo.setUserAgent(signingRequest.getUserRequestBrowserInformation());
        requestInfo.setConsentTimestamp(String.valueOf(consentTime));

        VidaDeviceBean device = VidaDeviceBean.getDummyInstance();

        VidaSignAppearanceBean appearance = new VidaSignAppearanceBean();
        if ("1".equals(tenant.getUseCustomSignImage())) {
            String nameFormat = Arrays.stream(vendorRegisteredUser.getAmMsuser().getFullName().toLowerCase().split(" "))
            .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1))
            .collect(Collectors.joining(" "));
            
            byte[] signImage = signImageLogic.generateSignImage(nameFormat, context);
            if (signImage == null || signImage.length == 0) {
                throw new EsignhubJobException("Failed to get signature image");
            }

            appearance.setType("provided");
            appearance.setSignImage(Base64.getEncoder().encodeToString(signImage));
        } else {
            appearance.setType("standard");
        }
        

        byte[] documentByteArray = cloudStorageLogic.getBaseSignDocument(documentDSign.getTrDocumentD(), context);
        String base64Document = Base64.getEncoder().encodeToString(documentByteArray);
        String signLocationJson = documentDSign.getVidaSignLocation();
        VidaSignLocationBean location = gson.fromJson(signLocationJson, VidaSignLocationBean.class);

        boolean useQr = "1".equals(document.getUseSignQr());

        VidaSigningInfoBean signingInfo = new VidaSigningInfoBean();
        signingInfo.setPdfFile("base64doc");
        signingInfo.setQrEnable(useQr);
        signingInfo.setAppearance(appearance);
        signingInfo.setHeight(String.valueOf(location.getH()));
        signingInfo.setWidth(String.valueOf(location.getW()));
        signingInfo.setXPoint(String.valueOf(location.getX()));
        signingInfo.setYPoint(String.valueOf(location.getY()));
        signingInfo.setPageNo(String.valueOf(documentDSign.getSignPage()));
        List<VidaSigningInfoBean> signingInfoBeans = new ArrayList<>();
        signingInfoBeans.add(signingInfo);

        VidaSignRequest request = new VidaSignRequest();
        request.setDevice(device);
        request.setPartnerTrxId(reservedTrxNo);
        request.setRequestInfo(requestInfo);
        request.setSigningInfo(signingInfoBeans);
        request.setUser(user);

        String requestLog = gson.toJson(request);
        context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Sign VIDA request: %3$s", documentH.getRefNumber(), document.getDocumentId(), requestLog));

        // Header
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.BEARER + token);
        Headers headers = Headers.of(headerMap);

        // Body
        request.getSigningInfo().get(0).setPdfFile(base64Document);
        String jsonRequest = gson.toJson(request);
        container.setRequest(requestLog);
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        // Prepare request
        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(System.getenv(Constants.ENV_VAR_VIDA_SIGN_URI))
            .post(body)
            .build();

        // Call API
        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(getSignConnectTimeout(), TimeUnit.SECONDS)
            .readTimeout(getSignReadTimeout(), TimeUnit.SECONDS)
            .build();
        
        Response okHttResponse = client.newCall(okHttpRequest).execute();
        String jsonResponse = okHttResponse.body().string();

        String responseCode = StringUtils.isBlank(okHttResponse.message()) ? String.valueOf(okHttResponse.code()) : okHttResponse.code() + " " + okHttResponse.message();
        context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Sign VIDA response code: %3$s, body: %4$s", documentH.getRefNumber(), document.getDocumentId(), responseCode, jsonResponse));
        container.setResponse(jsonResponse);
        return container;
    }

    @Override
    public VidaSigningResponseContainer getDocumentStatus(TrDocumentDSign documentDSign, String trackStatusId, Context context) throws IOException {
        VidaSigningResponseContainer container = new VidaSigningResponseContainer();
        MsTenant tenant = documentDSign.getTrDocumentD().getMsTenant();
        MsVendor vendor = documentDSign.getTrDocumentD().getMsVendor();
        TrDocumentD document = documentDSign.getTrDocumentD();
        TrDocumentH documentH = document.getTrDocumentH();

        MsVendoroftenant vendoroftenant = daoFactory.getVendoroftenantDao().getVendoroftenantNewTran(tenant.getTenantCode(), vendor.getVendorCode());        
        String token = getToken(vendoroftenant, context);
        
        Map<String, String> params = new HashMap<>();
        params.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.BEARER + token);
        params.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        Headers headers = Headers.of(params);

        String url = System.getenv(Constants.ENV_VAR_VIDA_DOC_STATUS_URI) + trackStatusId;
        container.setRequest(trackStatusId);
        context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Check document status with URL: %3$s", documentH.getRefNumber(), document.getDocumentId(), url));

        Request request = new Request.Builder()
                .headers(headers)
                .url(url)
                .build();

        OkHttpClient client = new OkHttpClient();
        Response response = client.newCall(request).execute();
        String jsonResponse = response.body().string();
        container.setResponse(jsonResponse);
        
        String responseCode = StringUtils.isBlank(response.message()) ? String.valueOf(response.code()) : response.code() + " " + response.message();
        context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Check document status response code: %3$s, body: %4$s", documentH.getRefNumber(), document.getDocumentId(), responseCode, jsonResponse));

        return container;
    }

    @Override
    public VidaGetPeruriDocumentTypeResponse getPeruriDocumentType(String partnerId, Context context) throws IOException{
        
        String token = vidaLogOn(context);

        if (token == null) {
            context.getLogger().error("token is not null.");
        }
        

        context.getLogger().debug(String.format("VIDA Token: %1$s", token));

        Map<String, String> params = new HashMap<>();
        params.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.BEARER + token);
        params.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        params.put(HttpHeaders.X_PARTNER_ID, partnerId);
        Headers headers = Headers.of(params);

        String url = System.getenv(Constants.ENV_VAR_VIDA_GET_PERURI_DOCUMENT_TYPE_URL);

        Request request = new Request.Builder()
                .headers(headers)
                .url(url)
                .build();

        OkHttpClient client = new OkHttpClient();
        Response response = client.newCall(request).execute();
        String jsonResponse = response.body().string();
        int responseCode = response.code();
        context.getLogger().debug(String.format("Get VIDA Document Type Response Code : %1$s", responseCode));
        
        return gson.fromJson(jsonResponse, VidaGetPeruriDocumentTypeResponse.class);
    
    }

    @Override
    public String vidaLogOn(Context context) throws IOException {
        String url = System.getenv(Constants.ENV_VAR_VIDA_LOG_ON_URL);
        OkHttpClient client = new OkHttpClient.Builder().build();
        String clientId = System.getenv(Constants.ENV_VAR_VIDA_GET_CLIENT_ID);
        String clientSecret = System.getenv(Constants.ENV_VAR_VIDA_GET_CLIENT_SECRET);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_X_WWW_FORM_URLENCODED);
        Headers header = Headers.of(headerMap);

        RequestBody formBody = new FormBody.Builder().add("grant_type", "client_credentials")
            .add("client_id", clientId)
            .add("client_secret", clientSecret)
            .add("scope", "roles")
        .build();
        Request request = new Request.Builder().url(url).headers(header).post(formBody).build();
        Response tokenResponse = client.newCall(request).execute();
        ResponseBody body = tokenResponse.body();
        String json = body.string();

        String responseCode = StringUtils.isBlank(tokenResponse.message()) ? String.valueOf(tokenResponse.code()) : tokenResponse.code() + " " + tokenResponse.message();
        context.getLogger().debug(String.format("VIDA Log On Stamping response code: %1$s, body: %2$s", responseCode, json));

        Gson gson = new Gson();
        VidaLogOnResponse token = gson.fromJson(json, VidaLogOnResponse.class);
        return token.getAccessToken();
    }

    @Override
    public VidaStampResponse uploadStampVida(String accessToken, TrDocumentD document, TrDocumentDStampduty documentStampduty, String reservedTrxNo, Context context) throws IOException {
        String url = System.getenv(Constants.ENV_VAR_VIDA_UPLOAD_STAMP_URL);
        String partnerId = System.getenv(Constants.ENV_VAR_VIDA_GET_PARTNER_ID);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        headerMap.put(HttpHeaders.KEY_AUTHORIZATION, "Bearer " + accessToken);
        headerMap.put("X-PARTNER-ID", partnerId);
        Headers header = Headers.of(headerMap);

        VidaStampRequest request = prepareUploadStampVida(document, documentStampduty, reservedTrxNo, context);
        String jsonRequest = gson.toJson(request);
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        Request okHttpRequest = new Request.Builder()
            .headers(header)
            .url(url)
            .post(body)
            .build();

        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(getStampConnectTimeout(), TimeUnit.SECONDS)
            .readTimeout(getStampReadTimeout(), TimeUnit.SECONDS)
            .build();
        
        Response okHttResponse = client.newCall(okHttpRequest).execute();
        String jsonResponse = okHttResponse.body().string();

        String responseCode = StringUtils.isBlank(okHttResponse.message()) ? String.valueOf(okHttResponse.code()) : okHttResponse.code() + " " + okHttResponse.message();
        context.getLogger().info(String.format("Dokumen %1$s, Stamp VIDA response code: %2$s, body: %3$s", document.getDocumentId(), responseCode, jsonResponse));

        return gson.fromJson(jsonResponse, VidaStampResponse.class);
    }

    private VidaStampRequest prepareUploadStampVida(TrDocumentD document, TrDocumentDStampduty documentStampduty, String reservedTrxNo, Context context) {

        String base64Document = commonStampingLogic.getDocumentFileToUploadVida(document, context);
        String signLocationJson = documentStampduty.getSignLocation();
        SignLocationBean bean = gson.fromJson(signLocationJson, SignLocationBean.class);
        MsPeruriDocType peruriDocType = null == document.getMsPeruriDocType() ? null : document.getMsPeruriDocType();

        VidaStampRequest request = new VidaStampRequest();
        int bumperY = Integer.parseInt(daoFactory.getGeneralSettingDao().getGsValueByCode("VIDA_STAMP_BUMPER_POINT_Y"));
        int bumperX = Integer.parseInt(daoFactory.getGeneralSettingDao().getGsValueByCode("VIDA_STAMP_BUMPER_POINT_X"));
        request.setPage(documentStampduty.getSignPage());
        request.setLlx((int) Math.round(bean.getLlx()) + bumperX);
        request.setLly((int) Math.round(bean.getLly()) + bumperY);
        request.setRefNum(reservedTrxNo);
        request.setCodeDocument(null != peruriDocType ? peruriDocType.getVidaDocCode() : "VD002");

        String requestLog = gson.toJson(request);
        context.getLogger().info(String.format("Dokumen %1$s, Stamp VIDA request: %2$s", document.getDocumentId(), requestLog));

        request.setDocument(base64Document);
        return request;
    }

    @Override
    public VidaCheckStampStatusResponse checkStampStatus(String accessToken, TrDocumentD document, String reservedTrxNo, Context context) throws IOException {
            String url = System.getenv(Constants.ENV_VAR_VIDA_CHECK_STAMP_STATUS_URL) + reservedTrxNo;
            String partnerId = System.getenv(Constants.ENV_VAR_VIDA_GET_PARTNER_ID);
        
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("X-PARTNER-ID", partnerId);
            headerMap.put(HttpHeaders.KEY_AUTHORIZATION, "Bearer " + accessToken);
            Headers header = Headers.of(headerMap);

            Request request = new Request.Builder()
                .headers(header)
                .url(url)
                .build();
                
            // Call API
            OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(getStampConnectTimeout(), TimeUnit.SECONDS)
                .readTimeout(getStampReadTimeout(), TimeUnit.SECONDS)
                .build();

            Response okHttpResponse = client.newCall(request).execute();
            String jsonResponse = okHttpResponse.body().string();
            
            VidaCheckStampStatusResponse masked = gson.fromJson(jsonResponse, VidaCheckStampStatusResponse.class);
            if (null != masked.getData() && StringUtils.isNotBlank(masked.getData().getDocument())) {
                masked.getData().setDocument("{{Base64 Doc}}");
            }

            String responseCode = StringUtils.isBlank(okHttpResponse.message()) ? String.valueOf(okHttpResponse.code()) : okHttpResponse.code() + " " + okHttpResponse.message();
            context.getLogger().info(String.format("Dokumen %1$s, Check Stamp Status VIDA response code: %2$s, body: %3$s", document.getDocumentId(), responseCode, gson.toJson(masked)));
            return gson.fromJson(jsonResponse, VidaCheckStampStatusResponse.class);
    }
    
}
