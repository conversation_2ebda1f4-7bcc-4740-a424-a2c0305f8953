package com.adins.constants;

import java.util.Base64;

public class HttpHeaders {
    private HttpHeaders() {}

    // Key
    public static final String KEY_AUTHORIZATION = "Authorization";
    public static final String KEY_CONTENT_TYPE = "Content-Type";
    public static final String KEY_X_API_KEY = "x-api-key";
    public static final String KEY_ACCEPT = "Accept";
    public static final String X_PARTNER_ID = "X-PARTNER-ID";
    public static final String BEARER = "Bearer ";

    // Value
    public static final String APPLICATION_JSON = "application/json";
    public static final String APPLICATION_X_WWW_FORM_URLENCODED = "application/x-www-form-urlencoded";
    public static final String MULTIPART_FORMDATA = "multipart/form-data";

    // Privy Header
    public static final String PRIVY_MERCHANT_KEY = "Merchant-Key";

    public static String buildBasicAuthorization(String username, String password) {
      String toBeEncoded = username + ":" + password;
      String encoded = Base64.getEncoder().encodeToString(toBeEncoded.getBytes());
      return "Basic " + encoded;
    }

    public static String buildBearerToken(String token) {
      return BEARER + token;
    }
}
