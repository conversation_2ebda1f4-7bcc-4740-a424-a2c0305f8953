package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_job_check_register_status")
public class TrJobCheckRegisterStatus extends CreatableAndUpdatableEntity implements Serializable {
	
	private long idJobCheckRegisterStatus;
	private TrBalanceMutation trBalanceMutation;
	private String hashedIdNo;
	private Short requestStatus;
	private String isExternal;
	private MsLov lovUserType;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_job_check_register_status", unique = true, nullable = false)
	public long getIdJobCheckRegisterStatus() {
		return idJobCheckRegisterStatus;
	}

	public void setIdJobCheckRegisterStatus(long idJobCheckRegisterStatus) {
		this.idJobCheckRegisterStatus = idJobCheckRegisterStatus;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_balance_mutation")
	public TrBalanceMutation getTrBalanceMutation() {
		return trBalanceMutation;
	}

	public void setTrBalanceMutation(TrBalanceMutation trBalanceMutation) {
		this.trBalanceMutation = trBalanceMutation;
	}

	@Column(name = "hashed_id_no", length = 200)	
	public String getHashedIdNo() {
		return hashedIdNo;
	}

	public void setHashedIdNo(String hashedIdNo) {
		this.hashedIdNo = hashedIdNo;
	}

	@Column(name = "request_status", length = 1)
	public Short getRequestStatus() {
		return requestStatus;
	}

	public void setRequestStatus(Short requestStatus) {
		this.requestStatus = requestStatus;
	}

	@Column(name = "is_external", length = 1)
	public String getIsExternal() {
		return isExternal;
	}

	public void setIsExternal(String isExternal) {
		this.isExternal = isExternal;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_user_type")
	public MsLov getLovUserType() {
		return lovUserType;
	}

	public void setLovUserType(MsLov lovUserType) {
		this.lovUserType = lovUserType;
	}
	
}
