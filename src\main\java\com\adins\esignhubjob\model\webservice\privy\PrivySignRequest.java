package com.adins.esignhubjob.model.webservice.privy;

import java.util.List;

import com.adins.esignhubjob.model.custom.privy.PrivyDocumentBean;
import com.adins.esignhubjob.model.custom.privy.PrivySignatureSignRequestBean;

public class PrivySignRequest {
    private List<PrivyDocumentBean> documents;
    private PrivySignatureSignRequestBean signature;

    public List<PrivyDocumentBean> getDocuments() {
        return this.documents;
    }

    public void setDocuments(List<PrivyDocumentBean> documents) {
        this.documents = documents;
    }

    public PrivySignatureSignRequestBean getSignature() {
        return this.signature;
    }

    public void setSignature(PrivySignatureSignRequestBean signature) {
        this.signature = signature;
    }

}
