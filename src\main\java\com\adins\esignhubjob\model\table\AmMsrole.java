package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableAndDeletableEntity;

@Entity
@Table(name = "am_msrole")
public class AmMsrole extends ActivatableAndDeletableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	private long idMsRole;
	private String roleName;
	private String roleCode;
	private MsTenant msTenant;
	private Set<AmMemberofrole> amMemberofroles = new HashSet<>(0);
	private Set<AmMenuofrole> amMenuofroles = new HashSet<>(0);

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_role", unique = true, nullable = false)
	public long getIdMsRole() {
		return this.idMsRole;
	}

	public void setIdMsRole(long idMsRole) {
		this.idMsRole = idMsRole;
	}

	@Column(name = "role_name", nullable = false, length = 100)
	public String getRoleName() {
		return this.roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	@Column(name = "role_code", nullable = false, length = 20)
	public String getRoleCode() {
		return this.roleCode;
	}

	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsrole")
	public Set<AmMemberofrole> getAmMemberofroles() {
		return this.amMemberofroles;
	}

	public void setAmMemberofroles(Set<AmMemberofrole> amMemberofroles) {
		this.amMemberofroles = amMemberofroles;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsrole")
	public Set<AmMenuofrole> getAmMenuofroles() {
		return this.amMenuofroles;
	}

	public void setAmMenuofroles(Set<AmMenuofrole> amMenuofroles) {
		this.amMenuofroles = amMenuofroles;
	}
}
