package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_email_pattern")
public class MsEmailPattern extends CreatableAndUpdatableEntity implements Serializable{
    private static final long serialVersionUID = 1L;
	
	public static final String EMAIL_SENDER_HBM = "emailSender";
	public static final String SUBJECT_EMAIL_HBM = "subjectEmail";
	
	private long idEmailPattern;
	private MsLov msLov;
	private MsMsgTemplate msMsgTemplate;
	private Short priority;
	private String emailSender;
	private String sentenceKey;
	private Integer sentenceLength;
	private String action;
	private String subjectEmail;
	private String cssQuery;
	private String dataType;
	private Short elementIndex;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_email_pattern", unique = true, nullable = false)
	public long getIdEmailPattern() {
		return this.idEmailPattern;
	}

	public void setIdEmailPattern(long idEmailPattern) {
		this.idEmailPattern = idEmailPattern;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "email_category", nullable = false)
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_msg_template")
	public MsMsgTemplate getMsMsgTemplate() {
		return this.msMsgTemplate;
	}

	public void setMsMsgTemplate(MsMsgTemplate msMsgTemplate) {
		this.msMsgTemplate = msMsgTemplate;
	}

	@Column(name = "priority")
	public Short getPriority() {
		return this.priority;
	}

	public void setPriority(Short priority) {
		this.priority = priority;
	}

	@Column(name = "email_sender", length = 64)
	public String getEmailSender() {
		return this.emailSender;
	}

	public void setEmailSender(String emailSender) {
		this.emailSender = emailSender;
	}

	@Column(name = "sentence_key", length = 150)
	public String getSentenceKey() {
		return this.sentenceKey;
	}

	public void setSentenceKey(String sentenceKey) {
		this.sentenceKey = sentenceKey;
	}

	@Column(name = "sentence_length")
	public Integer getSentenceLength() {
		return this.sentenceLength;
	}

	public void setSentenceLength(Integer sentenceLength) {
		this.sentenceLength = sentenceLength;
	}

	@Column(name = "action", length = 20)
	public String getAction() {
		return this.action;
	}

	public void setAction(String action) {
		this.action = action;
	}
	
	@Column(name = "subject_email", length = 80)
	public String getSubjectEmail() {
		return subjectEmail;
	}

	public void setSubjectEmail(String subjectEmail) {
		this.subjectEmail = subjectEmail;
	}

	@Column(name = "css_query", length = 50)
	public String getCssQuery() {
		return cssQuery;
	}

	public void setCssQuery(String cssQuery) {
		this.cssQuery = cssQuery;
	}

	@Column(name = "data_type", length = 20)
	public String getDataType() {
		return dataType;
	}
	
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	
	@Column(name = "element_index")
	public Short getElementIndex() {
		return elementIndex;
	}

	public void setElementIndex(Short elementIndex) {
		this.elementIndex = elementIndex;
	}
}
