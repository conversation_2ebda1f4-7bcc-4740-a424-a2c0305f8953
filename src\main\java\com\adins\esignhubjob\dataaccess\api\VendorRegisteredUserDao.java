package com.adins.esignhubjob.dataaccess.api;

import java.math.BigInteger;
import java.util.List;

import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.TrDocumentH;

public interface VendorRegisteredUserDao {
    void insertVendorRegisteredUser(MsVendorRegisteredUser registeredUser);
    void insertVendorRegisteredUserNewTran(MsVendorRegisteredUser registeredUser);
    
    void updateVendorRegisteredUser(MsVendorRegisteredUser registeredUser);
    void updateVendorRegisteredUserNewTran(MsVendorRegisteredUser registeredUser);

    MsVendorRegisteredUser getVendorRegisteredUser(AmMsuser user, MsVendor vendor);
    MsVendorRegisteredUser getVendorRegisteredUserNewTran(AmMsuser user, MsVendor vendor);
    Ms<PERSON>endorRegisteredUser getLastUpdatedVendorRegisteredUser(AmMsuser user);
    MsVendorRegisteredUser getVendorRegisteredUserById(BigInteger idVendorRegisteredUser);
    MsVendorRegisteredUser getLatestVendorRegisteredUserNewTran(AmMsuser user);
    MsVendorRegisteredUser getVendorRegisteredUserByIdNo(String idNo, String vendorCode);
    MsVendorRegisteredUser getVendorRegisteredUserByPhone(String phone, String vendorCode);
    MsVendorRegisteredUser getVendorRegisteredUserByEmail(String email, String vendorCode);

    List<MsVendorRegisteredUser> getDocHSigners(TrDocumentH documentH);
}
