package com.adins.util;

import java.net.InetAddress;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.Constants;
import com.aliyun.fc.runtime.Context;

import okhttp3.OkHttpClient;

public class Tools {

	private static SecureRandom randGen = new SecureRandom();
	
	private Tools() {}

	/**
	 * @param connectTimeout connect timeout limit in seconds
	 * @param readTimeout read timeout limit in seconds
	 */
    public static OkHttpClient getUnsafeOkHttpClient(long connectTimeout, long readTimeout) throws NoSuchAlgorithmException, KeyManagementException {
		final TrustManager[] trustAllCerts = new TrustManager[] {
			new X509TrustManager() {
				@Override
				public void checkClientTrusted(X509Certificate[] chain, String authType) {
				}
				
				@Override
				public void checkServerTrusted(X509Certificate[] chain, String authType) {
				}
				
				@Override
				public X509Certificate[] getAcceptedIssuers() {
					return new X509Certificate[]{};
				}
			}
		};
		
		SSLContext sslContext = SSLContext.getInstance("SSL");
		sslContext.init(null, trustAllCerts, new SecureRandom());
		
		OkHttpClient.Builder builder = new OkHttpClient.Builder();
		builder.sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0]);
		builder.hostnameVerifier((hostname, session) -> true);
		
		builder.connectTimeout(connectTimeout, TimeUnit.SECONDS);
		builder.readTimeout(readTimeout, TimeUnit.SECONDS);
		return builder.build();
    }

	public static String bytesToHex(byte[] hash) {
	    StringBuilder hexString = new StringBuilder(2 * hash.length);
	    for (int i = 0; i < hash.length; i++) {
	        String hex = Integer.toHexString(0xff & hash[i]);
	        if(hex.length() == 1) {
	            hexString.append('0');
	        }
	        hexString.append(hex);
	    }
	    return hexString.toString();
	}
	
	public static String getHashedString(String params) {
		String hashedString = StringUtils.EMPTY;
		try {
			MessageDigest digest = MessageDigest.getInstance("SHA-256");
			byte[] hashedByteArray = digest.digest(params.getBytes());
			hashedString = bytesToHex(hashedByteArray);
		} catch (NoSuchAlgorithmException e) {
            return null;
		}
		return hashedString;
	}

	public static String convertDateString(String date, String initialFormat, String resultFormat) {
		try {
			SimpleDateFormat initFormat = new SimpleDateFormat(initialFormat);
			SimpleDateFormat finalFormat = new SimpleDateFormat(resultFormat);

			Date initDate = initFormat.parse(date);
			return finalFormat.format(initDate);
		} catch (Exception e) {
			return null;
		}
		
	}

	public static Date formatStringToDate(String inputDateString, String dateFormat) {
		if (StringUtils.isEmpty(inputDateString)) {
			return null;
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
		try {
			return sdf.parse(inputDateString);
		} catch (ParseException e) {
			return null;
		}
	}

	public static String appendQueryParamUrl(String baseUrl, Map<String, String> queryParams) {
		if (null == queryParams || queryParams.size() <= 0) {
			return baseUrl;
		}

		try {
			StringBuilder paramBuilder = new StringBuilder();
			for (Map.Entry<String, String> entry : queryParams.entrySet()) {
				if (paramBuilder.length() > 0) {
					paramBuilder.append("&");
				}
				paramBuilder
					.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
					.append("=")
					.append(URLEncoder.encode(entry.getValue(), "UTF-8"));
			}
			return baseUrl + "?" + paramBuilder.toString();
		} catch (Exception e) {
			return null;
		}

	}

	public static String formatDateToStringIn(Date inputDate, String dateFormat) {
		if (null == inputDate) {
			return null;
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);  
		return sdf.format(inputDate);
	}

	public static Date changeDateFormat(Date inputDate, String dateFormat) {
		String formattedDate = formatDateToStringIn(inputDate, dateFormat);
		return formatStringToDate(formattedDate, dateFormat);
	}

	public static String cutImageStringPrefix(String base64Image) {
		if (StringUtils.isEmpty(base64Image)) {
			return base64Image;
		}
		
		String prefix = StringUtils.EMPTY;
		if (base64Image.startsWith(Constants.IMG_JPEG_PREFIX)) {
			prefix = Constants.IMG_JPEG_PREFIX;
		} else if (base64Image.startsWith(Constants.IMG_JPG_PREFIX)) {
			prefix = Constants.IMG_JPG_PREFIX;
		} else if (base64Image.startsWith(Constants.IMG_PNG_PREFIX)) {
			prefix = Constants.IMG_PNG_PREFIX;
		}
		return base64Image.substring(prefix.length());
	}

	public static byte[] imageStringToByteArray(String base64Image) {
		if (StringUtils.isBlank(base64Image)) {
			// return empty byte array
			return new byte[0];
		}
		
		String imageString = cutImageStringPrefix(base64Image);
		return Base64.getDecoder().decode(imageString);
	}

	public static String getApplicationIpAddress(Context context) {
		try {
			return InetAddress.getLocalHost().getHostAddress();
		} catch (Exception e) {
			context.getLogger().error("Failed to get IP address");
			context.getLogger().error(ExceptionUtils.getStackTrace(e));
			return null;
		}
	}

	private static String maskString(String input, int unmaskedPrefix, int unmaskedSuffix, char maskChar) {
		if (StringUtils.isBlank(input) || input.length() < unmaskedPrefix + unmaskedSuffix) {
			return input;
		}
		
		String prefixString = input.substring(0, unmaskedPrefix);
		String suffixString = input.substring(input.length() - unmaskedSuffix);
		
		int charsLengthToMask = input.length() - unmaskedPrefix - unmaskedSuffix;
		StringBuilder mask = new StringBuilder();
		for (int i = 0; i < charsLengthToMask; i++) {
			mask.append(maskChar);
		}
		
		return prefixString + mask.toString() + suffixString;
	}

	private static String maskEmailAddress(String email, int prefixLength) {
		if (StringUtils.isBlank(email)) {
			return email;
		}
		// Regex example: (?<=.{3}).(?=.*@)
		String regex = "(?<=.{" + prefixLength + "}).(?=.*@)";
		return email.replaceAll(regex, "*");
	}

	private static final String maskName(String name) {
		String[] separated = name.split(" ");
		StringBuilder masked = new StringBuilder();
		for (String n : separated) {
			String nMasked;
			if (n.length() > 6) {
				nMasked = maskString(n, 2, 2, '*');
			} else {
				nMasked = maskString(n, 1, 1, '*');
			}
			
			if (masked.toString().length() == 0) {
				masked.append(nMasked);
			} else {
				masked.append(" ").append(nMasked);
			}
			
		}
		
		return masked.toString();
	}

	public static String maskData(String data) {

		if (StringUtils.isBlank(data)) {
			return data;
		}

		String masked = null;
		if (StringUtils.isNumeric(data)) {
			// Mask phone
			masked = maskString(data, 4, 3, '*');
		} else if (data.contains("@")) {
			// Mask email
			String beforeAt = data.split("@")[0];
			int prefix = beforeAt.length() > 7 ? 5 : beforeAt.length()/2;
			masked = maskEmailAddress(data, prefix);
		} else {
			// Mask name
			masked = maskName(data);
		}

		return StringUtils.left(masked, 36);
	}

	public static String changePrefixTo62(String phoneNo) {
		if (null == phoneNo) {
			return null;
		}
		
		if (StringUtils.startsWithIgnoreCase(phoneNo, "0")) {
			return "62".concat(phoneNo.substring(1));
		}
		
		return phoneNo;
	}

	public static final String urlEncode(String data, Context context) {
		try {
			return URLEncoder.encode(data, StandardCharsets.UTF_8.toString());
		} catch (Exception e) {
			context.getLogger().error("Failed to URL encode: " + data);
			context.getLogger().error(ExceptionUtils.getStackTrace(e));
			return StringUtils.EMPTY;
		}
	}

	public static final String generateRandomCharacters(String pools, int length) {
		if (length < 1) {
			return null;
		}
		
		StringBuilder builder = new StringBuilder();
		for (int i = 0; i < length; i++) {
			int randomIndex = randGen.nextInt(pools.length());
			builder.append(pools.charAt(randomIndex));
		}
		return builder.toString();
	}
}
