package com.adins.esignhubjob.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.RegistrationLivenessResultDao;
import com.adins.esignhubjob.model.table.TrRegistrationLivenessResult;
import com.adins.util.Tools;

@Component
@Transactional
public class RegistrationLivenessResultDaoHbn extends BaseDaoHbn implements RegistrationLivenessResultDao {

    @Override
    public void insertRegistrationLivenessResult(TrRegistrationLivenessResult result) {
        result.setUsrCrt(Tools.maskData(result.getUsrCrt()));
        managerDAO.insert(result);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertRegistrationLivenessResultNewTran(TrRegistrationLivenessResult result) {
        result.setUsrCrt(Tools.maskData(result.getUsrCrt()));
        managerDAO.insert(result);
    }
    
}
