package com.adins.esignhubjob.model.webservice.vida;

import java.util.List;

import com.adins.esignhubjob.model.custom.vida.VidaDeviceBean;
import com.adins.esignhubjob.model.custom.vida.VidaRequestInfoBean;
import com.adins.esignhubjob.model.custom.vida.VidaSigningInfoBean;
import com.adins.esignhubjob.model.custom.vida.VidaUserBean;

public class VidaSignRequest {
    private String partnerTrxId;
    private VidaUserBean user;
    private VidaRequestInfoBean requestInfo;
    private VidaDeviceBean device;
    private List<VidaSigningInfoBean> signingInfo;

    public VidaSignRequest() {
        
    }

    public VidaSignRequest(VidaSignRequest request) {
        this.partnerTrxId = request.partnerTrxId;
        this.user = request.user;
        this.requestInfo = request.requestInfo;
        this.device = request.device;
        this.signingInfo = request.signingInfo;
    }
    
    public static VidaSignRequest newInstance(VidaSignRequest request) {
        return new VidaSignRequest(request);
    }

    public String getPartnerTrxId() {
        return this.partnerTrxId;
    }

    public void setPartnerTrxId(String partnerTrxId) {
        this.partnerTrxId = partnerTrxId;
    }

    public VidaUserBean getUser() {
        return this.user;
    }

    public void setUser(VidaUserBean user) {
        this.user = user;
    }

    public VidaRequestInfoBean getRequestInfo() {
        return this.requestInfo;
    }

    public void setRequestInfo(VidaRequestInfoBean requestInfo) {
        this.requestInfo = requestInfo;
    }

    public VidaDeviceBean getDevice() {
        return this.device;
    }

    public void setDevice(VidaDeviceBean device) {
        this.device = device;
    }

    public List<VidaSigningInfoBean> getSigningInfo() {
        return this.signingInfo;
    }

    public void setSigningInfo(List<VidaSigningInfoBean> signingInfo) {
        this.signingInfo = signingInfo;
    }

}
