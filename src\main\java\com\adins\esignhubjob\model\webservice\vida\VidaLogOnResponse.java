package com.adins.esignhubjob.model.webservice.vida;

import com.google.gson.annotations.SerializedName;

public class VidaLogOnResponse {
    @SerializedName("error_description") private String errorDescription;
    @SerializedName("access_token") private String accessToken;
    @SerializedName("expires_in") private int expiresIn;
    @SerializedName("refresh_expires_in") private int refreshExpiresIn;
    @SerializedName("id_token") private String idToken;
    @SerializedName("token_type") private String tokenType;
    @SerializedName("not-before-policy") private String notBeforePolicy;
    private String scope;

    public String getErrorDescription() {
        return errorDescription;
    }
    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }
    public String getAccessToken() {
        return accessToken;
    }
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    public int getExpiresIn() {
        return expiresIn;
    }
    public void setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
    }
    public int getRefreshExpiresIn() {
        return refreshExpiresIn;
    }
    public void setRefreshExpiresIn(int refreshExpiresIn) {
        this.refreshExpiresIn = refreshExpiresIn;
    }
    public String getIdToken() {
        return idToken;
    }
    public void setIdToken(String idToken) {
        this.idToken = idToken;
    }
    public String getTokenType() {
        return tokenType;
    }
    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }
    public String getNotBeforePolicy() {
        return notBeforePolicy;
    }
    public void setNotBeforePolicy(String notBeforePolicy) {
        this.notBeforePolicy = notBeforePolicy;
    }
    public String getScope() {
        return scope;
    }
    public void setScope(String scope) {
        this.scope = scope;
    }
    
}
