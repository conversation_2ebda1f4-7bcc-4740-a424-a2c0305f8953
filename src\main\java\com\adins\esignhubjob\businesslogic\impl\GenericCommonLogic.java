package com.adins.esignhubjob.businesslogic.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.AmGlobalKey;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.CommonLogic;
import com.adins.util.AesEncryptionUtils;

@Component
@Transactional(readOnly = true)
public class GenericCommonLogic extends BaseLogic implements CommonLogic {

    @Override
    public String encryptMessageToString(String data) {
        String key = getAesEncryptionKey();
        return encryptToString(data, key);
    }

    private String getAesEncryptionKey() {
		return daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GS_AES_KEY);
	}

    private String encryptToString(String data, String key) {
        return AesEncryptionUtils.encrypt(data, key);
    }
    
}
