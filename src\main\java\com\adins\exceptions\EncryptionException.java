package com.adins.exceptions;

import java.util.Collections;

import com.adins.framework.exception.AdInsException;

public class EncryptionException extends AdInsException {
    public EncryptionException() {
        super(Collections.<String, String> emptyMap());
    }

    public EncryptionException(String message){
        super(message);
    }

 

    public EncryptionException(String message, Throwable e){
        super(message, e);
    }

    public int getErrorCode() {
        return 9999;
    }
}