package com.adins.esignhubjob.model.custom.vida;

public class VidaSigningInfoBean {
    private String pdfFile;
    private boolean qrEnable;
    private VidaSignAppearanceBean appearance;  
    private String pageNo;
    private String xPoint;
    private String yPoint;
    private String height;
    private String width;

    public String getPdfFile() {
        return this.pdfFile;
    }

    public void setPdfFile(String pdfFile) {
        this.pdfFile = pdfFile;
    }

    public boolean isQrEnable() {
        return this.qrEnable;
    }

    public boolean getQrEnable() {
        return this.qrEnable;
    }

    public void setQrEnable(boolean qrEnable) {
        this.qrEnable = qrEnable;
    }

    public VidaSignAppearanceBean getAppearance() {
        return this.appearance;
    }

    public void setAppearance(VidaSignAppearanceBean appearance) {
        this.appearance = appearance;
    }

    public String getPageNo() {
        return this.pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getXPoint() {
        return this.xPoint;
    }

    public void setXPoint(String xPoint) {
        this.xPoint = xPoint;
    }

    public String getYPoint() {
        return this.yPoint;
    }

    public void setYPoint(String yPoint) {
        this.yPoint = yPoint;
    }

    public String getHeight() {
        return this.height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getWidth() {
        return this.width;
    }

    public void setWidth(String width) {
        this.width = width;
    }
    

}
