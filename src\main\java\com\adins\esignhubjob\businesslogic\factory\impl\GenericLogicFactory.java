package com.adins.esignhubjob.businesslogic.factory.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esignhubjob.businesslogic.api.CallbackLogic;
import com.adins.esignhubjob.businesslogic.api.CommonLogic;
import com.adins.esignhubjob.businesslogic.api.CommonStampingLogic;
import com.adins.esignhubjob.businesslogic.api.EmailSenderLogic;
import com.adins.esignhubjob.businesslogic.api.MessageTemplateLogic;
import com.adins.esignhubjob.businesslogic.api.NotificationSenderLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.SignImageLogic;
import com.adins.esignhubjob.businesslogic.api.SigningProcessAuditTrailLogic;
import com.adins.esignhubjob.businesslogic.api.TenantLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.AliyunOssCloudStorageLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.BalanceValidatorLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SchedulerLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SmsJatisLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.SmsVfirstLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.DocumentLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.EmateraiPajakkuLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.PrivyLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.VidaLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.WhatsAppJatisLogic;
import com.adins.esignhubjob.businesslogic.factory.api.LogicFactory;

@Component
public class GenericLogicFactory implements LogicFactory {

    @Autowired private AliyunOssCloudStorageLogic cloudStorageLogic;
    @Autowired private CallbackLogic callbackLogic;
    @Autowired private CommonStampingLogic commonStampingLogic;
    @Autowired private DigisignLogic digisignLogic;
    @Autowired private DocumentLogic documentLogic;
    @Autowired private EmailSenderLogic emailSenderLogic;
    @Autowired private EmateraiPajakkuLogic emateraiPajakkuLogic;
    @Autowired private PrivyLogic privyLogic;
    @Autowired private PrivyGeneralLogic privyGeneralLogic;
    @Autowired private SchedulerLogic schedulerLogic;
    @Autowired private SignImageLogic signImageLogic;
    @Autowired private VidaLogic vidaLogic;
    @Autowired private MessageTemplateLogic messageTemplateLogic;
    @Autowired private TenantLogic tenantLogic;
    @Autowired private SmsJatisLogic smsJatisLogic;
    @Autowired private SmsVfirstLogic smsVfirstLogic;
    @Autowired private PersonalDataEncryptionLogic personalDataEncryptionLogic;
    @Autowired private WhatsAppJatisLogic whatsAppJatisLogic;
    @Autowired private CommonLogic commonLogic;
    @Autowired private NotificationSenderLogic notificationSenderLogic;
    @Autowired private WhatsAppHalosisLogic whatsAppHalosisLogic;
    @Autowired private SigningProcessAuditTrailLogic signingProcessAuditTrailLogic;
    @Autowired private BalanceValidatorLogic balanceValidatorLogic;

    @Override
    public AliyunOssCloudStorageLogic getAliyunOssCloudStorageLogic() {
        return cloudStorageLogic;
    }

    @Override
    public DigisignLogic getDigisignLogic() {
        return digisignLogic;
    }

    @Override
    public VidaLogic getVidaLogic() {
        return vidaLogic;
    }

    @Override
    public SchedulerLogic getSchedulerLogic() {
        return schedulerLogic;
    }

    @Override
    public SignImageLogic getSignImageLogic() {
        return signImageLogic;
    }

    @Override
    public DocumentLogic getDocumentLogic() {
        return documentLogic;
    }

    @Override
    public EmateraiPajakkuLogic getEmateraiPajakkuLogic() {
        return emateraiPajakkuLogic;
    }

    @Override
    public PrivyLogic getPrivyLogic() {
        return privyLogic;
    }

    @Override
    public CallbackLogic getCallbackLogic() {
        return callbackLogic;
    }

    @Override
    public PrivyGeneralLogic getPrivyGeneralLogic() {
        return privyGeneralLogic;
    }

    @Override
    public EmailSenderLogic getEmailSenderLogic() {
        return emailSenderLogic;
    }

    @Override
    public CommonStampingLogic getCommonStampingLogic() {
        return commonStampingLogic;
    }

    @Override
    public MessageTemplateLogic getMessageTemplateLogic() {
        return messageTemplateLogic;
    }

    @Override
    public TenantLogic getTenantLogic() {
        return tenantLogic;
    }

    @Override
    public SmsJatisLogic getSmsJatisLogic() {
        return smsJatisLogic;
    }

    @Override
    public SmsVfirstLogic getSmsVfirstLogic() {
        return smsVfirstLogic;
    }

    @Override
    public PersonalDataEncryptionLogic getPersonalDataEncryptionLogic() {
        return personalDataEncryptionLogic;
    }

    @Override
    public WhatsAppJatisLogic getWhatsAppJatisLogic() {
        return whatsAppJatisLogic;
    }

    @Override
    public CommonLogic getCommonLogic() {
        return commonLogic;
    }

    @Override
    public NotificationSenderLogic getNotificationSenderLogic() {
        return notificationSenderLogic;
    }

    @Override
    public WhatsAppHalosisLogic getWhatsAppHalosisLogic() {
        return whatsAppHalosisLogic;
    }

    @Override
    public SigningProcessAuditTrailLogic getSigningProcessAuditTrailLogic() {
        return signingProcessAuditTrailLogic;
    }

    @Override
    public BalanceValidatorLogic getBalanceValidatorLogic() {
        return balanceValidatorLogic;
    }
}
