package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class Api<PERSON>eyException extends AdInsException {

    public ApiKeyException(String message) {
       super(message);
    }

    public ApiKeyException(String message, Throwable ex) {
		super(message, ex);
	}

    @Override
    public int getErrorCode() {
        return StatusCode.INVALID_API_KEY;
    }
}
