package com.adins.util;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

public class IOUtils {
	private IOUtils() {}

	public static final int EOF = -1;
	
	public static String toString(final InputStream input) throws IOException {
		BufferedReader br = new BufferedReader(new InputStreamReader(input));
		
		StringBuilder sb = new StringBuilder();
		String read;
		while ((read=br.readLine()) != null) {
		    sb.append(read);
		}
		br.close();
		
		return sb.toString();
	}
	
	/** toByteArray function copied from Apache Commons IO IOUtils. **/
	public static byte[] toByteArray(final InputStream input) throws IOException {
        try (final ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            copy(input, output);
            return output.toByteArray();
        }
    }
	
	public static int copy(final InputStream input, final OutputStream output) throws IOException {
        final long count = copyLarge(input, output);
        if (count > Integer.MAX_VALUE) {
            return -1;
        }
        return (int) count;
    }
	
    public static long copyLarge(final InputStream input, final OutputStream output)
            throws IOException {
        return copy(input, output, 8192);
    }
    
    public static long copy(final InputStream input, final OutputStream output, final int bufferSize)
            throws IOException {
        return copyLarge(input, output, new byte[bufferSize]);
    }
    
    public static long copyLarge(final InputStream input, final OutputStream output, final byte[] buffer)
            throws IOException {
        long count = 0;
        if (input != null) {
            int n;
            while (EOF != (n = input.read(buffer))) {
                output.write(buffer, 0, n);
                count += n;
            }
        }
        return count;
    }
}
