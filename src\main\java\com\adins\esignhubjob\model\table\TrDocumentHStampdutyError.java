package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_document_h_stampduty_error")
public class TrDocumentHStampdutyError extends CreatableAndUpdatableEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public static final String ID_DOCUMENT_H_STAMPDUTY_ERROR_HBM = "idDocumentHStampdutyError";

	private long idDocumentHStampdutyError;
	private TrDocumentH trDocumentH;
	private TrDocumentD trDocumentD;
	private Short errorCount;
	private String errorLocation;
	private String errorLocationDetail;
	private String errorMessage;
	private String isEmailSent;
	private Short retryCount;

	public TrDocumentHStampdutyError() {
	}

	public TrDocumentHStampdutyError(long idDocumentHStampdutyError, TrDocumentH trDocumentH, TrDocumentD trDocumentD,
			Short errorCount, String errorLocation, String errorLocationDetail, String errorMessage,
			String isEmailSent, String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd) {
		this.idDocumentHStampdutyError = idDocumentHStampdutyError;
		this.trDocumentH = trDocumentH;
		this.trDocumentD = trDocumentD;
		this.errorCount = errorCount;
		this.errorLocation = errorLocation;
		this.errorLocationDetail = errorLocationDetail;
		this.errorMessage = errorMessage;
		this.isEmailSent = isEmailSent;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_document_h_stampduty_error", unique = true, nullable = false)
	public long getIdDocumentHStampdutyError() {
		return idDocumentHStampdutyError;
	}

	public void setIdDocumentHStampdutyError(long idDocumentHStampdutyError) {
		this.idDocumentHStampdutyError = idDocumentHStampdutyError;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_h")
	public TrDocumentH getTrDocumentH() {
		return trDocumentH;
	}

	public void setTrDocumentH(TrDocumentH trDocumentH) {
		this.trDocumentH = trDocumentH;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return trDocumentD;
	}

	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}

	@Column(name = "error_count")
	public Short getErrorCount() {
		return errorCount;
	}

	public void setErrorCount(Short errorCount) {
		this.errorCount = errorCount;
	}

	@Column(name = "error_location", length = 50)
	public String getErrorLocation() {
		return errorLocation;
	}

	public void setErrorLocation(String errorLocation) {
		this.errorLocation = errorLocation;
	}

	@Column(name = "error_location_detail", length = 50)
	public String getErrorLocationDetail() {
		return errorLocationDetail;
	}

	public void setErrorLocationDetail(String errorLocationDetail) {
		this.errorLocationDetail = errorLocationDetail;
	}

	@Column(name = "error_message", length = 1000)
	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	@Column(name = "is_email_sent", length = 1)
	public String getIsEmailSent() {
		return isEmailSent;
	}

	public void setIsEmailSent(String isEmailSent) {
		this.isEmailSent = isEmailSent;
	}

	@Column(name = "retry_count")
	public Short getRetryCount() {
		return retryCount;
	}

	public void setRetryCount(Short retryCount) {
		this.retryCount = retryCount;
	}
}
