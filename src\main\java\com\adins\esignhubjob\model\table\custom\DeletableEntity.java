package com.adins.esignhubjob.model.table.custom;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class DeletableEntity extends CreatableAndUpdatableEntity {
    public static final String IS_DELETED_HBM = "isDeleted";
    protected String isDeleted;

    @Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return this.isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
}
