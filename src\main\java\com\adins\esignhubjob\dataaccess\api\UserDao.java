package com.adins.esignhubjob.dataaccess.api;

import java.util.List;

import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.PersonalDataBean;
import com.aliyun.fc.runtime.Context;

public interface UserDao {

    // am_msuser
    List<AmMsuser> getNonDormantUsers();

    void insertUser(AmMsuser user);
    void insertUserNewTran(AmMsuser user);

    AmMsuser getUserByPhone(String phone);
    AmMsuser getUserByIdNo(String idNo);
    AmMsuser getUserByIdNoNewTran(String idNo);
    AmMsuser getUserByEmailNewTran(String email);

    AmMsuser getUserByIdMsUser(long idMsuser);

    // Get id_ms_user that is registered in other vendor
	Long getIdMsUserRegisteredInOtherVendorByEmail(String email, MsVendor vendor);
	Long getIdMsUserRegisteredInOtherVendorByPhone(String phone, MsVendor vendor);
	Long getIdMsUserRegisteredInOtherVendorByNik(String nik, MsVendor vendor);

    void updateUserNewTran(AmMsuser user);

    // am_user_personal_data
    void insertUserPersonalData(PersonalDataBean personalDataBean, Context context);
    void insertUserPersonalDataNewTrx(PersonalDataBean personalDataBean, Context context);

    void updateUserPersonalData(PersonalDataBean personalDataBean, Context context);
    void updateUserPersonalDataNewTrx(PersonalDataBean personalDataBean, Context context);

    PersonalDataBean getUserDataByIdMsUser(long idMsUser, boolean getPhoto, Context context);
	PersonalDataBean getUserDataByIdMsUserNewTrx(long idMsUser, boolean getPhoto, Context context);

}
