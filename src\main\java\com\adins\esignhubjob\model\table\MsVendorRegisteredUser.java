package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "ms_vendor_registered_user")
public class MsVendorRegisteredUser extends ActivatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	
	public static final String AM_MS_USER_HBM = "amMsuser";
	public static final String MS_VENDOR_HBM = "msVendor";
	public static final String SIGNER_REGISTERED_EMAIL_HBM = "signerRegisteredEmail";
	public static final String HASHED_SIGNER_REGISTERED_PHONE = "hashedSignerRegisteredPhone";

	private long idMsVendorRegisteredUser;
	private AmMsuser amMsuser;
	private MsVendor msVendor;
	private MsEmailHosting msEmailHosting;
	private String signerRegisteredEmail;
	private String vendorUserAutosignKey;
	private Date activatedDate;
	private Date certExpiredDate;
	private String isRegistered;
	private String hashedSignerRegisteredPhone;
	private byte[] phoneBytea;
	private String emailService;
	private String vendorUserAutosignCvv;
	private String vendorRegistrationId;
	private String poaId;
	private String vendorAccessToken;
	private Date certPoaExpiredDate;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_vendor_registered_user", unique = true, nullable = false)
	public long getIdMsVendorRegisteredUser() {
		return this.idMsVendorRegisteredUser;
	}

	public void setIdMsVendorRegisteredUser(long idMsVendorRegisteredUser) {
		this.idMsVendorRegisteredUser = idMsVendorRegisteredUser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return this.msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_email_hosting")
	public MsEmailHosting getMsEmailHosting() {
		return msEmailHosting;
	}

	public void setMsEmailHosting(MsEmailHosting msEmailHosting) {
		this.msEmailHosting = msEmailHosting;
	}

	@Column(name = "signer_registered_email", length = 80)
	public String getSignerRegisteredEmail() {
		return this.signerRegisteredEmail;
	}

	public void setSignerRegisteredEmail(String signerRegisteredEmail) {
		this.signerRegisteredEmail = signerRegisteredEmail;
	}

	@Column(name = "vendor_user_autosign_key", length = 50)
	public String getVendorUserAutosignKey() {
		return this.vendorUserAutosignKey;
	}

	public void setVendorUserAutosignKey(String vendorUserAutosignKey) {
		this.vendorUserAutosignKey = vendorUserAutosignKey;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "activated_date", length = 29)
	public Date getActivatedDate() {
		return this.activatedDate;
	}

	public void setActivatedDate(Date activatedDate) {
		this.activatedDate = activatedDate;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "cert_expired_date", length = 29)
	public Date getCertExpiredDate() {
		return this.certExpiredDate;
	}

	public void setCertExpiredDate(Date certExpiredDate) {
		this.certExpiredDate = certExpiredDate;
	}
	
	@Column(name = "is_registered", length = 1)
	public String getIsRegistered() {
		return isRegistered;
	}

	public void setIsRegistered(String isRegistered) {
		this.isRegistered = isRegistered;
	}

	@Column(name = "hashed_signer_registered_phone", length = 200)
	public String getHashedSignerRegisteredPhone() {
		return hashedSignerRegisteredPhone;
	}

	public void setHashedSignerRegisteredPhone(String hashedSignerRegisteredPhone) {
		this.hashedSignerRegisteredPhone = hashedSignerRegisteredPhone;
	}

	@Column(name = "phone_bytea")
	public byte[] getPhoneBytea() {
		return phoneBytea;
	}

	public void setPhoneBytea(byte[] phoneBytea) {
		this.phoneBytea = phoneBytea;
	}

	@Column(name = "email_service", length = 1)
	public String getEmailService() {
		return emailService;
	}

	public void setEmailService(String emailService) {
		this.emailService = emailService;
	}

	@Column(name = "vendor_user_autosign_cvv", length = 3)
	public String getVendorUserAutosignCvv() {
		return vendorUserAutosignCvv;
	}

	public void setVendorUserAutosignCvv(String vendorUserAutosignCvv) {
		this.vendorUserAutosignCvv = vendorUserAutosignCvv;
	}

	@Column(name = "vendor_registration_id", length = 50)
	public String getVendorRegistrationId() {
		return vendorRegistrationId;
	}

	public void setVendorRegistrationId(String vendorRegistrationId) {
		this.vendorRegistrationId = vendorRegistrationId;
	}

	@Column(name = "poa_id", length = 40)
	public String getPoaId() {
		return poaId;
	}

	public void setPoaId(String poaId) {
		this.poaId = poaId;
	}

	@Column(name = "vendor_access_token", length = 1000)
	public String getVendorAccessToken() {
		return vendorAccessToken;
	}

	public void setVendorAccessToken(String vendorAccessToken) {
		this.vendorAccessToken = vendorAccessToken;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "cert_poa_expired_date", length = 29)
	public Date getCertPoaExpiredDate() {
		return certPoaExpiredDate;
	}

	public void setCertPoaExpiredDate(Date certPoaExpiredDate) {
		this.certPoaExpiredDate = certPoaExpiredDate;
	}
	
	
}
