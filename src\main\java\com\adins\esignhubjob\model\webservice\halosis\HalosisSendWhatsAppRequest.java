package com.adins.esignhubjob.model.webservice.halosis;

import com.adins.esignhubjob.model.custom.halosis.HalosisWhatsAppTemplate;
import com.google.gson.annotations.SerializedName;

public class HalosisSendWhatsAppRequest {
    @SerializedName("messaging_product") private String messagingProduct;
	@SerializedName("recipient_type") private String recipientType;
	private String to;
	private String type;
	private HalosisWhatsAppTemplate template;
	
	public String getMessagingProduct() {
		return messagingProduct;
	}
	public void setMessagingProduct(String messagingProduct) {
		this.messagingProduct = messagingProduct;
	}
	public String getRecipientType() {
		return recipientType;
	}
	public void setRecipientType(String recipientType) {
		this.recipientType = recipientType;
	}
	public String getTo() {
		return to;
	}
	public void setTo(String to) {
		this.to = to;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public HalosisWhatsAppTemplate getTemplate() {
		return template;
	}
	public void setTemplate(HalosisWhatsAppTemplate template) {
		this.template = template;
	}
}
