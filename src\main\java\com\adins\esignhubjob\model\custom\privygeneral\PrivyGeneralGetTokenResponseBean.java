package com.adins.esignhubjob.model.custom.privygeneral;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralGetTokenResponseBean {
    
    @SerializedName("access_token") private String accessToken;
	@SerializedName("token_type") private String tokenType;
	@SerializedName("expires_in") private Long expiresIn;
	
	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	public String getTokenType() {
		return tokenType;
	}
	public void setTokenType(String tokenType) {
		this.tokenType = tokenType;
	}
	public Long getExpiresIn() {
		return expiresIn;
	}
	public void setExpiresIn(Long expiresIn) {
		this.expiresIn = expiresIn;
	}
}
