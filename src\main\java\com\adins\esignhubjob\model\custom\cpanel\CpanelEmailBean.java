package com.adins.esignhubjob.model.custom.cpanel;

public class CpanelEmailBean {
    private Long ctime;
    private Long mode;
    private Long mtime;
    private String absdir;
    private String nicemode;
    private String path;
    private Long gid;
    private String type;
    private Long uid;
    private Integer exists;
    private String size;
    private String file;
    private String humansize;
    private String fullpath;

    public Long getCtime() {
        return this.ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getMode() {
        return this.mode;
    }

    public void setMode(Long mode) {
        this.mode = mode;
    }

    public Long getMtime() {
        return this.mtime;
    }

    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    public String getAbsdir() {
        return this.absdir;
    }

    public void setAbsdir(String absdir) {
        this.absdir = absdir;
    }

    public String getNicemode() {
        return this.nicemode;
    }

    public void setNicemode(String nicemode) {
        this.nicemode = nicemode;
    }

    public String getPath() {
        return this.path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Long getGid() {
        return this.gid;
    }

    public void setGid(Long gid) {
        this.gid = gid;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getUid() {
        return this.uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getExists() {
        return this.exists;
    }

    public void setExists(Integer exists) {
        this.exists = exists;
    }

    public String getSize() {
        return this.size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getFile() {
        return this.file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public String getHumansize() {
        return this.humansize;
    }

    public void setHumansize(String humansize) {
        this.humansize = humansize;
    }

    public String getFullpath() {
        return this.fullpath;
    }

    public void setFullpath(String fullpath) {
        this.fullpath = fullpath;
    }

}
