package com.adins.esignhubjob;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.stereotype.Component;

import com.adins.esignhubjob.businesslogic.factory.api.LogicFactory;
import com.adins.esignhubjob.dataaccess.factory.api.DaoFactory;
import com.adins.esignhubjob.factory.api.ApplicationBean;
import com.aliyun.fc.runtime.Context;
import com.aliyun.fc.runtime.FunctionComputeLogger;
import com.aliyun.fc.runtime.FunctionInitializer;
import com.aliyun.fc.runtime.StreamRequestHandler;
import com.aliyun.fc.runtime.FunctionComputeLogger.Level;
import com.google.gson.Gson;

@Component
public abstract class BaseJobHandler implements StreamRequestHandler, FunctionInitializer {

    protected FunctionComputeLogger logger = null;
    protected ClassPathXmlApplicationContext appContext = null;
    protected DaoFactory daoFactory = null;
    protected LogicFactory logicFactory = null;
    protected Gson gson = null;

    public abstract void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException;

    @Override
    public void initialize(Context context) throws IOException {
        try {
            long startTime = System.currentTimeMillis();

            this.logger = context.getLogger();
            
            // Load beans from xml
            appContext = new ClassPathXmlApplicationContext("application-context.xml");
            
            ApplicationBean appBeans = appContext.getBean(ApplicationBean.class);
            daoFactory = appBeans.getDaoFactory();
            logicFactory = appBeans.getLogicFactory();
            gson = appContext.getBean(Gson.class);
            
            this.logProcessDuration("Initialize function", startTime);
        } catch (Exception e) {
            String stackTrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stackTrace);
            throw e;
        }
        
    }

    @Override
    public void handleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        if ("1".equals(System.getenv("IS_DEBUG"))) {
            context.getLogger().setLogLevel(Level.DEBUG);
        } else {
            context.getLogger().setLogLevel(Level.INFO);
        }
        
        try {
            doHandleRequest(inputStream, outputStream, context);
        } catch (Exception e) {
            String stackTrace = ExceptionUtils.getStackTrace(e);
            logger.error(stackTrace);
        }
    }

    /**
	 * @param processLabel
	 * @param startTime Obtain startTime from System.currentTimeMillis()
	 */
	protected void logProcessDuration(String processLabel, long startTime) {
		long durationMs = System.currentTimeMillis() - startTime;
		this.logger.info(processLabel + " duration: " + durationMs + " ms");
	}
}