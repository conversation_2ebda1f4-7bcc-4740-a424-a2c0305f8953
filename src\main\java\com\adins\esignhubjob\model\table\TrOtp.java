package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_otp")
public class TrOtp extends CreatableAndUpdatableEntity implements Serializable{
    private static final long serialVersionUID = 1L;
	
	public static final String LOGIN_ID_HBM = "loginId";
	
	private long idOtp;
	private String loginId;
	private String otpCode;
    
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_otp", unique = true, nullable = false)
	public long getIdOtp() {
		return this.idOtp;
	}

	public void setIdOtp(long idOtp) {
		this.idOtp = idOtp;
	}

	@Column(name = "login_id", nullable = false, length = 80)
	public String getLoginId() {
		return this.loginId;
	}

	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	@Column(name = "otp_code", length = 10)
	public String getOtpCode() {
		return this.otpCode;
	}

	public void setOtpCode(String otpCode) {
		this.otpCode = otpCode;
	}
}
