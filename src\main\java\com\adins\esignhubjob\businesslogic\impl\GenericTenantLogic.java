package com.adins.esignhubjob.businesslogic.impl;

import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.constants.enums.NotificationSendingPoint;
import com.adins.constants.enums.NotificationType;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.TenantLogic;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsNotificationtypeoftenant;
import com.adins.esignhubjob.model.table.MsTenant;

@Component
public class GenericTenantLogic extends BaseLogic implements TenantLogic {

    @Override
    public NotificationType getNotificationType(MsTenant tenant, NotificationSendingPoint sendingPoint, String emailService) {
        MsNotificationtypeoftenant notifType = daoFactory.getNotificationtypeoftenantDao().getNotificationTypeNewTrx(tenant, sendingPoint.toString());
		boolean useEmailService = "1".equals(emailService);
		if (null == notifType) {
			MsLov lovWaGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_WA_GATEWAY, Constants.CODE_LOV_WA_GATEWAY_HALOSIS);
			boolean useWaMessage = "1".equals(tenant.getUseWaMessage());
			boolean mustUseWaFirst = "1".equals(tenant.getMustUseWaFirst());
			boolean sendOtpByEmail = "1".equals(tenant.getSentOtpByEmail());
			boolean mustUseSmsFirst = false;
			return getNotificationType(sendingPoint, useEmailService, useWaMessage, mustUseWaFirst, sendOtpByEmail, mustUseSmsFirst, tenant.getLovSmsGateway(), lovWaGateway);
		}
		
		boolean useWaMessage = "1".equals(notifType.getUseWaMessage());
		boolean mustUseWaFirst = "1".equals(notifType.getMustUseWaFirst());
		boolean sendOtpByEmail = "1".equals(notifType.getSendOtpByEmail());
		boolean mustUseSmsFirst = "1".equals(notifType.getMustUseSmsFirst());
		return getNotificationType(sendingPoint, useEmailService, useWaMessage, mustUseWaFirst, sendOtpByEmail, mustUseSmsFirst, notifType.getLovSmsGateway(), notifType.getLovWaGateway());
    }

    /**
	 * Priority:
	 * 1. mustUseSmsFirst
	 * 2. mustUseWaFirst
	 */
	private NotificationType getNotificationType(NotificationSendingPoint sendingPoint, boolean useEmailService, boolean useWaMessage, boolean mustUseWaFirst, boolean sendOtpByEmail, boolean mustUseSmsFirst, MsLov lovSmsGateway, MsLov lovWaGateway) {
		if (sendingPoint.isOtp()) {
			if (mustUseSmsFirst) {
				return getSmsNotificationType(lovSmsGateway);
			}
			
			if (mustUseWaFirst) {
				return getWaNotificationType(lovWaGateway);
			}
			
			if (sendingPoint != NotificationSendingPoint.OTP_ACT && sendOtpByEmail && !useEmailService) {
				return NotificationType.EMAIL;
			}
			
			if (useWaMessage) {
				return getWaNotificationType(lovWaGateway);
			}
			
			return getSmsNotificationType(lovSmsGateway);
		}
		
		// Non OTP notification
		if (mustUseSmsFirst) {
			return getSmsNotificationType(lovSmsGateway);
		}
		
		if (mustUseWaFirst) {
			return getWaNotificationType(lovWaGateway);
		}
		
		if (!useEmailService) {
			return NotificationType.EMAIL;
		}
		
		if (useWaMessage) {
			return getWaNotificationType(lovWaGateway);
		}
		
		return getSmsNotificationType(lovSmsGateway);
	}

    private NotificationType getSmsNotificationType(MsLov lovSmsGateway) {
		if (null == lovSmsGateway || Constants.CODE_LOV_SMS_GATEWAY_VFIRST.equals(lovSmsGateway.getCode())) {
			return NotificationType.SMS_VFIRST;
		}
		
		if (Constants.CODE_LOV_SMS_GATEWAY_JATIS.equals(lovSmsGateway.getCode())) {
			return NotificationType.SMS_JATIS;
		}
		
		return NotificationType.SMS_VFIRST;
	}

	private NotificationType getWaNotificationType(MsLov lovWaGateway) {
		if (null == lovWaGateway || Constants.CODE_LOV_WA_GATEWAY_HALOSIS.equals(lovWaGateway.getCode())) {
			return NotificationType.WHATSAPP_HALOSIS;
		}
		
		if (Constants.CODE_LOV_WA_GATEWAY_JATIS.equals(lovWaGateway.getCode())) {
			return NotificationType.WHATSAPP;
		}
		
		return NotificationType.WHATSAPP_HALOSIS;
	}
    
}
