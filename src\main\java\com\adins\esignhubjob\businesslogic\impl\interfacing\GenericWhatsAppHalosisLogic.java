package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.custom.halosis.HalosisCredentialBean;
import com.adins.esignhubjob.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esignhubjob.model.custom.halosis.HalosisTokenBean;
import com.adins.esignhubjob.model.custom.halosis.HalosisWhatsAppTemplate;
import com.adins.esignhubjob.model.custom.halosis.HalosisWhatsAppTemplateComponent;
import com.adins.esignhubjob.model.custom.halosis.HalosisWhatsAppTemplateComponentParameter;
import com.adins.esignhubjob.model.custom.halosis.HalosisWhatsAppTemplateLanguage;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrMessageDeliveryReport;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;
import com.adins.esignhubjob.model.webservice.halosis.HalosisAccessTokenRequest;
import com.adins.esignhubjob.model.webservice.halosis.HalosisAccessTokenResponse;
import com.adins.esignhubjob.model.webservice.halosis.HalosisLoginRequest;
import com.adins.esignhubjob.model.webservice.halosis.HalosisLoginResponse;
import com.adins.esignhubjob.model.webservice.halosis.HalosisSendWhatsAppRequest;
import com.adins.esignhubjob.model.webservice.halosis.HalosisSendWhatsAppResponse;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
public class GenericWhatsAppHalosisLogic extends BaseLogic implements WhatsAppHalosisLogic {

	@Autowired private PersonalDataEncryptionLogic encryptionLogic;

    @Override
    public void sendMessage(HalosisSendWhatsAppRequestBean request, Context context) {
        MsTenant tenant = request.getMsTenant();
		MsMsgTemplate template = request.getTemplate();
		String phone = request.getPhoneNumber();
		List<String> headerTexts = request.getHeaderTexts();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();


        HalosisCredentialBean credential = getTenantCredential(tenant, context);
        boolean isSuccessful = true;
		String vendorTrxNo = null;
        try {
			String token = getTenantAuthorizationToken(tenant, credential, context);
			HalosisSendWhatsAppResponse waResponse = callSendWhatsApp(tenant, template, phone, token, headerTexts, bodyTexts, buttonText, context);
			isSuccessful = "success".equals(waResponse.getStatus());
			context.getLogger().info("sending WA Halosis");
			vendorTrxNo = waResponse.getWamId();
		} catch (Exception e) {
			context.getLogger().error("Failed to send Halosis WhatsApp: " + e.getLocalizedMessage());
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
			isSuccessful = false;
		}

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_WA);
		MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_UWA);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getReservedTrxNo());
		mutation.setVendorTrxNo(vendorTrxNo);
		mutation.setTrxDate(new Date());
		mutation.setQty(isSuccessful ? -1 : 0);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setUsrCrt(context.getRequestId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = "Sending WhatsApp to " + phone;
		} else {
			notes = request.getNotes();
		}
		mutation.setNotes(notes);
		if (null != request.getAmMsuser()) {
			mutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			mutation.setTrDocumentH(request.getTrDocumentH());
			mutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			mutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			mutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			mutation.setRefNo(request.getRefNo());
		}
		daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
    }

	private boolean needtoCutBalance(MsTenant tenant, String phoneNumber, boolean isOtp) {
		if (isOtp) {
			return true;
		}
		
		TrMessageDeliveryReport report = daoFactory.getMessageDeliveryReportDao().getLatestWhatsAppMessageDeliveryReport(tenant, phoneNumber, "1");
		if (null == report) {
			return true;
		}
		
		Date lastSessionTime = report.getReportTime();
		Date currentTime = new Date();
		long oneDayMillis = 86_400_000L; // 24 * 60 * 60 * 1000
		
		// Cut balance only last session has passed 24 hours
		return (currentTime.getTime() - lastSessionTime.getTime()) >=  oneDayMillis;
	}

    private HalosisCredentialBean getTenantCredential(MsTenant tenant, Context context) {
        MsTenantSettings emailSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "HALOSIS_EMAIL");
		if (emailSettings == null) {
			return getApplicationCredential(tenant, context);
		}
		
		MsTenantSettings passwordSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "HALOSIS_PASSWORD");
		if (passwordSettings == null) {
			return getApplicationCredential(tenant, context);
		}
		
        context.getLogger().debug(String.format("Tenant %1$s, uses Halosis credential from tenant settings", tenant.getTenantCode()));
		return new HalosisCredentialBean(emailSettings.getSettingValue(), passwordSettings.getSettingValue());
    }

    private HalosisCredentialBean getApplicationCredential(MsTenant tenant, Context context) {
        context.getLogger().debug(String.format("Tenant %1$s, uses Halosis credential from environment variables", tenant.getTenantCode()));
		return new HalosisCredentialBean(System.getenv(Constants.ENV_VAR_HALOSIS_EMAIL), System.getenv(Constants.ENV_VAR_HALOSIS_PASSWORD));
	}

    private String getTenantAuthorizationToken(MsTenant tenant, HalosisCredentialBean credential, Context context) throws IOException {
		
        MsTenantSettings tokenValueSetting = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, Constants.LOV_CODE_TENANT_SETTING_HALOSIS_TOKEN_VALUE);
        MsTenantSettings tokenExpirySetting = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, Constants.LOV_CODE_TENANT_SETTING_HALOSIS_TOKEN_EXPIRY_DATE);
        
		if (null == tokenValueSetting || null == tokenExpirySetting) {
			HalosisTokenBean tokenBean = generateNewTenantToken(tenant, credential, context);

            MsLov lovTokenSetting = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TENANT_SETTING_TYPE, Constants.LOV_CODE_TENANT_SETTING_HALOSIS_TOKEN_VALUE);
            MsLov lovTokenExpirySetting = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TENANT_SETTING_TYPE, Constants.LOV_CODE_TENANT_SETTING_HALOSIS_TOKEN_EXPIRY_DATE);

            tokenValueSetting = new MsTenantSettings();
            tokenValueSetting.setMsTenant(tenant);
            tokenValueSetting.setLovSettingType(lovTokenSetting);
            tokenValueSetting.setSettingValue(tokenBean.getToken());
            tokenValueSetting.setUsrCrt(context.getRequestId());
            tokenValueSetting.setDtmCrt(new Date());
            daoFactory.getTenantSettingsDao().insertTenantSettingsNewTrx(tokenValueSetting);

            tokenExpirySetting = new MsTenantSettings();
            tokenExpirySetting.setMsTenant(tenant);
            tokenExpirySetting.setLovSettingType(lovTokenExpirySetting);
            tokenExpirySetting.setSettingValue(tokenBean.getExpiredDate());
            tokenExpirySetting.setUsrCrt(context.getRequestId());
            tokenExpirySetting.setDtmCrt(new Date());
            daoFactory.getTenantSettingsDao().insertTenantSettingsNewTrx(tokenExpirySetting);

            return tokenBean.getToken();
		}

        if (StringUtils.isBlank(tokenValueSetting.getSettingValue()) || StringUtils.isBlank(tokenExpirySetting.getSettingValue())) {
            HalosisTokenBean tokenBean = generateNewTenantToken(tenant, credential, context);

            tokenValueSetting.setSettingValue(tokenBean.getToken());
            tokenValueSetting.setUsrUpd(context.getRequestId());
            tokenValueSetting.setDtmUpd(new Date());
            daoFactory.getTenantSettingsDao().updateTenantSettingsNewTrx(tokenValueSetting);

            tokenExpirySetting.setSettingValue(tokenBean.getExpiredDate());
            tokenExpirySetting.setUsrUpd(context.getRequestId());
            tokenExpirySetting.setDtmUpd(new Date());
            daoFactory.getTenantSettingsDao().updateTenantSettingsNewTrx(tokenExpirySetting);

            return tokenBean.getToken();
        }
		
		boolean isExpired = isTokenExpired(tokenExpirySetting.getSettingValue());
		if (isExpired) {
			HalosisTokenBean tokenBean = generateNewTenantToken(tenant, credential, context);

            tokenValueSetting.setSettingValue(tokenBean.getToken());
            tokenValueSetting.setUsrUpd(context.getRequestId());
            tokenValueSetting.setDtmUpd(new Date());
            daoFactory.getTenantSettingsDao().updateTenantSettingsNewTrx(tokenValueSetting);

            tokenExpirySetting.setSettingValue(tokenBean.getExpiredDate());
            tokenExpirySetting.setUsrUpd(context.getRequestId());
            tokenExpirySetting.setDtmUpd(new Date());
            daoFactory.getTenantSettingsDao().updateTenantSettingsNewTrx(tokenExpirySetting);

            return tokenBean.getToken();
		}
		
		context.getLogger().debug("Tenant " + tenant.getTenantCode() + ", using token from tenant setting");
		return tokenValueSetting.getSettingValue();
	}

    private boolean isTokenExpired(String tokenExpiryDateString) {
        if (StringUtils.isBlank(tokenExpiryDateString)) {
            return true;
        }

		Date expiredDate = Tools.formatStringToDate(tokenExpiryDateString, "dd MMM yyyy HH:mm:ss");
		
		Date expiredDateThreshold = DateUtils.addDays(expiredDate, -1);
		Date currentDate = new Date();
		
		return currentDate.getTime() >= expiredDateThreshold.getTime();
	}

    private HalosisTokenBean generateNewTenantToken(MsTenant tenant, HalosisCredentialBean credential, Context context) throws IOException {
        context.getLogger().debug("Tenant " + tenant.getTenantCode() + ", generating new token");
        HalosisLoginResponse loginResponse = loginHalosis(credential.getEmail(), credential.getPassword());
		String refreshToken = loginResponse.getRefreshToken();
		HalosisAccessTokenResponse accessTokenResponse = getAccessToken(refreshToken, context);
		
		HalosisTokenBean tokenBean = new HalosisTokenBean();
		tokenBean.setToken(accessTokenResponse.getLongLivedToken());
		
		// Expired date in "ddd, dd MMM yyyy HH:mm:ss" format, Convert to "dd MMM yyyy HH:mm:ss" format by removing the first 5 characters
		tokenBean.setExpiredDate(accessTokenResponse.getTokenExpiredAt().substring(5));
        return tokenBean;
    }

    private HalosisLoginResponse loginHalosis(String email, String password) throws IOException {
		
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		Headers headers = Headers.of(headerMap);
		
		HalosisLoginRequest request = new HalosisLoginRequest();
		request.setEmail(email);
		request.setPassword(password);
		String jsonRequest = gson.toJson(request);
		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));
		
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(System.getenv(Constants.ENV_VAR_HALOSIS_BASE_URL) + System.getenv(Constants.ENV_VAR_HALOSIS_LOGIN_URL))
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(60L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Response response = client.newCall(okHttpRequest).execute();
		
		String jsonResponse = response.body().string();
		return gson.fromJson(jsonResponse, HalosisLoginResponse.class);
	}

    private HalosisAccessTokenResponse getAccessToken(String refreshToken, Context context) throws IOException {
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		Headers headers = Headers.of(headerMap);
		
		HalosisAccessTokenRequest request = new HalosisAccessTokenRequest();
		request.setRefreshToken(refreshToken);
		String jsonRequest = gson.toJson(request);
		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));
		
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(System.getenv(Constants.ENV_VAR_HALOSIS_BASE_URL) + System.getenv(Constants.ENV_VAR_HALOSIS_ACCESS_TOKEN_URL))
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(60L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Response response = client.newCall(okHttpRequest).execute();
		
		String jsonResponse = response.body().string();
		context.getLogger().info("Get halosis access token response: " + jsonResponse);
		return gson.fromJson(jsonResponse, HalosisAccessTokenResponse.class);
		
	}

    private HalosisWhatsAppTemplateComponent prepareHeaderComponent(List<String> headerTexts) {
		List<HalosisWhatsAppTemplateComponentParameter> parameters = new ArrayList<>();
		for (String headerText : headerTexts) {
			HalosisWhatsAppTemplateComponentParameter param = new HalosisWhatsAppTemplateComponentParameter();
			param.setType("text");
			param.setText(headerText);
			parameters.add(param);
		}
		
		HalosisWhatsAppTemplateComponent component = new HalosisWhatsAppTemplateComponent();
		component.setType("header");
		component.setParameters(parameters);
		return component;
	}
	
	private HalosisWhatsAppTemplateComponent prepareBodyComponent(List<String> headerTexts) {
		List<HalosisWhatsAppTemplateComponentParameter> parameters = new ArrayList<>();
		for (String headerText : headerTexts) {
			HalosisWhatsAppTemplateComponentParameter param = new HalosisWhatsAppTemplateComponentParameter();
			param.setType("text");
			param.setText(headerText);
			parameters.add(param);
		}
		
		HalosisWhatsAppTemplateComponent component = new HalosisWhatsAppTemplateComponent();
		component.setType("body");
		component.setParameters(parameters);
		return component;
	}
	
	private HalosisWhatsAppTemplateComponent prepareButtonComponent(String buttonText) {
		HalosisWhatsAppTemplateComponentParameter param = new HalosisWhatsAppTemplateComponentParameter();
		param.setType("text");
		param.setText(buttonText);
		
		List<HalosisWhatsAppTemplateComponentParameter> parameters = new ArrayList<>();
		parameters.add(param);
		
		HalosisWhatsAppTemplateComponent component = new HalosisWhatsAppTemplateComponent();
		component.setType("button");
		component.setSubType("URL");
		component.setIndex("0");
		component.setParameters(parameters);
		return component;
	}
	
	private HalosisSendWhatsAppResponse callSendWhatsApp(MsTenant tenant, MsMsgTemplate template, String phone, String token, List<String> headerTexts, List<String> bodyTexts, String buttonText, Context context) throws IOException {
		
		// Prepare header
		Map<String, String> header = new HashMap<>();
		header.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.buildBearerToken(token));
		header.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		Headers headers = Headers.of(header);
		
		// Prepare body
		List<HalosisWhatsAppTemplateComponent> components = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(headerTexts)) {
			HalosisWhatsAppTemplateComponent headerComponent = prepareHeaderComponent(headerTexts);
			components.add(headerComponent);
		}
		
		if (CollectionUtils.isNotEmpty(bodyTexts)) {
			HalosisWhatsAppTemplateComponent bodyComponent = prepareBodyComponent(bodyTexts);
			components.add(bodyComponent);
		}
		
		if (StringUtils.isNotBlank(buttonText)) {
			HalosisWhatsAppTemplateComponent buttonComponent = prepareButtonComponent(buttonText);
			components.add(buttonComponent);
		}
		
		HalosisWhatsAppTemplateLanguage language = new HalosisWhatsAppTemplateLanguage();
		language.setCode("id");
		
		HalosisWhatsAppTemplate waTemplate = new HalosisWhatsAppTemplate();
		waTemplate.setName(template.getWaHalosisTemplateCode() + System.getenv(Constants.ENV_VAR_JATIS_WA_TEMPLATE_SUFFIX));
		waTemplate.setLanguage(language);
		waTemplate.setComponents(components);
		
		HalosisSendWhatsAppRequest request = new HalosisSendWhatsAppRequest();
		request.setMessagingProduct("whatsapp");
		request.setRecipientType("individual");
		request.setTo(Tools.changePrefixTo62(phone));
		request.setType("template");
		request.setTemplate(waTemplate);
		
		String jsonRequest = gson.toJson(request);
        context.getLogger().info(String.format("Tenant %1$s, send WhatsApp Halosis request: %2$s", tenant.getTenantCode(), jsonRequest));
		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));
		
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(System.getenv(Constants.ENV_VAR_HALOSIS_BASE_URL) + System.getenv(Constants.ENV_VAR_HALOSIS_SEND_WA_URL))
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(60L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Response response = client.newCall(okHttpRequest).execute();
		String jsonResponse = response.body().string();
        context.getLogger().info(String.format("Tenant %1$s, send WhatsApp Halosis response: %2$s", tenant.getTenantCode(), jsonResponse));
		return gson.fromJson(jsonResponse, HalosisSendWhatsAppResponse.class);
	}

	@Override
	public void sendMessage(HalosisSendWhatsAppRequestBean request, SigningProcessAuditTrailBean auditTrailBean, Context context) {
		MsTenant tenant = request.getMsTenant();
		MsMsgTemplate template = request.getTemplate();
		String phone = request.getPhoneNumber();
		List<String> headerTexts = request.getHeaderTexts();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();

        HalosisCredentialBean credential = getTenantCredential(tenant, context);
        boolean isSuccessful = true;
		String vendorTrxNo = null;
		String auditNotes = null;
        try {
			String token = getTenantAuthorizationToken(tenant, credential, context);
			HalosisSendWhatsAppResponse waResponse = callSendWhatsApp(tenant, template, phone, token, headerTexts, bodyTexts, buttonText, context);
			isSuccessful = "success".equals(waResponse.getStatus());
			vendorTrxNo = waResponse.getWamId();
			auditNotes = isSuccessful ? null : waResponse.getStatus();
		} catch (Exception e) {
			context.getLogger().error("Failed to send Halosis WhatsApp: " + e.getLocalizedMessage());
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
			isSuccessful = false;
			auditNotes = e.getLocalizedMessage();
		}

		insertAuditTrail(auditTrailBean, isSuccessful, auditNotes, context);

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_WA);
		MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_UWA);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getReservedTrxNo());
		mutation.setVendorTrxNo(vendorTrxNo);
		mutation.setTrxDate(new Date());
		mutation.setQty(isSuccessful ? -1 : 0);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setUsrCrt(context.getRequestId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = "Sending WhatsApp to " + phone;
		} else {
			notes = request.getNotes();
		}
		mutation.setNotes(notes);
		if (null != request.getAmMsuser()) {
			mutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			mutation.setTrDocumentH(request.getTrDocumentH());
			mutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			mutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			mutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			mutation.setRefNo(request.getRefNo());
		}
		daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);
	}

	private TrSigningProcessAuditTrail insertAuditTrail(SigningProcessAuditTrailBean auditTrailBean, boolean sendSuccess, String notes, Context context) {
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(encryptionLogic.encryptFromString(auditTrailBean.getPhone()));
		trail.setHashedPhoneNo(Tools.getHashedString(auditTrailBean.getPhone()));
		trail.setEmail(StringUtils.upperCase(auditTrailBean.getEmail()));
		trail.setAmMsUser(auditTrailBean.getUser());
		trail.setMsTenant(auditTrailBean.getTenant());
		trail.setMsVendor(auditTrailBean.getVendorPsre());
		trail.setNotificationMedia("WA");
		trail.setNotificationVendor(daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_WA_GATEWAY, Constants.CODE_LOV_WA_GATEWAY_HALOSIS).getDescription());
		trail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		trail.setLovProcessType(auditTrailBean.getLovProcessType());
		trail.setResultStatus(sendSuccess ? "1" : "0");
		trail.setNotes(notes);
		trail.setUsrCrt(context.getRequestId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

		if (CollectionUtils.isEmpty(auditTrailBean.getDocumentDs())) {
			return trail;
		}

		for (TrDocumentD documentD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail detail = new TrSigningProcessAuditTrailDetail();
			detail.setSigningProcessAuditTrail(trail);
			detail.setTrDocumentD(documentD);
			detail.setUsrCrt(context.getRequestId());
			detail.setDtmCrt(new Date());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTrx(detail);
		}

		return trail;
	}
    
}
