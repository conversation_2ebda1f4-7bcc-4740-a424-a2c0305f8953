package com.adins.esignhubjob.model.webservice.vida;

import com.google.gson.annotations.SerializedName;

public class VidaTokenResponse {
    private String error;
    @SerializedName("error_description") private String errorDescription;
    @SerializedName("access_token") private String accessToken;
    @SerializedName("expires_in") private int expiresIn;
    @SerializedName("refresh_expires_in") private int refreshExpiresIn;
    @SerializedName("refresh_token") private String refreshToken;
    @SerializedName("token_type") private String tokenType;
    @SerializedName("not-before-policy") private int notBeforePolicy;
    @SerializedName("session_state") private String sessionState;
    private String scope;

    public String getError() {
        return error;
    }
    public void setError(String error) {
        this.error = error;
    }
    public String getErrorDescription() {
        return errorDescription;
    }
    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }
    public String getAccessToken() {
        return accessToken;
    }
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    public int getExpiresIn() {
        return expiresIn;
    }
    public void setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
    }
    public int getRefreshExpiresIn() {
        return refreshExpiresIn;
    }
    public void setRefreshExpiresIn(int refreshExpiresIn) {
        this.refreshExpiresIn = refreshExpiresIn;
    }
    public String getRefreshToken() {
        return refreshToken;
    }
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }
    public String getTokenType() {
        return tokenType;
    }
    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }
    public int getNotBeforePolicy() {
        return notBeforePolicy;
    }
    public void setNotBeforePolicy(int notBeforePolicy) {
        this.notBeforePolicy = notBeforePolicy;
    }
    public String getSessionState() {
        return sessionState;
    }
    public void setSessionState(String sessionState) {
        this.sessionState = sessionState;
    }
    public String getScope() {
        return scope;
    }
    public void setScope(String scope) {
        this.scope = scope;
    }
}
