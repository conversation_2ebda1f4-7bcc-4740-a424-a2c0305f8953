package com.adins.esignhubjob.model.webservice.privygeneral;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralDocumentHistoryRequest {
    @SerializedName("reference_number") private String referenceNumber;
    @SerializedName("channel_id") private String channelId;
    @SerializedName("document_token") private String documentToken;
    private String info;

    public String getReferenceNumber() {
        return this.referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getChannelId() {
        return this.channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getDocumentToken() {
        return this.documentToken;
    }

    public void setDocumentToken(String documentToken) {
        this.documentToken = documentToken;
    }

    public String getInfo() {
        return this.info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

}
