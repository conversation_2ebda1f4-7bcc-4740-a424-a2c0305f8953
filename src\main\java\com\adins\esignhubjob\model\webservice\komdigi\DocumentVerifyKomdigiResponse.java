package com.adins.esignhubjob.model.webservice.komdigi;

import java.util.List;

import com.adins.esignhubjob.model.custom.komdigi.DocumentKomdigiDetailSign;
import com.adins.esignhubjob.model.custom.komdigi.DocumentKomdigiDetailStamp;
import com.google.gson.annotations.SerializedName;

public class DocumentVerifyKomdigiResponse {
    @SerializedName("agrmntNo") private String agrmntNo;
    @SerializedName("code") private int code;
    @SerializedName("detailSign") private List<DocumentKomdigiDetailSign> detailSign;
    @SerializedName("detailMeterai") private List<DocumentKomdigiDetailStamp> detailStamp;
    @SerializedName("documentId") private String documentId;
    @SerializedName("status") private String status;
    @SerializedName("statusMeterai") private String statusMeterai;
    @SerializedName("statusSign") private String statusSign;
    private String jsonResponse;

    public String getAgrmntNo() {
        return this.agrmntNo;
    }

    public void setAgrmntNo(String agrmntNo) {
        this.agrmntNo = agrmntNo;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public List<DocumentKomdigiDetailSign> getDetailSign() {
        return this.detailSign;
    }

    public void setDetailSign(List<DocumentKomdigiDetailSign> detailSign) {
        this.detailSign = detailSign;
    }

    public List<DocumentKomdigiDetailStamp> getDetailStamp() {
        return this.detailStamp;
    }

    public void setDetailStamp(List<DocumentKomdigiDetailStamp> detailStamp) {
        this.detailStamp = detailStamp;
    }

    public String getDocumentId() {
        return this.documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusMeterai() {
        return this.statusMeterai;
    }

    public void setStatusMeterai(String statusMeterai) {
        this.statusMeterai = statusMeterai;
    }

    public String getStatusSign() {
        return this.statusSign;
    }

    public void setStatusSign(String statusSign) {
        this.statusSign = statusSign;
    }

    public String getJsonResponse() {
        return this.jsonResponse;
    }

    public void setJsonResponse(String jsonResponse) {
        this.jsonResponse = jsonResponse;
    }

}
