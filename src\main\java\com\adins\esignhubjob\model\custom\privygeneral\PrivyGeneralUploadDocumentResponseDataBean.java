package com.adins.esignhubjob.model.custom.privygeneral;

import java.util.Map;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralUploadDocumentResponseDataBean {
	
	@SerializedName("reference_number") private String referenceNumber;
	@SerializedName("channel_id") private String channelId;
	@SerializedName("document_token") private String documentToken;
	private String status;
	private String message;
	@SerializedName("signing_url") private String signingUrl;
	private String info;
	@SerializedName("signing_urls") private Map<String, String> signingUrls;
	
	public String getReferenceNumber() {
		return referenceNumber;
	}
	public void setReferenceNumber(String referenceNumber) {
		this.referenceNumber = referenceNumber;
	}
	public String getChannelId() {
		return channelId;
	}
	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
	public String getDocumentToken() {
		return documentToken;
	}
	public void setDocumentToken(String documentToken) {
		this.documentToken = documentToken;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getSigningUrl() {
		return signingUrl;
	}
	public void setSigningUrl(String signingUrl) {
		this.signingUrl = signingUrl;
	}
	public String getInfo() {
		return info;
	}
	public void setInfo(String info) {
		this.info = info;
	}
	public Map<String, String> getSigningUrls() {
		return signingUrls;
	}
	public void setSigningUrls(Map<String, String> signingUrls) {
		this.signingUrls = signingUrls;
	}
}