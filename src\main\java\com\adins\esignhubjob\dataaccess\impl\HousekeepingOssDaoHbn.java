package com.adins.esignhubjob.dataaccess.impl;

import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.HousekeepingOssDao;
import com.adins.esignhubjob.model.table.MsHousekeepingOss;

@Component
@Transactional
public class HousekeepingOssDaoHbn extends BaseDaoHbn implements HousekeepingOssDao {

    @Override
    @SuppressWarnings("unchecked")
    public List<MsHousekeepingOss> getAllHousekeepingOss() {
        return (List<MsHousekeepingOss>) this.managerDAO.list(
                "from MsHousekeepingOss mho where mho.isActive = '1'", 
                (Map<String, Object>) null).get(Constants.MAP_RESULT_LIST);
    }
}