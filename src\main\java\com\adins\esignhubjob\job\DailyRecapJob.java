package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import com.adins.esignhubjob.BaseJobHandler;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.aliyun.fc.runtime.Context;

public class DailyRecapJob extends BaseJobHandler {

    private static final String SCHEDULER = "SCHEDULER FC";

	@Override
	public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
		
		AuditContext auditContext = new AuditContext(SCHEDULER);
		logicFactory.getSchedulerLogic().dailyRecap(auditContext, context);
	}
    
}
