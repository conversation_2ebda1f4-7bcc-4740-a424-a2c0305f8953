package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_face_verify")
public class TrFaceVerify extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	private long idFaceVerify;
	private AmMsuser amMsuser;
	private TrDocumentD trDocumentD;
	private TrDocumentH trDocumentH;
	private Date liveCheckDate;
	private byte[] userPhoto;
	private String verifyResult;
	
	public TrFaceVerify() {
	}

	public TrFaceVerify(long idFaceVerify, String usrCrt, Date dtmCrt) {
		this.idFaceVerify = idFaceVerify;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}

	public TrFaceVerify(long idFaceVerify, AmMsuser amMsuser, TrDocumentD trDocumentD, TrDocumentH trDocumentH,
			Date liveCheckDate, byte[] userPhoto, String verifyResult, String usrCrt, Date dtmCrt, String usrUpd,
			Date dtmUpd) {
		this.idFaceVerify = idFaceVerify;
		this.amMsuser = amMsuser;
		this.trDocumentD = trDocumentD;
		this.trDocumentH = trDocumentH;
		this.liveCheckDate = liveCheckDate;
		this.userPhoto = userPhoto;
		this.verifyResult = verifyResult;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_face_verify", unique = true, nullable = false)
	public long getIdFaceVerify() {
		return this.idFaceVerify;
	}

	public void setIdFaceVerify(long idFaceVerify) {
		this.idFaceVerify = idFaceVerify;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return this.trDocumentD;
	}

	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_h")
	public TrDocumentH getTrDocumentH() {
		return this.trDocumentH;
	}

	public void setTrDocumentH(TrDocumentH trDocumentH) {
		this.trDocumentH = trDocumentH;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "live_check_date", length = 29)
	public Date getLiveCheckDate() {
		return this.liveCheckDate;
	}

	public void setLiveCheckDate(Date liveCheckDate) {
		this.liveCheckDate = liveCheckDate;
	}

	@Column(name = "user_photo")
	public byte[] getUserPhoto() {
		return this.userPhoto;
	}

	public void setUserPhoto(byte[] userPhoto) {
		this.userPhoto = userPhoto;
	}

	@Column(name = "verify_result", length = 100)
	public String getVerifyResult() {
		return this.verifyResult;
	}

	public void setVerifyResult(String verifyResult) {
		this.verifyResult = verifyResult;
	}
}
