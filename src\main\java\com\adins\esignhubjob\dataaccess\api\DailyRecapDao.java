package com.adins.esignhubjob.dataaccess.api;

import java.util.List;

import com.adins.esignhubjob.model.custom.adins.BalanceTopUpBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceDailyRecap;

public interface DailyRecapDao {
    	int countQtyDailyRecap(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor);
        TrBalanceDailyRecap getDailyRecap(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor);
        void insertTrBalanceDailyRecap(TrBalanceDailyRecap dailyRecap);
        List<BalanceTopUpBean> getBalanceTopUp (MsLov balanceType , MsTenant tenant, MsVendor vendor);
        void updateTrBalanceDailyRecap(TrBalanceDailyRecap dailyRecap);
}
