package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.TrJobUpdatePsreId;
import com.adins.esignhubjob.model.webservice.privy.PrivyRegisterStatusResponse;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

public class UpdatePsreIdJob extends BaseJobHandler {

    private static final String AUDIT = "FC_UPD_PSRE_ID";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String functionInput = IOUtils.toString(inputStream);
        Long idJobUpdatePsreId = Long.valueOf(functionInput);

        TrJobUpdatePsreId jobUpdatePsreId = daoFactory.getJobUpdatePsreIdDao().getJobUpdatePsreId(idJobUpdatePsreId);
        if (null == jobUpdatePsreId) {
            context.getLogger().error("Update ID " + functionInput + " not found");
            return;
        }

        MsVendor vendor = jobUpdatePsreId.getMsVendor();
        if (Constants.VENDOR_CODE_PRIVY.equals(vendor.getVendorCode())) {
            updatePrivyId(jobUpdatePsreId, context);
            return;
        }

        context.getLogger().error("Update ID " + functionInput + ", process stopped (unhandled vendor)");
        processFailedJob(jobUpdatePsreId, context);
    }

    private void updatePrivyId(TrJobUpdatePsreId jobUpdatePsreId, Context context) {
        String privyId = getPrivyId(jobUpdatePsreId, context);
        if (StringUtils.isBlank(privyId)) {
            processFailedJob(jobUpdatePsreId, context);
            return;
        }

        AmMsuser user = jobUpdatePsreId.getAmMsuser();
        MsVendor vendor = jobUpdatePsreId.getMsVendor();
        
        MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUser(user, vendor);
        vendorUser.setVendorRegistrationId(privyId);
        vendorUser.setUsrUpd(AUDIT);
        vendorUser.setDtmUpd(new Date());
        daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vendorUser);

        processSuccessfulJob(jobUpdatePsreId, context);
    }

    private void processFailedJob(TrJobUpdatePsreId jobUpdatePsreId, Context context) {
        jobUpdatePsreId.setRequestStatus((short) 2);
        jobUpdatePsreId.setUsrUpd(AUDIT);
        jobUpdatePsreId.setDtmUpd(new Date());
        daoFactory.getJobUpdatePsreIdDao().updateJobUpdatePsreId(jobUpdatePsreId);
        
        context.getLogger().info(String.format("Request ID %1$s, update failed", jobUpdatePsreId.getRequestId()));
    }

    private void processSuccessfulJob(TrJobUpdatePsreId jobUpdatePsreId, Context context) {
        jobUpdatePsreId.setRequestStatus((short) 3);
        jobUpdatePsreId.setUsrUpd(AUDIT);
        jobUpdatePsreId.setDtmUpd(new Date());
        daoFactory.getJobUpdatePsreIdDao().updateJobUpdatePsreId(jobUpdatePsreId);

        context.getLogger().info(String.format("Request ID %1$s, update successful", jobUpdatePsreId.getRequestId()));
    }

    private String getPrivyId(TrJobUpdatePsreId jobUpdatePsreId, Context context) {
        MsTenant tenant = jobUpdatePsreId.getMsTenant();
        MsVendor vendor = jobUpdatePsreId.getMsVendor();
        MsVendoroftenant vendoroftenant = daoFactory.getVendoroftenantDao().getVendoroftenant(tenant.getTenantCode(), vendor.getVendorCode());

        try {
            boolean success = false;
            int attempts = getCheckNumberOfIteration();
            long delayMs = getCheckDelay();

            while (!success) {
                if (attempts <= 0) {
                    context.getLogger().error(String.format("Request ID %1$s, failed to get Privy ID (Max attempts reached)", jobUpdatePsreId.getRequestId()));
                    return null;
                }

                Thread.sleep(delayMs);
                context.getLogger().info(String.format("Request ID %1$s, %2$s attempt(s) left", jobUpdatePsreId.getRequestId(), attempts));
                PrivyRegisterStatusResponse response = logicFactory.getPrivyLogic().checkRegisterStatus(vendoroftenant, jobUpdatePsreId.getRequestId(), context);
                if (response.getCode() == 200) {
                    return response.getData().getPrivyId();
                }

                attempts -= 1;
            }

            context.getLogger().error(String.format("Request ID %1$s, failed to get Privy ID (Max attempts reached)", jobUpdatePsreId.getRequestId()));
            return null;
        } catch (InterruptedException e) {

            context.getLogger().error(String.format("Request ID %1$s, failed to get Privy ID (Interrupted, re-interrupt current thread)", jobUpdatePsreId.getRequestId()));
            Thread.currentThread().interrupt();
            return null;

        } catch (Exception e) {

            context.getLogger().error(String.format("Request ID %1$s, failed to get Privy ID (Exception occurred)", jobUpdatePsreId.getRequestId()));
            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);
            return null;
            
        }
        
    }

    private long getCheckDelay() {
        String delayValue = System.getenv(Constants.ENV_VAR_PRIVY_CHECK_REG_STATUS_DELAY_MS);
        try {
            return Long.valueOf(delayValue);
        } catch (Exception e) {
            return 500L;
        }
        
    }

    private int getCheckNumberOfIteration() {
        String iterationValue = System.getenv(Constants.ENV_VAR_PRIVY_CHECK_REG_STATUS_ITERATION);
        try {
            return Integer.valueOf(iterationValue);
        } catch (Exception e) {
            return 20;
        }
    }
    
}
