package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.table.MsHousekeepingOss;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.aliyun.fc.runtime.Context;

@Component
public class DeleteExpiredFileJob extends BaseJobHandler {

    String deleteFileActive         = System.getenv("ENV_OSS_DELETE_FILE_ACTIVE");

    private static final String[] ERROR_EMAIL_RECEIVERS = { "<EMAIL>" };

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        
        String receiver = daoFactory.getGeneralSettingDao()
                .getGsValueByCode(AmGlobalKey.GS_SEND_EMAIL_PIC_OPERATION_ADINS);

        String[] receivers = receiver.split(";");
        if (StringUtils.isBlank(receivers[0])) {
            receivers = ERROR_EMAIL_RECEIVERS;
        }


        LocalDate currentDate = LocalDate.now();

        List<MsHousekeepingOss> dataOsses = daoFactory.getHousekeepingOss().getAllHousekeepingOss();

        for (MsHousekeepingOss dataOss : dataOsses) {
            try {
                LocalDate newDate = currentDate.minusMonths(Long.parseLong(dataOss.getExpiredValue()) + 1);

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M");

                String formattedDate = newDate.format(formatter);

                String path = String.format("%1$s/%2$s/", dataOss.getPath(), formattedDate);
                
                context.getLogger().info(path);

                if ("1".equals(deleteFileActive)) {
                    logicFactory.getAliyunOssCloudStorageLogic().deleteExpiredRegistrationFolder(path, context);
                }

            } catch (Exception e) {
                Map<String, Object> templateParameters = new HashMap<>();

                context.getLogger().error("Error Delete Expired File : " + e);

                String item = String.format("Error Delete Expired File for month %1$s with error %2$s", currentDate.toString(), e.toString());

                templateParameters.put("items", item);

                MsMsgTemplate template = logicFactory.getMessageTemplateLogic().getAndParseContent(
                        Constants.TEMPLATE_ERROR_DELETE_FILE,
                        templateParameters);

                context.getLogger().info(template.getBody());
                context.getLogger().info("Template retrieved successfully");    

                EmailInformationBean emailInfo = new EmailInformationBean();
                emailInfo.setSubject(template.getSubject());
                emailInfo.setBodyMessage(template.getBody());
                emailInfo.setTo(receivers);
                emailInfo.setImportance(true);
                logicFactory.getEmailSenderLogic().sendEmail(emailInfo, null, context);
            }
        }

        String notes = "Done";

        Date date = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        insertTrSchedulerJob(date, Constants.LOV_CODE_SCHEDULER_MONTHLY, Constants.CODE_LOV_JOB_TYPE_DELETE_EXPIRED_FILE, 1, notes);

    }

    private void insertTrSchedulerJob(Date startTime, String lovSchedulerTypeCode, String lovJobTypeCode,
			long dataProcessed, String notes) {
		MsLov lovSchedulerType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_SCHEDULER_TYPE,
				lovSchedulerTypeCode);
		MsLov lovJobType = daoFactory.getLovDao().getLovByLovGroupAndCode(Constants.LOV_GROUP_JOB_TYPE, lovJobTypeCode);

		TrSchedulerJob schedulerJob = new TrSchedulerJob();
		schedulerJob.setSchedulerStart(startTime);
		schedulerJob.setSchedulerEnd(new Date());
		schedulerJob.setMsLovBySchedulerType(lovSchedulerType);
		schedulerJob.setMsLovByJobType(lovJobType);
		schedulerJob.setDataProcessed(dataProcessed);
		schedulerJob.setNotes(notes);
		schedulerJob.setUsrCrt("SH");
		schedulerJob.setDtmCrt(new Date());
		daoFactory.getSchedulerJobDao().insertSchedulerJob(schedulerJob);
	}

}