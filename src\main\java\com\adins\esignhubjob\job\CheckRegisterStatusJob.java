package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.Constants;
import com.adins.constants.enums.NotificationSendingPoint;
import com.adins.constants.enums.NotificationType;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.RegisterExternalRequestBean;
import com.adins.esignhubjob.model.custom.adins.UserBean;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralRegisterStatusResponseContainer;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrInvitationLink;
import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralRegisterStatusResponse;
import com.adins.util.IOUtils;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class CheckRegisterStatusJob extends BaseJobHandler {

    private static final String AUDIT = "FC_CHECK_REG_STATUS";
    private static final String NOTES_SUCCESSFUL_VERIF = "Verifikasi berhasil";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String input = IOUtils.toString(inputStream);
        Long idJobCheckRegisterStauts = Long.valueOf(input);

        TrJobCheckRegisterStatus checkRegisterStatus = daoFactory.getJobCheckRegisterStatusDao().getJobCheckRegisterStatusNewTran(idJobCheckRegisterStauts);
        if (null == checkRegisterStatus) {
            context.getLogger().error("Job ID " + input + " not found");
            return;
        }

        MsVendor vendor = checkRegisterStatus.getTrBalanceMutation().getMsVendor();
        if (!Constants.VENDOR_CODE_PRIVY.equals(vendor.getVendorCode())) {
            context.getLogger().error("Job ID " + input + ", unhandled vendor " + vendor.getVendorCode());
            return;
        }

        checkRegisterStatusPrivy(checkRegisterStatus, context);
    }

    private void checkRegisterStatusPrivy(TrJobCheckRegisterStatus checkRegisterStatus, Context context) {
        TrBalanceMutation balanceMutation = checkRegisterStatus.getTrBalanceMutation();
        MsTenant tenant = balanceMutation.getMsTenant();
        MsVendor vendor = balanceMutation.getMsVendor();

        MsVendoroftenant vendoroftenant = daoFactory.getVendoroftenantDao().getVendoroftenantNewTran(tenant.getTenantCode(), vendor.getVendorCode());

        try {
            PrivyGeneralRegisterStatusResponseContainer responseContainer = logicFactory.getPrivyGeneralLogic().checkRegisterStatus(checkRegisterStatus, vendoroftenant, context);
            PrivyGeneralRegisterStatusResponse checkResponse = responseContainer.getResponse();
            byte[] registerRequest = logicFactory.getAliyunOssCloudStorageLogic().getRegisterRequest(checkRegisterStatus, context);
            String registerRequestJson = new String(registerRequest);
            boolean isRegisterExternal = "1".equals(checkRegisterStatus.getIsExternal());
            
            if ("registered".equalsIgnoreCase(checkResponse.getData().getStatus())) {
                
                String privyId = checkResponse.getData().getPrivyId();
                AmMsuser user = null;
                if (isRegisterExternal) {
                    RegisterExternalRequestBean bean = gson.fromJson(registerRequestJson, RegisterExternalRequestBean.class);
                    user = logicFactory.getPrivyLogic().insertRegisteredUser(bean, privyId, tenant, vendor, context);
                } else {
                    UserBean bean = gson.fromJson(registerRequestJson, UserBean.class);
                    user = logicFactory.getPrivyLogic().insertInvitationRegisteredUser(bean, privyId, checkRegisterStatus.getLovUserType(), tenant, vendor, context);
                }

                balanceMutation.setNotes(NOTES_SUCCESSFUL_VERIF);
                balanceMutation.setUsrUpd(AUDIT);
                balanceMutation.setDtmUpd(new Date());
                daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(balanceMutation);
                
                checkRegisterStatus.setRequestStatus((short) 3);
                checkRegisterStatus.setUsrUpd(AUDIT);
                checkRegisterStatus.setDtmUpd(new Date());
                daoFactory.getJobCheckRegisterStatusDao().updateJobCheckRegisterStatusNewTran(checkRegisterStatus);
                
                MsLov lovCallbackType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_CALLBACK_TYPE, Constants.LOV_CODE_CALLBACK_TYPE_ACTIVATION_COMPLETE);
                MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserNewTran(user, vendor);

                updateUserAuditTrailData (isRegisterExternal, user, vendorUser, tenant, vendor, context);
                
                // Execute callback to tenant
                logicFactory.getCallbackLogic().executeCallbackToClient(tenant, lovCallbackType, vendorUser, null, null, NOTES_SUCCESSFUL_VERIF, context);

                // Send verification result notif to user
                TrSigningProcessAuditTrail trail = sendSuccessfulVerificationNotif(tenant, vendorUser, isRegisterExternal, context);
                if (null != trail) {
                    logicFactory.getSigningProcessAuditTrailLogic().logProcessRequestResponse(trail, Constants.AUDIT_TRAIL_SUBFOLDER_REGISTER, responseContainer.getRequestBody(), responseContainer.getResponseBody(), false, context);
                }

                return;
            }

            if ("invalid".equalsIgnoreCase(checkResponse.getData().getStatus()) || "rejected".equalsIgnoreCase(checkResponse.getData().getStatus())) {

                String rejectMessage = logicFactory.getPrivyGeneralLogic().getRegisterStatusRejectMessage(checkResponse, context);
                String shortRejectMessage = logicFactory.getPrivyGeneralLogic().getRegisterStatusShortRejectMessage(checkResponse, context);

                balanceMutation.setNotes(rejectMessage);
                balanceMutation.setUsrUpd(AUDIT);
                balanceMutation.setDtmUpd(new Date());
                daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(balanceMutation);

                checkRegisterStatus.setRequestStatus((short) 2);
                checkRegisterStatus.setUsrUpd(AUDIT);
                checkRegisterStatus.setDtmUpd(new Date());
                daoFactory.getJobCheckRegisterStatusDao().updateJobCheckRegisterStatusNewTran(checkRegisterStatus);

                executeFailedVerificationcallback(tenant, registerRequestJson, isRegisterExternal, shortRejectMessage, context);
                
                TrSigningProcessAuditTrail trail = sendFailedVerificationNotif(tenant, registerRequestJson, shortRejectMessage, isRegisterExternal, context);
                
                if (null != trail) {
                    MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_REGISTRATION);
                    TrSigningProcessAuditTrail registerAuditTrail = daoFactory.getSigningProcessAuditTrailDao().getLatestAuditTrailNewTrx(trail.getHashedPhoneNo(), trail.getEmail(), tenant, vendor, lovProcessType);
                    logicFactory.getAliyunOssCloudStorageLogic().deleteZippedApiLogAuditTrail(Constants.AUDIT_TRAIL_SUBFOLDER_REGISTER, registerAuditTrail, context);
                }

                return;
            }

            // Others (waiting status) -> Flagged to retry
            checkRegisterStatus.setRequestStatus((short) 0);
            checkRegisterStatus.setUsrUpd(AUDIT);
            checkRegisterStatus.setDtmUpd(new Date());
            daoFactory.getJobCheckRegisterStatusDao().updateJobCheckRegisterStatusNewTran(checkRegisterStatus);

        } catch (Exception e) {
            context.getLogger().error(String.format("Job ID %1$s, check register status exception: %2$s", checkRegisterStatus.getIdJobCheckRegisterStatus(), e.getLocalizedMessage()));
            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);

            // Flagged to retry
            checkRegisterStatus.setRequestStatus((short) 0);
            checkRegisterStatus.setUsrUpd(AUDIT);
            checkRegisterStatus.setDtmUpd(new Date());
            daoFactory.getJobCheckRegisterStatusDao().updateJobCheckRegisterStatusNewTran(checkRegisterStatus);
        }
    }

    private void updateUserAuditTrailData(boolean isRegisterExternal, AmMsuser user, MsVendorRegisteredUser vendorUser, MsTenant tenant, MsVendor vendor, Context context) {
        if (isRegisterExternal) {
            MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_REGISTRATION);
            TrSigningProcessAuditTrail auditTrail = daoFactory.getSigningProcessAuditTrailDao().getLatestAuditTrailNewTrx(user.getHashedPhone(), user.getLoginId(), tenant, vendor, lovProcessType);
            auditTrail.setAmMsUser(user);
            daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrailNewTrx(auditTrail);
        } else if (!isRegisterExternal) {
            String receiverDetail = null;
            if ("1".equals(vendorUser.getEmailService())) {
                receiverDetail = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
            } else {
                receiverDetail = vendorUser.getSignerRegisteredEmail();
            }
            TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorUser.getMsVendor(), tenant);
            if (null != invitationLink) {
                invitationLink.setAmMsuser(user);
                invitationLink.setDtmUpd(new Date());
                invitationLink.setUsrUpd(context.getRequestId());
                daoFactory.geInvitationLinkDao().updateInvitationLinkNewTrx(invitationLink);
            }
        }
    }

    private void executeFailedVerificationcallback(MsTenant tenant, String registerRequestJson, boolean isRegisterExternal, String rejectMessage, Context context) {
        String email = null;
        String phone = null;
        if (isRegisterExternal) {
            RegisterExternalRequestBean bean = gson.fromJson(registerRequestJson, RegisterExternalRequestBean.class);
            email = bean.getEmail();
            phone = bean.getTlp();
        } else {
            UserBean bean = gson.fromJson(registerRequestJson, UserBean.class);
            email = bean.getEmail();
            phone = bean.getUserPhone();
        }

        logicFactory.getCallbackLogic().executeFailedVerificationCallbackToClient(tenant, email, phone, rejectMessage, context);
    }

    private boolean needToSendVerifNotif(MsTenant tenant) {
        MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, Constants.LOV_CODE_TENANT_SETTING_SEND_PRIVY_VERIF_RESULT_NOTIF);
        return null != settings && "1".equals(settings.getSettingValue());
    }

    private TrSigningProcessAuditTrail sendSuccessfulVerificationNotif(MsTenant tenant, MsVendorRegisteredUser vendorUser, boolean isRegisterExternal, Context context) {
        if (!needToSendVerifNotif(tenant)) {
            context.getLogger().info("Tenant " + tenant.getTenantCode() + " does not send verification result notification");
            return null;
        }

        NotificationType notificationType = logicFactory.getTenantLogic().getNotificationType(tenant, NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY, vendorUser.getEmailService());
        context.getLogger().info("Using notif: " + notificationType.toString());
        if (NotificationType.EMAIL == notificationType) {
            return sendSuccessfulVerificationEmail(tenant, vendorUser, isRegisterExternal, context);
        }

        if (NotificationType.SMS_VFIRST == notificationType) {
            return sendSuccessfulVerificationSmsVfirst(tenant, vendorUser, isRegisterExternal, context);
        }

        if (NotificationType.SMS_JATIS == notificationType) {
            return sendSuccessfulVerificationSmsJatis(tenant, vendorUser, isRegisterExternal, context);
        }

        if (NotificationType.WHATSAPP == notificationType) {
            return sendSuccessfulVerificationWhatsApp(tenant, vendorUser, isRegisterExternal, context);
        }

        if (NotificationType.WHATSAPP_HALOSIS == notificationType) {
            return sendSuccessfulVerificationWhatsAppHalosis(tenant, vendorUser, isRegisterExternal, context);
        }

        return null;
    }

    private TrSigningProcessAuditTrail sendSuccessfulVerificationEmail(MsTenant tenant, MsVendorRegisteredUser vendorUser, boolean isRegisterExternal, Context context) {
        if (isRegisterExternal) {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifEmailExternal(vendorUser, context);
        } else {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifEmailInvitation(tenant, vendorUser, context);
        }

        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);

        TrInvitationLink invitationLink = null;
        if (!isRegisterExternal) {
            String receiverDetail = null;
            if ("1".equals(vendorUser.getEmailService())) {
                receiverDetail = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
            } else {
                receiverDetail = vendorUser.getSignerRegisteredEmail();
            }
            invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorUser.getMsVendor(), tenant);
            if (null != invitationLink) {
                invitationLink.setAmMsuser(vendorUser.getAmMsuser());
                invitationLink.setDtmUpd(new Date());
                invitationLink.setUsrUpd(context.getRequestId());
                daoFactory.geInvitationLinkDao().updateInvitationLinkNewTrx(invitationLink);
            }
        }

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(vendorUser.getPhoneBytea());
        trail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
        trail.setEmail(vendorUser.getSignerRegisteredEmail());
        trail.setAmMsUser(vendorUser.getAmMsuser());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorUser.getMsVendor());
        trail.setNotificationMedia(Constants.NOTIF_TYPE_EMAIL);
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("1");
        trail.setNotes(NOTES_SUCCESSFUL_VERIF);
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;

    }

    private TrSigningProcessAuditTrail sendSuccessfulVerificationSmsVfirst(MsTenant tenant, MsVendorRegisteredUser vendorUser, boolean isRegisterExternal, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_SMS))) {
            return null;
        }

        context.getLogger().info("About to send SMS VFirst");
        if (isRegisterExternal) {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifSmsVfirstExternal(tenant, vendorUser, context);
        } else {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifSmsVfirstInvitation(tenant, vendorUser, context);
        }


        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        MsLov lovSmsGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SMS_GATEWAY, Constants.CODE_LOV_SMS_GATEWAY_VFIRST);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_SMS, tenant, vdr, 1, context);

        TrInvitationLink invitationLink = null;
        if (!isRegisterExternal) {
            String receiverDetail = null;
            if ("1".equals(vendorUser.getEmailService())) {
                receiverDetail = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
            } else {
                receiverDetail = vendorUser.getSignerRegisteredEmail();
            }
            invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorUser.getMsVendor(), tenant);
            if (null != invitationLink) {
                invitationLink.setAmMsuser(vendorUser.getAmMsuser());
                invitationLink.setDtmUpd(new Date());
                invitationLink.setUsrUpd(context.getRequestId());
                daoFactory.geInvitationLinkDao().updateInvitationLinkNewTrx(invitationLink);
            }
        }

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(vendorUser.getPhoneBytea());
        trail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
        trail.setEmail(vendorUser.getSignerRegisteredEmail());
        trail.setAmMsUser(vendorUser.getAmMsuser());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorUser.getMsVendor());
        trail.setNotificationMedia(Constants.NOTIF_TYPE_SMS);
        trail.setNotificationVendor(lovSmsGateway.getDescription());
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("1");
        trail.setNotes(NOTES_SUCCESSFUL_VERIF);
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;
    }

    private TrSigningProcessAuditTrail sendSuccessfulVerificationSmsJatis(MsTenant tenant, MsVendorRegisteredUser vendorUser, boolean isRegisterExternal, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_SMS))) {
            return null;
        }

        context.getLogger().info("About to send SMS Jatis");
        if (isRegisterExternal) {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifSmsJatisExternal(tenant, vendorUser, context);
        } else {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifSmsJatisInvitation(tenant, vendorUser, context);
        }

        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        MsLov lovSmsGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SMS_GATEWAY, Constants.CODE_LOV_SMS_GATEWAY_JATIS);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_SMS, tenant, vdr, 1, context);

        TrInvitationLink invitationLink = null;
        if (!isRegisterExternal) {
            String receiverDetail = null;
            if ("1".equals(vendorUser.getEmailService())) {
                receiverDetail = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
            } else {
                receiverDetail = vendorUser.getSignerRegisteredEmail();
            }
            invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorUser.getMsVendor(), tenant);
            if (null != invitationLink) {
                invitationLink.setAmMsuser(vendorUser.getAmMsuser());
                invitationLink.setDtmUpd(new Date());
                invitationLink.setUsrUpd(context.getRequestId());
                daoFactory.geInvitationLinkDao().updateInvitationLinkNewTrx(invitationLink);
            }
        }

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(vendorUser.getPhoneBytea());
        trail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
        trail.setEmail(vendorUser.getSignerRegisteredEmail());
        trail.setAmMsUser(vendorUser.getAmMsuser());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorUser.getMsVendor());
        trail.setNotificationMedia(Constants.NOTIF_TYPE_SMS);
        trail.setNotificationVendor(lovSmsGateway.getDescription());
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("1");
        trail.setNotes(NOTES_SUCCESSFUL_VERIF);
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;
    }

    private TrSigningProcessAuditTrail sendSuccessfulVerificationWhatsApp(MsTenant tenant, MsVendorRegisteredUser vendorUser, boolean isRegisterExternal, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_WA))) {
            return null;
        }

        if (isRegisterExternal) {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifWhatsAppExternal(tenant, vendorUser, context);
        } else {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifWhatsAppInvitation(tenant, vendorUser, context);
        }

        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        MsLov lovWaGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_WA_GATEWAY, Constants.CODE_LOV_WA_GATEWAY_JATIS);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_WA, tenant, vdr, 1, context);

        TrInvitationLink invitationLink = null;
        if (!isRegisterExternal) {
            String receiverDetail = null;
            if ("1".equals(vendorUser.getEmailService())) {
                receiverDetail = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
            } else {
                receiverDetail = vendorUser.getSignerRegisteredEmail();
            }
            invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorUser.getMsVendor(), tenant);
            if (null != invitationLink) {
                invitationLink.setAmMsuser(vendorUser.getAmMsuser());
                invitationLink.setDtmUpd(new Date());
                invitationLink.setUsrUpd(context.getRequestId());
                daoFactory.geInvitationLinkDao().updateInvitationLinkNewTrx(invitationLink);
            }
        }

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(vendorUser.getPhoneBytea());
        trail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
        trail.setEmail(vendorUser.getSignerRegisteredEmail());
        trail.setAmMsUser(vendorUser.getAmMsuser());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorUser.getMsVendor());
        trail.setNotificationMedia(Constants.NOTIF_TYPE_WA);
        trail.setNotificationVendor(lovWaGateway.getDescription());
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("1");
        trail.setNotes(NOTES_SUCCESSFUL_VERIF);
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;
    }

    private TrSigningProcessAuditTrail sendSuccessfulVerificationWhatsAppHalosis(MsTenant tenant, MsVendorRegisteredUser vendorUser, boolean isRegisterExternal, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_WA))) {
            return null;
        }

        if (isRegisterExternal) {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifHalosisWhatsAppExternal(tenant, vendorUser, context);
        } else {
            logicFactory.getNotificationSenderLogic().sendSuccessfulPrivyVerifHalosisWhatsAppInvitation(tenant, vendorUser, context);
        }

        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        MsLov lovWaGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_WA_GATEWAY, Constants.CODE_LOV_WA_GATEWAY_HALOSIS);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_WA, tenant, vdr, 1, context);

        TrInvitationLink invitationLink = null;
        if (!isRegisterExternal) {
            String receiverDetail = null;
            if ("1".equals(vendorUser.getEmailService())) {
                receiverDetail = logicFactory.getPersonalDataEncryptionLogic().decryptToString(vendorUser.getPhoneBytea());
            } else {
                receiverDetail = vendorUser.getSignerRegisteredEmail();
            }
            invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorUser.getMsVendor(), tenant);
            if (null != invitationLink) {
                invitationLink.setAmMsuser(vendorUser.getAmMsuser());
                invitationLink.setDtmUpd(new Date());
                invitationLink.setUsrUpd(context.getRequestId());
                daoFactory.geInvitationLinkDao().updateInvitationLinkNewTrx(invitationLink);
            }
        }

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(vendorUser.getPhoneBytea());
        trail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
        trail.setEmail(vendorUser.getSignerRegisteredEmail());
        trail.setAmMsUser(vendorUser.getAmMsuser());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorUser.getMsVendor());
        trail.setNotificationMedia(Constants.NOTIF_TYPE_WA);
        trail.setNotificationVendor(lovWaGateway.getDescription());
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("1");
        trail.setNotes(NOTES_SUCCESSFUL_VERIF);
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;
    }

    private TrSigningProcessAuditTrail sendFailedVerificationNotif(MsTenant tenant, String registerRequestJson, String rejectMessage, boolean isRegisterExternal, Context context) {
        if (!needToSendVerifNotif(tenant)) {
            context.getLogger().info("Tenant " + tenant.getTenantCode() + " does not send verification result notification");
            return null;
        }

        String emailService = null;
        if (isRegisterExternal) {
            RegisterExternalRequestBean bean = gson.fromJson(registerRequestJson, RegisterExternalRequestBean.class);
            emailService = (bean.getIdEmailHosting() != null && bean.getIdEmailHosting() != 0) ? "1" :"0";
        } else {
            UserBean bean = gson.fromJson(registerRequestJson, UserBean.class);
            emailService = (bean.getIdEmailHosting() != null && bean.getIdEmailHosting() != 0) ? "1" :"0";
        }

        NotificationType notificationType = logicFactory.getTenantLogic().getNotificationType(tenant, NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY, emailService);
        if (NotificationType.EMAIL == notificationType) {
            return sendFailedVerificationEmail(tenant, registerRequestJson, rejectMessage, isRegisterExternal, context);
        }

        if (NotificationType.SMS_VFIRST == notificationType) {
            return sendFailedVerificationSmsVfirst(tenant, registerRequestJson, rejectMessage, isRegisterExternal, context);
        }

        if (NotificationType.SMS_JATIS == notificationType) {
            return sendFailedVerificationSmsJatis(tenant, registerRequestJson, rejectMessage, isRegisterExternal, context);
        }

        if (NotificationType.WHATSAPP == notificationType) {
            return sendFailedVerificationWhatsApp(tenant, registerRequestJson, rejectMessage, isRegisterExternal, context);
        }

        if (NotificationType.WHATSAPP_HALOSIS == notificationType) {
            return sendFailedVerificationWhatsAppHalosis(tenant, registerRequestJson, rejectMessage, isRegisterExternal, context);
        }

        return null;
    }

    private TrSigningProcessAuditTrail sendFailedVerificationEmail(MsTenant tenant, String registerRequestJson, String rejectMessage, boolean isRegisterExternal, Context context) {
        
        MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_PRIVY);
        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        
        if (isRegisterExternal) {
            RegisterExternalRequestBean bean = gson.fromJson(registerRequestJson, RegisterExternalRequestBean.class);
            logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifEmailExternal(bean, rejectMessage, context);

            byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getTlp());

            TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
            trail.setPhoneNoBytea(encryptedPhone);
            trail.setHashedPhoneNo(Tools.getHashedString(bean.getTlp()));
            trail.setEmail(bean.getEmail());
            trail.setMsTenant(tenant);
            trail.setMsVendor(vendorPsre);
            trail.setNotificationMedia(Constants.NOTIF_TYPE_EMAIL);
            trail.setLovSendingPoint(lovSendingPoint);
            trail.setLovProcessType(lovProcessType);
            trail.setResultStatus("0");
            trail.setNotes(StringUtils.left(rejectMessage, 200));
            trail.setUsrCrt(context.getRequestId());
            trail.setDtmCrt(new Date());
            daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);
            return trail;
        }
        
        UserBean bean = gson.fromJson(registerRequestJson, UserBean.class);
        logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifEmailInvitation(tenant, bean, rejectMessage, context);

        byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getUserPhone());

        String receiverDetail = null;
        String emailService = (bean.getIdEmailHosting() != null && bean.getIdEmailHosting() != 0) ? "1" :"0";
        if ("1".equals(emailService)) {
            receiverDetail = bean.getUserPhone();
        } else {
            receiverDetail = bean.getEmail();
        }
        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorPsre, tenant);

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(encryptedPhone);
        trail.setHashedPhoneNo(Tools.getHashedString(bean.getUserPhone()));
        trail.setEmail(bean.getEmail());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorPsre);
        trail.setNotificationMedia(Constants.NOTIF_TYPE_EMAIL);
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("0");
        trail.setNotes(StringUtils.left(rejectMessage, 200));
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;
    }

    private TrSigningProcessAuditTrail sendFailedVerificationSmsVfirst(MsTenant tenant, String registerRequestJson, String rejectMessage, boolean isRegisterExternal, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_SMS))) {
            return null;
        }

        MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_PRIVY);
        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        MsLov lovSmsGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SMS_GATEWAY, Constants.CODE_LOV_SMS_GATEWAY_VFIRST);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_SMS, tenant, vdr, 1, context);

        if (isRegisterExternal) {
            RegisterExternalRequestBean bean = gson.fromJson(registerRequestJson, RegisterExternalRequestBean.class);
            logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifSmsVfirstExternal(tenant, bean, rejectMessage, context);

            byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getTlp());

            TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
            trail.setPhoneNoBytea(encryptedPhone);
            trail.setHashedPhoneNo(Tools.getHashedString(bean.getTlp()));
            trail.setEmail(bean.getEmail());
            trail.setMsTenant(tenant);
            trail.setMsVendor(vendorPsre);
            trail.setNotificationMedia(Constants.NOTIF_TYPE_SMS);
            trail.setNotificationVendor(lovSmsGateway.getDescription());
            trail.setLovSendingPoint(lovSendingPoint);
            trail.setLovProcessType(lovProcessType);
            trail.setResultStatus("0");
            trail.setNotes(StringUtils.left(rejectMessage, 200));
            trail.setUsrCrt(context.getRequestId());
            trail.setDtmCrt(new Date());
            daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

            return trail;
        }

        UserBean bean = gson.fromJson(registerRequestJson, UserBean.class);
        logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifSmsVfirstInvitation(tenant, bean, rejectMessage, context);

        byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getUserPhone());

        String receiverDetail = null;
        String emailService = (bean.getIdEmailHosting() != null && bean.getIdEmailHosting() != 0) ? "1" :"0";
        if ("1".equals(emailService)) {
            receiverDetail = bean.getUserPhone();
        } else {
            receiverDetail = bean.getEmail();
        }
        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorPsre, tenant);
        
        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(encryptedPhone);
        trail.setHashedPhoneNo(Tools.getHashedString(bean.getUserPhone()));
        trail.setEmail(bean.getEmail());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorPsre);
        trail.setNotificationMedia(Constants.NOTIF_TYPE_SMS);
        trail.setNotificationVendor(lovSmsGateway.getDescription());
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("0");
        trail.setNotes(StringUtils.left(rejectMessage, 200));
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;
    }

    private TrSigningProcessAuditTrail sendFailedVerificationSmsJatis(MsTenant tenant, String registerRequestJson, String rejectMessage, boolean isRegisterExternal, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_SMS))) {
            return null;
        }

        MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_PRIVY);
        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        MsLov lovSmsGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SMS_GATEWAY, Constants.CODE_LOV_SMS_GATEWAY_JATIS);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_SMS, tenant, vdr, 1, context);

        if (isRegisterExternal) {
            RegisterExternalRequestBean bean = gson.fromJson(registerRequestJson, RegisterExternalRequestBean.class);
            logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifSmsJatisExternal(tenant, bean, rejectMessage, context);

            byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getTlp());

            TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
            trail.setPhoneNoBytea(encryptedPhone);
            trail.setHashedPhoneNo(Tools.getHashedString(bean.getTlp()));
            trail.setEmail(bean.getEmail());
            trail.setMsTenant(tenant);
            trail.setMsVendor(vendorPsre);
            trail.setNotificationMedia(Constants.NOTIF_TYPE_SMS);
            trail.setNotificationVendor(lovSmsGateway.getDescription());
            trail.setLovSendingPoint(lovSendingPoint);
            trail.setLovProcessType(lovProcessType);
            trail.setResultStatus("0");
            trail.setNotes(StringUtils.left(rejectMessage, 200));
            trail.setUsrCrt(context.getRequestId());
            trail.setDtmCrt(new Date());
            daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

            return trail;
        }
        
        UserBean bean = gson.fromJson(registerRequestJson, UserBean.class);
        logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifSmsJatisInvitation(tenant, bean, rejectMessage, context);

        byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getUserPhone());

        String receiverDetail = null;
        String emailService = (bean.getIdEmailHosting() != null && bean.getIdEmailHosting() != 0) ? "1" :"0";
        if ("1".equals(emailService)) {
            receiverDetail = bean.getUserPhone();
        } else {
            receiverDetail = bean.getEmail();
        }
        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorPsre, tenant);
        
        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(encryptedPhone);
        trail.setHashedPhoneNo(Tools.getHashedString(bean.getUserPhone()));
        trail.setEmail(bean.getEmail());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorPsre);
        trail.setNotificationMedia(Constants.NOTIF_TYPE_SMS);
        trail.setNotificationVendor(lovSmsGateway.getDescription());
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("0");
        trail.setNotes(StringUtils.left(rejectMessage, 200));
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;
    }

    private TrSigningProcessAuditTrail sendFailedVerificationWhatsApp(MsTenant tenant, String registerRequestJson, String rejectMessage, boolean isRegisterExternal, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_WA))) {
            return null;
        }

        MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_PRIVY);
        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        MsLov lovWaGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_WA_GATEWAY, Constants.CODE_LOV_WA_GATEWAY_JATIS);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_WA, tenant, vdr, 1, context);

        if (isRegisterExternal) {
            RegisterExternalRequestBean bean = gson.fromJson(registerRequestJson, RegisterExternalRequestBean.class);
            logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifWhatsAppExternal(tenant, bean, rejectMessage, context);

            byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getTlp());

            TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
            trail.setPhoneNoBytea(encryptedPhone);
            trail.setHashedPhoneNo(Tools.getHashedString(bean.getTlp()));
            trail.setEmail(bean.getEmail());
            trail.setMsTenant(tenant);
            trail.setMsVendor(vendorPsre);
            trail.setNotificationMedia(Constants.NOTIF_TYPE_WA);
            trail.setNotificationVendor(lovWaGateway.getDescription());
            trail.setLovSendingPoint(lovSendingPoint);
            trail.setLovProcessType(lovProcessType);
            trail.setResultStatus("0");
            trail.setNotes(StringUtils.left(rejectMessage, 200));
            trail.setUsrCrt(context.getRequestId());
            trail.setDtmCrt(new Date());
            daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

            return trail;
        }
        
        UserBean bean = gson.fromJson(registerRequestJson, UserBean.class);
        logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifWhatsAppInvitation(tenant, bean, rejectMessage, context);

        byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getUserPhone());

        String receiverDetail = null;
        String emailService = (bean.getIdEmailHosting() != null && bean.getIdEmailHosting() != 0) ? "1" :"0";
        if ("1".equals(emailService)) {
            receiverDetail = bean.getUserPhone();
        } else {
            receiverDetail = bean.getEmail();
        }
        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorPsre, tenant);

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(encryptedPhone);
        trail.setHashedPhoneNo(Tools.getHashedString(bean.getUserPhone()));
        trail.setEmail(bean.getEmail());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorPsre);
        trail.setNotificationMedia(Constants.NOTIF_TYPE_WA);
        trail.setNotificationVendor(lovWaGateway.getDescription());
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("0");
        trail.setNotes(StringUtils.left(rejectMessage, 200));
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;

    }

    private TrSigningProcessAuditTrail sendFailedVerificationWhatsAppHalosis(MsTenant tenant, String registerRequestJson, String rejectMessage, boolean isRegisterExternal, Context context) {
        if ("1".equals(System.getenv(Constants.ENV_VAR_SKIP_SEND_WA))) {
            return null;
        }

        MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_PRIVY);
        String sendingPointCode = NotificationSendingPoint.JOB_CHECK_REGIS_STATUS_PRIVY.toString();
        MsLov lovSendingPoint = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_NOTIF_SENDING_POINT, sendingPointCode);
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_CHECK_REGIS_STATUS);
        MsLov lovWaGateway = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_WA_GATEWAY, Constants.CODE_LOV_WA_GATEWAY_HALOSIS);

        MsVendor vdr = daoFactory.getVendorDao().getVendorByCode(Constants.VENDOR_CODE_ADINS);

        logicFactory.getBalanceValidatorLogic().validateBalanceAvailabilityWithAmount(Constants.BALANCE_TYPE_CODE_WA, tenant, vdr, 1, context);

        if (isRegisterExternal) {
            RegisterExternalRequestBean bean = gson.fromJson(registerRequestJson, RegisterExternalRequestBean.class);
            logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifHalosisWhatsAppExternal(tenant, bean, rejectMessage, context);

            byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getTlp());

            TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
            trail.setPhoneNoBytea(encryptedPhone);
            trail.setHashedPhoneNo(Tools.getHashedString(bean.getTlp()));
            trail.setEmail(bean.getEmail());
            trail.setMsTenant(tenant);
            trail.setMsVendor(vendorPsre);
            trail.setNotificationMedia(Constants.NOTIF_TYPE_WA);
            trail.setNotificationVendor(lovWaGateway.getDescription());
            trail.setLovSendingPoint(lovSendingPoint);
            trail.setLovProcessType(lovProcessType);
            trail.setResultStatus("0");
            trail.setNotes(StringUtils.left(rejectMessage, 200));
            trail.setUsrCrt(context.getRequestId());
            trail.setDtmCrt(new Date());
            daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

            return trail;
        }
        
        UserBean bean = gson.fromJson(registerRequestJson, UserBean.class);
        logicFactory.getNotificationSenderLogic().sendFailedPrivyVerifHalosisWhatsAppInvitation(tenant, bean, rejectMessage, context);

        byte[] encryptedPhone = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(bean.getUserPhone());

        String receiverDetail = null;
        String emailService = (bean.getIdEmailHosting() != null && bean.getIdEmailHosting() != 0) ? "1" :"0";
        if ("1".equals(emailService)) {
            receiverDetail = bean.getUserPhone();
        } else {
            receiverDetail = bean.getEmail();
        }
        TrInvitationLink invitationLink = daoFactory.geInvitationLinkDao().getInvitationLinkNewTrx(receiverDetail, vendorPsre, tenant);

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(encryptedPhone);
        trail.setHashedPhoneNo(Tools.getHashedString(bean.getUserPhone()));
        trail.setEmail(bean.getEmail());
        trail.setMsTenant(tenant);
        trail.setMsVendor(vendorPsre);
        trail.setNotificationMedia(Constants.NOTIF_TYPE_WA);
        trail.setNotificationVendor(lovWaGateway.getDescription());
        trail.setLovSendingPoint(lovSendingPoint);
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus("0");
        trail.setNotes(StringUtils.left(rejectMessage, 200));
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        trail.setTrInvitationLink(invitationLink);
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        return trail;
    }

}
