package com.adins.esignhubjob.dataaccess.api;

import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;

public interface JobCheckRegisterStatusDao {
    TrJobCheckRegisterStatus getJobCheckRegisterStatus(long idJobCheckRegisterStatus);
    TrJobCheckRegisterStatus getJobCheckRegisterStatusNewTran(long idJobCheckRegisterStatus);
    void updateJobCheckRegisterStatus(TrJobCheckRegisterStatus jobCheckRegisterStatus);
    void updateJobCheckRegisterStatusNewTran(TrJobCheckRegisterStatus jobCheckRegisterStatus);
}
