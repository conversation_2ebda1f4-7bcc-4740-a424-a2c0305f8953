package com.adins.esignhubjob.job;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import com.adins.esignhubjob.BaseJobHandler;
import com.aliyun.fc.runtime.Context;

public class CheckFileListJob extends BaseJobHandler {

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {
        String directoryPath         = System.getenv("DIRECTORY");
        
        context.getLogger().info("Listing files in the root directory...");

        String rootDirectoryPath = File.separator; // This will be "/" on Unix/Linux and "\" on Windows.
        File rootDirectory = new File(rootDirectoryPath);

        checkFileInRootDirectory(rootDirectory, context);

        context.getLogger().info("Listing files in the 'nas' directory...");

        // Specify the directory name
        // String directoryPath = "mnt/ematerai";
        File directory = new File(directoryPath);

        if (directory.exists() && directory.isDirectory()) {
            String[] contents = directory.list();

            if (contents != null && contents.length > 0) {
                context.getLogger().info("List of files and directories in 'nas':");
                for (String content : contents) {
                    context.getLogger().info(content);
                }
            } else {
                context.getLogger().info("'nas' directory is empty.");
            }
        } else {
            context.getLogger().info("The directory 'nas' does not exist or is not a directory.");
        }
    }

    void checkFileInRootDirectory(File rootDirectory, Context context) {
        if (rootDirectory.exists() && rootDirectory.isDirectory()) {
            String[] contents = rootDirectory.list();

            if (contents != null && contents.length > 0) {
                context.getLogger().info("List of files and directories in the root directory:");
                for (String content : contents) {
                    context.getLogger().info(content);
                }
            } else {
                context.getLogger().info("The root directory is empty.");
            }
        } else {
            context.getLogger().info("The root directory does not exist or is not accessible.");
        }
    }
}
