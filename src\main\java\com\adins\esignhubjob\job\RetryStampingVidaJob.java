package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.aliyun.fc.runtime.Context;

public class RetryStampingVidaJob extends BaseJobHandler {

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {

        String maxRetryStampVida = System.getenv(Constants.ENV_VAR_MAX_RETRY_STAMP_VIDA);
        int limit = Integer.parseInt(maxRetryStampVida);
        
        List<Map<String, Object>> idDocumentHs = daoFactory.getDocumentDao().getRetryStampFromUplodaVidaIdDocumentHs(limit);

        if (CollectionUtils.isEmpty(idDocumentHs)) {
            context.getLogger().info("Job does not process any data");
            return;
        }
       
        context.getLogger().info("Job processing " + idDocumentHs.size() + " data");

        for (Map<String,Object> idMap : idDocumentHs) {
            BigInteger idDocumentH = (BigInteger) idMap.get("d0");
            TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByIdDocumentH(idDocumentH.longValue());
            String functionName = "retry_stamp_from_upload_vida";
            String functionParams ="'" + documentH.getRefNumber() + "'" + "," + 72;
            context.getLogger().info("Retrying Stamping for : " + documentH.getRefNumber());
            String functionResult = daoFactory.getCommonDao().hitFunctionInDatabase(functionName, functionParams);
            context.getLogger().info("Retrying Stamping for : " + documentH.getRefNumber() + ", Result : " + functionResult);
        }
    }
}
