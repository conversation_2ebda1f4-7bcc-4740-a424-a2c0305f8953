package com.adins.esignhubjob.model.custom.adins;

import java.util.List;

import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrInvitationLink;

public class SigningProcessAuditTrailBean {
    
    // tr_signing_process_audit_trail param
	private String phone;
	private String email;
	private AmMsuser user;
	private MsTenant tenant;
	private MsVendor vendorPsre;
	// notification_media ditentukan langsung di method pengiriman notif, tidak jadi parameter
	// notification_vendor ditentukan langsung di method pengiriman notif, tidak jadi parameter
	private MsLov lovSendingPoint;
	private String otpCode;
	private MsLov lovProcessType;
	// result_status ditentukan berdasarkan status pengiriman notif, tidak jadi parameter
	private String notes;
	private TrInvitationLink invLink;
	
	// tr_signing_process_audit_trail_detail param
	private List<TrDocumentD> documentDs;
	
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public AmMsuser getUser() {
		return user;
	}
	public void setUser(AmMsuser user) {
		this.user = user;
	}
	public MsTenant getTenant() {
		return tenant;
	}
	public void setTenant(MsTenant tenant) {
		this.tenant = tenant;
	}
	public MsVendor getVendorPsre() {
		return vendorPsre;
	}
	public void setVendorPsre(MsVendor vendorPsre) {
		this.vendorPsre = vendorPsre;
	}
	public MsLov getLovSendingPoint() {
		return lovSendingPoint;
	}
	public void setLovSendingPoint(MsLov lovSendingPoint) {
		this.lovSendingPoint = lovSendingPoint;
	}
	public String getOtpCode() {
		return otpCode;
	}
	public void setOtpCode(String otpCode) {
		this.otpCode = otpCode;
	}
	public MsLov getLovProcessType() {
		return lovProcessType;
	}
	public void setLovProcessType(MsLov lovProcessType) {
		this.lovProcessType = lovProcessType;
	}
	public String getNotes() {
		return notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}
	public TrInvitationLink getInvLink() {
		return invLink;
	}
	public void setInvLink(TrInvitationLink invLink) {
		this.invLink = invLink;
	}
	public List<TrDocumentD> getDocumentDs() {
		return documentDs;
	}
	public void setDocumentDs(List<TrDocumentD> documentDs) {
		this.documentDs = documentDs;
	}
}
