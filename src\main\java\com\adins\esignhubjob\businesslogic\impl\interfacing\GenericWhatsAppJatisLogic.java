package com.adins.esignhubjob.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esignhubjob.businesslogic.api.interfacing.WhatsAppJatisLogic;
import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppProperties;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppRequestBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppTemplateBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppTemplateComponentBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppTemplateComponentParameterBean;
import com.adins.esignhubjob.model.custom.jatis.JatisWhatsAppTemplateLanguageBean;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsMsgTemplate;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrMessageDeliveryReport;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;
import com.adins.esignhubjob.model.webservice.jatis.JatisSendWhatsAppMessageRequest;
import com.adins.esignhubjob.model.webservice.jatis.JatisSendWhatsAppMessageResponse;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
public class GenericWhatsAppJatisLogic extends BaseLogic implements WhatsAppJatisLogic {

	@Autowired private PersonalDataEncryptionLogic encryptionLogic;

    @Override
    public void sendMessageAndCutBalance(JatisWhatsAppRequestBean request, Context context) {
        MsTenant tenant = request.getMsTenant();
        JatisWhatsAppProperties properties = getTenantWhatsAppProperties(tenant, context);
        MsMsgTemplate messageTemplate = request.getTemplate();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
		String reservedTrxNo = request.getReservedTrxNo();
		String recipient = request.getPhoneNumber();

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_WA);
		MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_UWA);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());	
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(request.getMsTenant());
		balanceMutation.setMsVendor(vendor);
		if (null != request.getAmMsuser()) {
			balanceMutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			balanceMutation.setTrDocumentH(request.getTrDocumentH());
			balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			balanceMutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(balanceMutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			balanceMutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = "Sending WhatsApp to " + recipient;
		} else {
			notes = request.getNotes();
		}
		balanceMutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(balanceMutation);

		boolean success = true;
		try {
			JatisSendWhatsAppMessageResponse response = callSendWhatsAppApi(properties, messageTemplate, bodyTexts, buttonText, reservedTrxNo, recipient, request.isRemoveHeader(), context);
			success = response.getError() == null;
		} catch (Exception e) {
			success = false;
			context.getLogger().error("Send WhatsApp exception caught: " + e.getLocalizedMessage());
			context.getLogger().error(ExceptionUtils.getStackTrace(e));
		}

		balanceMutation.setQty(success ? -1 : 0);
		balanceMutation.setUsrUpd(context.getRequestId());
		balanceMutation.setDtmUpd(new Date());
		daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(balanceMutation);
    }

	private boolean needtoCutBalance(MsTenant tenant, String phoneNumber, boolean isOtp) {
		if (isOtp) {
			return true;
		}
		
		TrMessageDeliveryReport report = daoFactory.getMessageDeliveryReportDao().getLatestWhatsAppMessageDeliveryReport(tenant, phoneNumber, "1");
		if (null == report) {
			return true;
		}
		
		Date lastSessionTime = report.getReportTime();
		Date currentTime = new Date();
		long oneDayMillis = 86_400_000L; // 24 * 60 * 60 * 1000
		
		// Cut balance only last session has passed 24 hours
		return (currentTime.getTime() - lastSessionTime.getTime()) >=  oneDayMillis;
	}

    private JatisSendWhatsAppMessageResponse callSendWhatsAppApi(JatisWhatsAppProperties properties, MsMsgTemplate messageTemplate, List<String> bodyTexts, String buttonText, String reservedTrxNo, String recipient, boolean removeHeader, Context context) throws IOException {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		headerMap.put(HttpHeaders.KEY_AUTHORIZATION, HttpHeaders.buildBearerToken(properties.getToken()));
		headerMap.put("x-access-token", properties.getToken());
		Headers headers = Headers.of(headerMap);

		JatisSendWhatsAppMessageRequest request = prepareSendWhatsAppRequest(messageTemplate, bodyTexts, buttonText, reservedTrxNo, recipient, removeHeader);
		String jsonRequest = gson.toJson(request);
		String completeUrl = System.getenv(Constants.ENV_VAR_JATIS_WA_BASE_URL).replace("{x}", properties.getAccountId());

		RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

		Request okHttpRequest = new Request.Builder()
			.headers(headers)
			.url(completeUrl)
			.post(body)
			.build();

		OkHttpClient client = new OkHttpClient.Builder()
			.connectTimeout(20, TimeUnit.SECONDS)
			.readTimeout(60, TimeUnit.SECONDS)
			.build();

		context.getLogger().info("Send WhatsApp URL: " + completeUrl);
		context.getLogger().info("Send WhatsApp request: " + jsonRequest);
		Response response = client.newCall(okHttpRequest).execute();
		String jsonResponse = response.body().string();
		context.getLogger().info("Send WhatsApp response code: " + response.code() + ", body: " + jsonResponse);
		return gson.fromJson(jsonResponse, JatisSendWhatsAppMessageResponse.class);

    }

	private JatisSendWhatsAppMessageRequest prepareSendWhatsAppRequest(MsMsgTemplate messageTemplate, List<String> bodyTexts, String buttonText, String reservedTrxNo, String recipient, boolean removeHeader) {
	
		String phoneNumber = Character.compare(recipient.charAt(0), '0') == 0 ? "+62" + recipient.substring(1) : recipient;
		
		JatisWhatsAppTemplateLanguageBean language = new JatisWhatsAppTemplateLanguageBean();
		language.setPolicy("deterministic");
		language.setCode("id");
		
		List<JatisWhatsAppTemplateComponentBean> components = new ArrayList<>();
		if (!removeHeader) {
			components.add(getHeaderComponent(messageTemplate));
		}
		
		components.add(getBodyComponent(bodyTexts));
		
		if (StringUtils.isNotEmpty(buttonText)) {
			components.add(getButtonComponent(buttonText));
		}
		
		JatisWhatsAppTemplateBean template = new JatisWhatsAppTemplateBean();
		template.setName(messageTemplate.getWaTemplateCode() + System.getenv(Constants.ENV_VAR_JATIS_WA_TEMPLATE_SUFFIX));
		template.setLanguage(language);
		template.setComponents(components);
		
		JatisSendWhatsAppMessageRequest request = new JatisSendWhatsAppMessageRequest();
		request.setXid(reservedTrxNo);
		request.setTo(phoneNumber);
		request.setType("template");
		request.setPreviewUrl(true);
		request.setTemplate(template);
		return request;
	}

	private JatisWhatsAppTemplateComponentBean getHeaderComponent(MsMsgTemplate messageTemplate) {
		List<JatisWhatsAppTemplateComponentParameterBean> headerParameters = new ArrayList<>();
		
		JatisWhatsAppTemplateComponentParameterBean headerParameter = new JatisWhatsAppTemplateComponentParameterBean();
		headerParameter.setType("text");
		headerParameter.setText(messageTemplate.getSubject());
		
		headerParameters.add(headerParameter);
		
		JatisWhatsAppTemplateComponentBean headerComponent = new JatisWhatsAppTemplateComponentBean();
		headerComponent.setType("header");
		headerComponent.setParameters(headerParameters);
		return headerComponent;
	}
	
	private JatisWhatsAppTemplateComponentBean getBodyComponent(List<String> bodyTexts) {
		List<JatisWhatsAppTemplateComponentParameterBean> bodyParameters = new ArrayList<>();
		for (String body : bodyTexts) {
			JatisWhatsAppTemplateComponentParameterBean bodyParameter = new JatisWhatsAppTemplateComponentParameterBean();
			bodyParameter.setType("text");
			bodyParameter.setText(body);
			bodyParameters.add(bodyParameter);
		}
		
		JatisWhatsAppTemplateComponentBean bodyComponent = new JatisWhatsAppTemplateComponentBean();
		bodyComponent.setType("body");
		bodyComponent.setParameters(bodyParameters);
		return bodyComponent;
	}
	
	private JatisWhatsAppTemplateComponentBean getButtonComponent(String buttonText) {
		List<JatisWhatsAppTemplateComponentParameterBean> buttonParameters = new ArrayList<>();
		
		JatisWhatsAppTemplateComponentParameterBean buttonParameter = new JatisWhatsAppTemplateComponentParameterBean();
		buttonParameter.setType("text");
		buttonParameter.setText(buttonText);
		buttonParameters.add(buttonParameter);
		
		JatisWhatsAppTemplateComponentBean buttonComponent = new JatisWhatsAppTemplateComponentBean();
		buttonComponent.setType("button");
		buttonComponent.setIndex(0);
		buttonComponent.setSubType("url");
		buttonComponent.setParameters(buttonParameters);
		return buttonComponent;
	}

    private JatisWhatsAppProperties getTenantWhatsAppProperties(MsTenant tenant, Context context) {
        MsTenantSettings accountIdSetting = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, "WHATSAPP_JATIS_ACCOUNT_ID");
        if (null == accountIdSetting) {
			return getApplicationWhatsAppProperties(tenant, context);
		}

        MsTenantSettings tokenSetting = daoFactory.getTenantSettingsDao().getTenantSettingsNewTrx(tenant, "WHATSAPP_JATIS_TOKEN");
		if (null == tokenSetting) {
			return getApplicationWhatsAppProperties(tenant, context);
		}

        context.getLogger().debug(String.format("Tenant %1$s uses Jatis WhatsApp properties from tenant settings", tenant.getTenantCode()));
        return new JatisWhatsAppProperties(accountIdSetting.getSettingValue(), tokenSetting.getSettingValue());
    }

    private JatisWhatsAppProperties getApplicationWhatsAppProperties(MsTenant tenant, Context context) {
        context.getLogger().debug(String.format("Tenant %1$s uses Jatis WhatsApp properties from application", tenant.getTenantCode()));
        return new JatisWhatsAppProperties(System.getenv(Constants.ENV_VAR_JATIS_WA_ACCOUNT_ID), System.getenv(Constants.ENV_VAR_JATIS_WA_TOKEN));
    }

	@Override
	public void sendMessageAndCutBalance(JatisWhatsAppRequestBean request, SigningProcessAuditTrailBean auditTrailBean, Context context) {
		MsTenant tenant = request.getMsTenant();
        JatisWhatsAppProperties properties = getTenantWhatsAppProperties(tenant, context);
        MsMsgTemplate messageTemplate = request.getTemplate();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
		String reservedTrxNo = request.getReservedTrxNo();
		String recipient = request.getPhoneNumber();

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_WA);
		MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_UWA);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(Constants.VENDOR_CODE_ADINS);

        TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());	
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(StringUtils.left(context.getRequestId(), 36));
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(request.getMsTenant());
		balanceMutation.setMsVendor(vendor);
		if (null != request.getAmMsuser()) {
			balanceMutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			balanceMutation.setTrDocumentH(request.getTrDocumentH());
			balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			balanceMutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(balanceMutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			balanceMutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = "Sending WhatsApp to " + recipient;
		} else {
			notes = request.getNotes();
		}
		balanceMutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(balanceMutation);

		String auditNotes = null;
		boolean success = true;
		try {
			JatisSendWhatsAppMessageResponse response = callSendWhatsAppApi(properties, messageTemplate, bodyTexts, buttonText, reservedTrxNo, recipient, request.isRemoveHeader(), context);
			success = response.getError() == null;
			if (!success) {
				auditNotes = response.getError().getDetail();
			}
		} catch (Exception e) {
			success = false;
			auditNotes = e.getLocalizedMessage();
			context.getLogger().error("Send WhatsApp exception caught: " + e.getLocalizedMessage());
			context.getLogger().error(ExceptionUtils.getStackTrace(e));
		}

		insertAuditTrail(auditTrailBean, success, auditNotes, context);

		balanceMutation.setQty(success ? -1 : 0);
		balanceMutation.setUsrUpd(context.getRequestId());
		balanceMutation.setDtmUpd(new Date());
		daoFactory.getBalanceMutationDao().updateBalanceMutationNewTran(balanceMutation);
	}

	private TrSigningProcessAuditTrail insertAuditTrail(SigningProcessAuditTrailBean auditTrailBean, boolean sendSuccess, String notes, Context context) {
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(encryptionLogic.encryptFromString(auditTrailBean.getPhone()));
		trail.setHashedPhoneNo(Tools.getHashedString(auditTrailBean.getPhone()));
		trail.setEmail(StringUtils.upperCase(auditTrailBean.getEmail()));
		trail.setAmMsUser(auditTrailBean.getUser());
		trail.setMsTenant(auditTrailBean.getTenant());
		trail.setMsVendor(auditTrailBean.getVendorPsre());
		trail.setNotificationMedia("WA");
		trail.setNotificationVendor(daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_WA_GATEWAY, Constants.CODE_LOV_WA_GATEWAY_JATIS).getDescription());
		trail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		trail.setLovProcessType(auditTrailBean.getLovProcessType());
		trail.setResultStatus(sendSuccess ? "1" : "0");
		trail.setNotes(notes);
		trail.setUsrCrt(context.getRequestId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

		if (CollectionUtils.isEmpty(auditTrailBean.getDocumentDs())) {
			return trail;
		}

		for (TrDocumentD documentD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail detail = new TrSigningProcessAuditTrailDetail();
			detail.setSigningProcessAuditTrail(trail);
			detail.setTrDocumentD(documentD);
			detail.setUsrCrt(context.getRequestId());
			detail.setDtmCrt(new Date());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTrx(detail);
		}

		return trail;
	}
    
}
