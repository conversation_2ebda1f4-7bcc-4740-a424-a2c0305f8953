package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.SchedulerJobDao;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.util.Tools;

@Transactional
@Component
public class SchedulerJobDaoHbn extends BaseDaoHbn implements SchedulerJobDao{
    
    @Override
	public void insertSchedulerJob(TrSchedulerJob schedulerJob) {
		schedulerJob.setUsrCrt(Tools.maskData(schedulerJob.getUsrCrt()));
		managerDAO.insert(schedulerJob);	
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertSchedulerJobNewTrx(TrSchedulerJob schedulerJob) {
		schedulerJob.setUsrCrt(Tools.maskData(schedulerJob.getUsrCrt()));
		managerDAO.insert(schedulerJob);	
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrSchedulerJob getSchedulerJobNewTrx(MsTenant tenant, String dateRecap, String jobType, String balanceType) {
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put("dateRecapStart", Tools.formatStringToDate(dateRecap + Constants.SOD_TIME_MILL_SEC, Constants.DATE_TIME_FORMAT_MIL_SEC));
		params.put("dateRecapEnd", Tools.formatStringToDate(dateRecap + Constants.EOD_TIME_MILL_SEC, Constants.DATE_TIME_FORMAT_MIL_SEC));
		params.put("balanceType", StringUtils.upperCase(balanceType));
		params.put("jobType", StringUtils.upperCase(jobType));
		
		return managerDAO.selectOne(
				"from TrSchedulerJob sj "
				+ "join fetch sj.msLovByJobType ljt "
				+ "left join fetch sj.msLovByBalanceType lbt "
				+ "join fetch sj.msLovBySchedulerType lst "
				+ "where ljt.code = :jobType "
				+ "and lbt.code = :balanceType "
				+ "and sj.schedulerStart >= :dateRecapStart "
				+ "and sj.schedulerStart <= :dateRecapEnd "
				+ "and sj.msTenant = :tenant ", params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateSchedulerJobNewTrx(TrSchedulerJob schedulerJob) {
		schedulerJob.setUsrUpd(Tools.maskData(schedulerJob.getUsrUpd()));
		managerDAO.update(schedulerJob);
	}
    
}
