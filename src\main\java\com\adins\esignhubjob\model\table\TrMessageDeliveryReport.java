package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableEntity;

@Entity
@Table(name = "tr_message_delivery_report")
public class TrMessageDeliveryReport extends CreatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	public static final String ID_MESSAGE_DELIVERY_REPORT_HBM = "idMessageDeliveryReport";
	
	private long idMessageDeliveryReport;
	private MsLov msLov;
	private MsLov msLovMessageGateway;
	private MsLov msLovCredentialType;
	private MsLov msLovSendingPoint;
	private MsVendor msVendor;
	private MsTenant msTenant;
	private String trxNo;
	private String recipientDetail;
	private String deliveryStatus;
	private Date reportTime;
	private String vendorTrxNo;
	private Date requestTime;
	
	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_message_delivery_report", unique = true, nullable = false)
	public long getIdMessageDeliveryReport() {
		return idMessageDeliveryReport;
	}
	public void setIdMessageDeliveryReport(long idMessageDeliveryReport) {
		this.idMessageDeliveryReport = idMessageDeliveryReport;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_message_media", nullable = false)
	public MsLov getMsLov() {
		return msLov;
	}
	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return msVendor;
	}
	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return msTenant;
	}
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	@Column(name = "trx_no", length = 45)
	public String getTrxNo() {
		return trxNo;
	}
	public void setTrxNo(String trxNo) {
		this.trxNo = trxNo;
	}
	
	@Column(name = "recipient_detail", length = 36)
	public String getRecipientDetail() {
		return recipientDetail;
	}
	public void setRecipientDetail(String recipientDetail) {
		this.recipientDetail = recipientDetail;
	}
	
	@Column(name = "delivery_status", length = 36)
	public String getDeliveryStatus() {
		return deliveryStatus;
	}
	public void setDeliveryStatus(String deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}
	@Column(name = "report_time", length = 29)
	public Date getReportTime() {
		return reportTime;
	}
	public void setReportTime(Date reportTime) {
		this.reportTime = reportTime;
	}
	
	@Column(name = "vendor_trx_no", length = 45)
	public String getVendorTrxNo() {
		return vendorTrxNo;
	}
	public void setVendorTrxNo(String vendorTrxNo) {
		this.vendorTrxNo = vendorTrxNo;
	}
	
	@Column(name = "request_time", length = 29)
	public Date getRequestTime() {
		return this.requestTime;
	}
	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_message_gateway")
	public MsLov getMsLovMessageGateway() {
		return this.msLovMessageGateway;
	}

	public void setMsLovMessageGateway(MsLov msLovMessageGateway) {
		this.msLovMessageGateway = msLovMessageGateway;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_credential_type")
	public MsLov getMsLovCredentialType() {
		return this.msLovCredentialType;
	}

	public void setMsLovCredentialType(MsLov msLovCredentialType) {
		this.msLovCredentialType = msLovCredentialType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sending_point")
	public MsLov getMsLovSendingPoint() {
		return this.msLovSendingPoint;
	}

	public void setMsLovSendingPoint(MsLov msLovSendingPoint) {
		this.msLovSendingPoint = msLovSendingPoint;
	}
}
