package com.adins.esignhubjob.dataaccess.api;

import java.util.List;

import com.adins.esignhubjob.model.table.MsPeruriDocType;

public interface PeruriDocTypeDao {
    MsPeruriDocType getMsPeruriDocTypeByDocId(String peruriDocId);
    void insertMsPeruriDocType(MsPeruriDocType msPeruriDocType);
	void updateMsPeruriDocType(MsPeruriDocType msPeruriDocType);
    void deactiveDocumentNotInDocId(List<String> allperuriDocId, String usrUpd);
    List<MsPeruriDocType> getMsPeruriDocTypeByDocName(String docName);
}
