package com.adins.esignhubjob.businesslogic.api.interfacing;

import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.adins.esignhubjob.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.aliyun.fc.runtime.Context;

public interface SmsJatisLogic {
    /**
	 * 
	 * Insert tr_balance_mutation with <code>Propagation.REQUIRES_NEW</code> transaction.<br>
	 * If database session error occurred, may be needed to get Ms<PERSON>enant, TrDocumentH, TrDocumentD, or AmMsuser with <code>Propagation.REQUIRES_NEW</code> transaction also.
	 * 
	 * @param request - Mandatory to set all request attributes (tenant, phoneNumber, smsMessage, trxNo, isOtpSms)
	 * @param documentH - Optional, if not null, will be used to insert tr_balance_mutation
	 * @param document - Optional, if not null, will be used to insert tr_balance_mutation
	 * @param user - Optional, if not null, will be used to insert tr_balance_mutation
	 * @param notes - Optional, if null, will be filled with "Send SMS to {phone}"
	 * 
	 */
    void sendSmsAndCutBalance(JatisSmsRequestBean request, TrDocumentH documentH, TrDocumentD document, AmMsuser user, String notes, Context context);

	void sendSmsAndCutBalance(JatisSmsRequestBean request, TrDocumentH documentH, TrDocumentD document, AmMsuser user, String notes, SigningProcessAuditTrailBean auditTrail, Context context);
}
