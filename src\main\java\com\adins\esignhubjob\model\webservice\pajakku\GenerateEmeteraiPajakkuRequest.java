package com.adins.esignhubjob.model.webservice.pajakku;

public class GenerateEmeteraiPajakkuRequest {
    private String idfile;
	private boolean isUpload;
	private String namadoc;
	private String namafile;
	private String nilaidoc;
	private boolean snOnly;
	private String nodoc;
	private String tgldoc;
	private String namejidentitas;
	private String noidentitas;
	private String namedipungut;
    
    public String getIdfile() {
        return idfile;
    }
    public void setIdfile(String idfile) {
        this.idfile = idfile;
    }
    public boolean isUpload() {
        return isUpload;
    }
    public void setUpload(boolean isUpload) {
        this.isUpload = isUpload;
    }
    public String getNamadoc() {
        return namadoc;
    }
    public void setNamadoc(String namadoc) {
        this.namadoc = namadoc;
    }
    public String getNamafile() {
        return namafile;
    }
    public void setNamafile(String namafile) {
        this.namafile = namafile;
    }
    public String getNilaidoc() {
        return nilaidoc;
    }
    public void setNilaidoc(String nilaidoc) {
        this.nilaidoc = nilaidoc;
    }
    public boolean isSnOnly() {
        return snOnly;
    }
    public void setSnOnly(boolean snOnly) {
        this.snOnly = snOnly;
    }
    public String getNodoc() {
        return nodoc;
    }
    public void setNodoc(String nodoc) {
        this.nodoc = nodoc;
    }
    public String getTgldoc() {
        return tgldoc;
    }
    public void setTgldoc(String tgldoc) {
        this.tgldoc = tgldoc;
    }
    public String getNamejidentitas() {
        return namejidentitas;
    }
    public void setNamejidentitas(String namejidentitas) {
        this.namejidentitas = namejidentitas;
    }
    public String getNoidentitas() {
        return noidentitas;
    }
    public void setNoidentitas(String noidentitas) {
        this.noidentitas = noidentitas;
    }
    public String getNamedipungut() {
        return namedipungut;
    }
    public void setNamedipungut(String namedipungut) {
        this.namedipungut = namedipungut;
    }
}
