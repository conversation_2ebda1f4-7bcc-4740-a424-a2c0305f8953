package com.adins.esignhubjob.model.table;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;
@Entity
@Table(name = "tr_verify_document_komdigi")
public class TrVerifyDocumentKomdigi extends CreatableAndUpdatableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	private long idVerifyDocumentKomdigiH;
	private TrDocumentD trDocumentD;
	private Date verifyDate;
	private String verifyStatus;
	private String documentStatus;
	private String jsonResponse;
	private String stackTrace;
	public TrVerifyDocumentKomdigi(long idVerifyDocumentKomdigiH, TrDocumentD trDocumentD, Date verifyDate,
			String verifyStatus, String documentStatus, String jsonResponse, String stackTrace,
			String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd) {
		this.idVerifyDocumentKomdigiH = idVerifyDocumentKomdigiH;
		this.trDocumentD = trDocumentD;
		this.verifyDate = verifyDate;
		this.verifyStatus = verifyStatus;
		this.documentStatus = documentStatus;
		this.jsonResponse = jsonResponse;
		this.stackTrace = stackTrace;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}
	public TrVerifyDocumentKomdigi() {
		//TODO Auto-generated constructor stub
	}
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_verify_document_komdigi_h", unique = true, nullable = false)
	public long getIdVerifyDocumentKomdigiH() {
		return this.idVerifyDocumentKomdigiH;
	}
	public void setIdVerifyDocumentKomdigiH(long idVerifyDocumentKomdigiH) {
		this.idVerifyDocumentKomdigiH = idVerifyDocumentKomdigiH;
	}
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return this.trDocumentD;
	}
	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}
	@Column(name = "verify_date", length = 29)
	public Date getVerifyDate() {
		return this.verifyDate;
	}
	public void setVerifyDate(Date verifyDate) {
		this.verifyDate = verifyDate;
	}
	@Column(name = "verify_status", length = 1)
	public String getVerifyStatus() {
		return this.verifyStatus;
	}
	public void setVerifyStatus(String verifyStatus) {
		this.verifyStatus = verifyStatus;
	}
	@Column(name = "document_status")
	public String getDocumentStatus() {
		return this.documentStatus;
	}
	public void setDocumentStatus(String documentStatus) {
		this.documentStatus = documentStatus;
	}
	@Column(name = "json_response")
	public String getJsonResponse() {
		return this.jsonResponse;
	}
	public void setJsonResponse(String jsonResponse) {
		this.jsonResponse = jsonResponse;
	}
	@Column(name = "stack_trace")
	public String getStackTrace() {
		return this.stackTrace;
	}
	public void setStackTrace(String stackTrace) {
		this.stackTrace = stackTrace;
	}
}