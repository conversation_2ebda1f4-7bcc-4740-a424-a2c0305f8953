package com.adins.esignhubjob.model.custom.privygeneral;

import com.google.gson.annotations.SerializedName;

public class PrivyGeneralUploadDocumentBean {
    @SerializedName("document_file") private String documentFile;
    @SerializedName("document_name") private String documentName;
    @SerializedName("sign_process") private String signProcess;
    @SerializedName("barcode_position") private String barcodePosition;

    public String getDocumentFile() {
        return this.documentFile;
    }

    public void setDocumentFile(String documentFile) {
        this.documentFile = documentFile;
    }

    public String getDocumentName() {
        return this.documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public String getSignProcess() {
        return this.signProcess;
    }

    public void setSignProcess(String signProcess) {
        this.signProcess = signProcess;
    }

    public String getBarcodePosition() {
        return this.barcodePosition;
    }

    public void setBarcodePosition(String barcodePosition) {
        this.barcodePosition = barcodePosition;
    }

}
