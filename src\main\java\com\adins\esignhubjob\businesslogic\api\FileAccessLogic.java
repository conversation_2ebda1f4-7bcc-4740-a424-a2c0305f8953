package com.adins.esignhubjob.businesslogic.api;

import java.io.IOException;

import com.adins.esignhubjob.model.table.TrDocumentD;
import com.aliyun.fc.runtime.Context;

public interface FileAccessLogic {
    // On-Premise Peruri, akses ke folder /UNSIGNED/
	void storeBaseStampDocument(byte[] fileContent, TrDocumentD document, Context context) throws IOException;
	byte[] getBaseStampDocument(TrDocumentD document, Context context) throws IOException;
	boolean deleteBaseStampDocument(TrDocumentD document, Context context);

	// On-Premise Peruri, akses ke folder /SIGNED/
	byte[] getStampedDocument(TrDocumentD document, Context context) throws IOException;
	boolean isStampedDocumentExists(TrDocumentD document, Context context);
	boolean deleteStampedDocument(TrDocumentD document, Context context);
	boolean deleteStampedDocumentbckp(TrDocumentD document, Context context);

	// On-Premise Peruri, akses ke folder /STAMP/
	void storeStampQr(byte[] imageContent, String serialNumber, Context context) throws IOException;
	byte[] getStampQr(String serialNumber, Context context) throws IOException;
	boolean deleteStampQr(String serialNumber, Context context);
}
