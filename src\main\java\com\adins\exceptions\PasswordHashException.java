package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;
import com.adins.framework.exception.StatusCodes;

public class PasswordHashException extends AdInsException {
    public PasswordHashException(String message, Throwable cause) {
		super(message, cause);
	}

	public PasswordHashException(String message) {
		super(message);
	}

	public PasswordHashException(Throwable cause) {
		super(cause);
	}

	@Override
	public int getErrorCode() {
		return StatusCodes.PASSWORD_HASH_ERROR;
	}
}
