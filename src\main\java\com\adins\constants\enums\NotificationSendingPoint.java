package com.adins.constants.enums;

public enum NotificationSendingPoint {
    
    // Writing format: {{ENUM_NAME}}("MS_LOV.CODE", {{IS_OTP_NOTIF}})
	
	GEN_INV("GEN_INV", false),
	GEN_INV_MENU("GEN_INV_MENU", false),
	REGEN_INV("REGEN_INV", false),
	RESEND_INV("RESEND_INV", false),
	OTP_ACT("OTP_ACT", true),
	CERT_NOTIF("CERT_NOTIF", false),
	SEND_DOC("SEND_DOC", false),
	MANUAL_SIGN_REQ("MANUAL_SIGN_REQ", false),
	RESEND_SIGN_NOTIF("RESEND_SIGN_NOTIF", false),
	RESEND_SIGN_NOTIF_EMBED_V1("RESEND_SIGN_NOTIF_EMBED_V1", false),
	RESEND_SIGN_NOTIF_EMBED_V2("RESEND_SIGN_NOTIF_EMBED_V2", false),
	OTP_SIGN_NORMAL("OTP_SIGN_NORMAL", true),
	OTP_SIGN_EMBED_V2("OTP_SIGN_EMBED_V2", true),
	OTP_SIGN_EXTERNAL("OTP_SIGN_EXTERNAL", true),
	RESET_PASSWORD("RESET_PASSWORD", true),
	SIGN_COMPLETE_NOTIF_JOB("SIGN_COMPLETE_NOTIF_JOB", false),
	SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER("SIGN_COMPLETE_NOTIF_JOB_DOC_OWNER", false),
	JOB_CHECK_REGIS_STATUS_PRIVY("JOB_CHECK_REGIS_STATUS_PRIVY", false);
	
	private final String lovCode;
	private final boolean isOtpNotification;
	
	private NotificationSendingPoint(String lovCode, boolean isOtpNotification) {
		this.lovCode = lovCode;
		this.isOtpNotification = isOtpNotification;
	}
	
	@Override
	public String toString() {
		return this.lovCode;
	}
	
	public String getLovCode() {
        return lovCode;
    }
	
	public boolean isOtp() {
		return this.isOtpNotification;
	}
	
	public static NotificationSendingPoint getSendingPointByCode(String lovCode) {
		for (NotificationSendingPoint sendingPoint : values()) {
			if (sendingPoint.getLovCode().equals(lovCode)) {
				return sendingPoint;
			}
		}
		
		return null;
	}
}
