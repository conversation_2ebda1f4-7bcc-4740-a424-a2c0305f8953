package com.adins.esignhubjob.businesslogic.api;

import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.aliyun.fc.runtime.Context;

public interface SigningProcessAuditTrailLogic {
    
    /**
	 * Save request and response body in OSS folder audit_log. Log will be saved in zip format.
	 * 
	 * @param trail TrSigningProcessAuditTrail object
	 * @param subfolderName The value of x in audit_log/{x}/{id_signing_process_audit_trail}.zip
	 * @param requestBody
	 * @param responseBody
	 * @param appendExistingLog Set <code>true</code> to append existing log file. Set <code>false</code> to overwrite existing log file.
	 * @param audit
	 */
	String logProcessRequestResponse(TrSigningProcessAuditTrail trail, String subfolderName, String requestBody, String responseBody, boolean appendExistingLog, Context context);
}
