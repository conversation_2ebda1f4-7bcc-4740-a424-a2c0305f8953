package com.adins.esignhubjob.model.custom.privy;

public class PrivyRegisterStatusV2Bean {
    private String privyId;
    private String email;
    private String phone;
    private String processedAt;
    private String userToken;
    private String status;
    private PrivyIdentityBeanV2 identity;
    private PrivyRejectBean reject;
    
    public String getPrivyId() {
        return this.privyId;
    }

    public void setPrivyId(String privyId) {
        this.privyId = privyId;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProcessedAt() {
        return this.processedAt;
    }

    public void setProcessedAt(String processedAt) {
        this.processedAt = processedAt;
    }

    public String getUserToken() {
        return this.userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public PrivyIdentityBeanV2 getIdentity() {
        return this.identity;
    }

    public void setIdentity(PrivyIdentityBeanV2 identity) {
        this.identity = identity;
    }

    public PrivyRejectBean getReject() {
        return this.reject;
    }

    public void setReject(PrivyRejectBean reject) {
        this.reject = reject;
    }

}
