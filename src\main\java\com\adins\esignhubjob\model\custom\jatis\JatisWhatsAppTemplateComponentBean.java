package com.adins.esignhubjob.model.custom.jatis;

import java.util.List;

import com.google.gson.annotations.SerializedName;

public class JatisWhatsAppTemplateComponentBean {
    private String type;
	private Integer index;
	@SerializedName("sub_type") private String subType;
    private List<JatisWhatsAppTemplateComponentParameterBean> parameters;
	
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public Integer getIndex() {
		return index;
	}
	public void setIndex(Integer index) {
		this.index = index;
	}
	public String getSubType() {
		return subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	public List<JatisWhatsAppTemplateComponentParameterBean> getParameters() {
		return parameters;
	}
	public void setParameters(List<JatisWhatsAppTemplateComponentParameterBean> parameters) {
		this.parameters = parameters;
	}
}
