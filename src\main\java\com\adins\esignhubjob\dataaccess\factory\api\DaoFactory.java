package com.adins.esignhubjob.dataaccess.factory.api;

import com.adins.esignhubjob.dataaccess.api.BackgroundProcessFailDao;
import com.adins.esignhubjob.dataaccess.api.BalanceMutationDao;
import com.adins.esignhubjob.dataaccess.api.BalanceTopUpDao;
import com.adins.esignhubjob.dataaccess.api.BalancevendoroftenantDao;
import com.adins.esignhubjob.dataaccess.api.ClientCallbackRequestDao;
import com.adins.esignhubjob.dataaccess.api.CommonDao;
import com.adins.esignhubjob.dataaccess.api.DailyRecapDao;
import com.adins.esignhubjob.dataaccess.api.DocumentDao;
import com.adins.esignhubjob.dataaccess.api.DocumentSigningRequestDao;
import com.adins.esignhubjob.dataaccess.api.EmailDao;
import com.adins.esignhubjob.dataaccess.api.GeneralSettingDao;
import com.adins.esignhubjob.dataaccess.api.HousekeepingOssDao;
import com.adins.esignhubjob.dataaccess.api.InvitationLinkDao;
import com.adins.esignhubjob.dataaccess.api.JobCheckRegisterStatusDao;
import com.adins.esignhubjob.dataaccess.api.JobResultDao;
import com.adins.esignhubjob.dataaccess.api.JobUpdatePsreIdDao;
import com.adins.esignhubjob.dataaccess.api.LovDao;
import com.adins.esignhubjob.dataaccess.api.MessageDeliveryReportDao;
import com.adins.esignhubjob.dataaccess.api.MsgTemplateDao;
import com.adins.esignhubjob.dataaccess.api.NotificationtypeoftenantDao;
import com.adins.esignhubjob.dataaccess.api.OfficeDao;
import com.adins.esignhubjob.dataaccess.api.PeruriDocTypeDao;
import com.adins.esignhubjob.dataaccess.api.ProcessAutosignBmDao;
import com.adins.esignhubjob.dataaccess.api.RegistrationLivenessResultDao;
import com.adins.esignhubjob.dataaccess.api.RoleDao;
import com.adins.esignhubjob.dataaccess.api.SchedulerJobDao;
import com.adins.esignhubjob.dataaccess.api.SigningProcessAuditTrailDao;
import com.adins.esignhubjob.dataaccess.api.StampDutyDao;
import com.adins.esignhubjob.dataaccess.api.TenantDao;
import com.adins.esignhubjob.dataaccess.api.TenantSettingsDao;
import com.adins.esignhubjob.dataaccess.api.UrlForwarderDao;
import com.adins.esignhubjob.dataaccess.api.UserDao;
import com.adins.esignhubjob.dataaccess.api.UseroftenantDao;
import com.adins.esignhubjob.dataaccess.api.VendorDao;
import com.adins.esignhubjob.dataaccess.api.VendorRegisteredUserDao;
import com.adins.esignhubjob.dataaccess.api.VendoroftenantDao;
import com.adins.esignhubjob.dataaccess.api.VerifyDocumentKomdigiDao;

public interface DaoFactory {
    BalanceMutationDao getBalanceMutationDao();
    BalancevendoroftenantDao getBalancevendoroftenantDao();
    ClientCallbackRequestDao getClientCallbackRequestDao();
    CommonDao getCommonDao();
    DailyRecapDao getDailyRecapDao();
    DocumentDao getDocumentDao();
    DocumentSigningRequestDao getDocumentSigningRequestDao();
    EmailDao getEmailDao();
    GeneralSettingDao getGeneralSettingDao();
    JobCheckRegisterStatusDao getJobCheckRegisterStatusDao();
    JobResultDao getJobResultDao();
    JobUpdatePsreIdDao getJobUpdatePsreIdDao();
    LovDao getLovDao();
    MessageDeliveryReportDao getMessageDeliveryReportDao();
    MsgTemplateDao getMsgTemplateDao();
    OfficeDao getOfficeDao();
    PeruriDocTypeDao getPeruriDocTypeDao();
    RegistrationLivenessResultDao getRegistrationLivenessResultDao();
    RoleDao getRoleDao();
    SchedulerJobDao getSchedulerJobDao();
    TenantDao getTenantDao();
    UrlForwarderDao getUrlForwarderDao();
    UserDao getUserDao();
    UseroftenantDao getUseroftenantDao();
    VendorDao getVendorDao();
    VendoroftenantDao getVendoroftenantDao();
    VendorRegisteredUserDao getVendorRegisteredUserDao();
	TenantSettingsDao getTenantSettingsDao();
    NotificationtypeoftenantDao getNotificationtypeoftenantDao();
    InvitationLinkDao geInvitationLinkDao();
    ProcessAutosignBmDao getProcessAutosignBmDao();
    StampDutyDao getStampDutyDao();
    SigningProcessAuditTrailDao getSigningProcessAuditTrailDao();
    HousekeepingOssDao getHousekeepingOss();
    BalanceTopUpDao getBalanceTopUpDao();
    BackgroundProcessFailDao getBackgroundProcessFailDao();
    VerifyDocumentKomdigiDao getVerifyDocumentKomdigiDao();

}
