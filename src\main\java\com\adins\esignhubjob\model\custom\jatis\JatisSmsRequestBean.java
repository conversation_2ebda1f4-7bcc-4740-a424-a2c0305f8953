package com.adins.esignhubjob.model.custom.jatis;

import com.adins.esignhubjob.model.table.MsBusinessLine;
import com.adins.esignhubjob.model.table.MsOffice;
import com.adins.esignhubjob.model.table.MsTenant;

public class JatisSmsRequestBean {
    
    private MsTenant tenant;
	private MsOffice office;
	private MsBusinessLine businessLine;
	private String phoneNumber;
	private String smsMessage;
	private String trxNo;
	private String refNo;
	private boolean isOtpSms;
	
	public JatisSmsRequestBean(MsTenant tenant, String phoneNumber, String smsMessage, String trxNo, boolean isOtpSms) {
		this.tenant = tenant;
		this.phoneNumber = phoneNumber;
		this.smsMessage = smsMessage;
		this.trxNo = trxNo;
		this.isOtpSms = isOtpSms;
	}
	
	public JatisSmsRequestBean(MsTenant tenant, MsOffice office, MsBusinessLine businessLine, String phoneNumber, String smsMessage, String trxNo, String refNo, boolean isOtpSms) {
		this.tenant = tenant;
		this.office = office;
		this.businessLine = businessLine;
		this.phoneNumber = phoneNumber;
		this.smsMessage = smsMessage;
		this.trxNo = trxNo;
		this.refNo = refNo;
		this.isOtpSms = isOtpSms;
	}
	
	public JatisSmsRequestBean(MsTenant tenant, MsOffice office, MsBusinessLine businessLine, String phoneNumber, String smsMessage, String trxNo, boolean isOtpSms) {
		this.tenant = tenant;
		this.office = office;
		this.businessLine = businessLine;
		this.phoneNumber = phoneNumber;
		this.smsMessage = smsMessage;
		this.trxNo = trxNo;
		this.isOtpSms = isOtpSms;
	}
	
	public MsTenant getTenant() {
		return tenant;
	}
	public void setTenant(MsTenant tenant) {
		this.tenant = tenant;
	}
	public MsOffice getOffice() {
		return office;
	}
	public void setOffice(MsOffice office) {
		this.office = office;
	}
	public MsBusinessLine getBusinessLine() {
		return businessLine;
	}
	public void setBusinessLine(MsBusinessLine businessLine) {
		this.businessLine = businessLine;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	public String getSmsMessage() {
		return smsMessage;
	}
	public void setSmsMessage(String smsMessage) {
		this.smsMessage = smsMessage;
	}
	public String getTrxNo() {
		return trxNo;
	}
	public void setTrxNo(String trxNo) {
		this.trxNo = trxNo;
	}
	public String getRefNo() {
		return refNo;
	}
	public void setRefNo(String refNo) {
		this.refNo = refNo;
	}
	public boolean isOtpSms() {
		return isOtpSms;
	}
	public void setOtpSms(boolean isOtpSms) {
		this.isOtpSms = isOtpSms;
	}
}
