package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "am_msprovince")
public class AmMsprovince extends CreatableAndUpdatableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
    public static final String ID_MSPROVINCE_HBM = "idMsprovince";
	public static final String PROVINCE_ID_HBM = "provinceId";
	
	private Long idMsprovince;
	private String provinceName;
	private Long provinceId;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_msprovince", unique = true, nullable = false)
	public Long getIdMsprovince() {
		return idMsprovince;
	}

	public void setIdMsprovince(Long idMsprovince) {
		this.idMsprovince = idMsprovince;
	}
	
	@Column(name = "province_name", length = 70)
	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}
	
	@Column(name = "province_id")
	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
}
