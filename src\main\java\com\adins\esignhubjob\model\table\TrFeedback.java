package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableEntity;

@Entity
@Table(name = "tr_feedback")
public class TrFeedback extends CreatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	private long idFeedback;
	private AmMsuser amMsuser;
	private TrDocumentD trDocumentD;
	private short feedbackValue;
	private String comment;

	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_feedback", unique = true, nullable = false)
	public long getIdFeedback() {
		return this.idFeedback;
	}

	public void setIdFeedback(long idFeedback) {
		this.idFeedback = idFeedback;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_document_d")
	public TrDocumentD getTrDocumentD() {
		return this.trDocumentD;
	}

	public void setTrDocumentD(TrDocumentD trDocumentD) {
		this.trDocumentD = trDocumentD;
	}

	@Column(name = "feedback_value", nullable = false)
	public short getFeedbackValue() {
		return this.feedbackValue;
	}

	public void setFeedbackValue(short feedbackValue) {
		this.feedbackValue = feedbackValue;
	}

	@Column(name = "comment", length = 200)
	public String getComment() {
		return this.comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
}
