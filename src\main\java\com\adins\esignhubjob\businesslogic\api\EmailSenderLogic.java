package com.adins.esignhubjob.businesslogic.api;

import com.adins.esignhubjob.model.custom.adins.EmailAttachmentBean;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.custom.adins.SigningProcessAuditTrailBean;
import com.aliyun.fc.runtime.Context;

public interface EmailSenderLogic {
    void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments, Context context);
    void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments, SigningProcessAuditTrailBean auditTrail, Context context);
}
