package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.JobCheckRegisterStatusDao;
import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;
import com.adins.util.Tools;

@Component
@Transactional
public class JobCheckRegisterStatusDaoHbn extends BaseDaoHbn implements JobCheckRegisterStatusDao {

    @Override
    public TrJobCheckRegisterStatus getJobCheckRegisterStatus(long idJobCheckRegisterStatus) {
        Map<String, Object> params = new HashMap<>();
        params.put("idJobCheckRegisterStatus", idJobCheckRegisterStatus);

        return managerDAO.selectOne(
            "from TrJobCheckRegisterStatus crs "
            + "join fetch crs.trBalanceMutation bm "
            + "join fetch bm.msTenant mt "
            + "join fetch bm.msVendor mv "
            + "where crs.idJobCheckRegisterStatus = :idJobCheckRegisterStatus ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrJobCheckRegisterStatus getJobCheckRegisterStatusNewTran(long idJobCheckRegisterStatus) {
        Map<String, Object> params = new HashMap<>();
        params.put("idJobCheckRegisterStatus", idJobCheckRegisterStatus);

        return managerDAO.selectOne(
            "from TrJobCheckRegisterStatus crs "
            + "join fetch crs.trBalanceMutation bm "
            + "join fetch bm.msTenant mt "
            + "join fetch bm.msVendor mv "
            + "left join fetch crs.lovUserType lut "
            + "where crs.idJobCheckRegisterStatus = :idJobCheckRegisterStatus ", params);
    }

    @Override
    public void updateJobCheckRegisterStatus(TrJobCheckRegisterStatus jobCheckRegisterStatus) {
        jobCheckRegisterStatus.setUsrUpd(Tools.maskData(jobCheckRegisterStatus.getUsrUpd()));
        managerDAO.update(jobCheckRegisterStatus);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateJobCheckRegisterStatusNewTran(TrJobCheckRegisterStatus jobCheckRegisterStatus) {
        jobCheckRegisterStatus.setUsrUpd(Tools.maskData(jobCheckRegisterStatus.getUsrUpd()));
        managerDAO.update(jobCheckRegisterStatus);
    }
    
}
