package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableEntity;

@Entity
@Table(name = "am_userpwdhistory")
public class AmUserpwdhistory extends CreatableEntity implements Serializable{
    private static final long serialVersionUID = 1L;

	private long idUserPwdHistory;
	private AmMsuser amMsuser;
	private MsLov msLov;
	private String password;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_user_pwd_history", unique = true, nullable = false)
	public long getIdUserPwdHistory() {
		return this.idUserPwdHistory;
	}

	public void setIdUserPwdHistory(long idUserPwdHistory) {
		this.idUserPwdHistory = idUserPwdHistory;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_pwd_change_type")
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@Column(name = "password", length = 200)
	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
}
