package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.ActivatableEntity;

@Entity
@Table(name = "am_msmenu")
public class AmMsmenu extends ActivatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private long idMsMenu;
	private String menuCode;
	private Integer menuOrder;
	private String menuPrompt;
	private String path;
	private Long idParentMsMenu;
	private String icon;
	private String css;
	private String isHidden;
	private String isExternalLink;
	private String params;

    private Set<AmMenuofrole> amMenuofroles = new HashSet<>(0);

    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_ms_menu", unique = true, nullable = false)
	public long getIdMsMenu() {
		return this.idMsMenu;
	}

	public void setIdMsMenu(long idMsMenu) {
		this.idMsMenu = idMsMenu;
	}

	@Column(name = "menu_code", length = 20)
	public String getMenuCode() {
		return this.menuCode;
	}

	public void setMenuCode(String menuCode) {
		this.menuCode = menuCode;
	}

	@Column(name = "menu_order")
    public Integer getMenuOrder() {
        return this.menuOrder;
    }

    public void setMenuOrder(Integer menuOrder) {
        this.menuOrder = menuOrder;
    }
	
	@Column(name = "menu_prompt", length = 200)
    public String getMenuPrompt() {
        return this.menuPrompt;
    }

    public void setMenuPrompt(String menuPrompt) {
        this.menuPrompt = menuPrompt;
    }
    
    @Column(name = "path", length = 1000)
    public String getPath() {
        return this.path;
    }

    public void setPath(String path) {
        this.path = path;
    }

	@Column(name = "id_parent_ms_menu")
	public Long getIdParentMsMenu() {
		return this.idParentMsMenu;
	}

	public void setIdParentMsMenu(Long idParentMsMenu) {
		this.idParentMsMenu = idParentMsMenu;
	}
	
	@Column(name = "icon", length = 100)
    public String getIcon() {
        return this.icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
	
	@Column(name = "css", length = 100)
    public String getCss() {
        return this.css;
    }

    public void setCss(String css) {
        this.css = css;
    }
    
	@Column(name = "is_hidden", length = 1)
    public String getIsHidden() {
        return this.isHidden;
    }

    public void setIsHidden(String isHidden) {
        this.isHidden = isHidden;
    }
    
	@Column(name = "is_external_link", length = 1)
    public String getIsExternalLink() {
        return this.isExternalLink;
    }

    public void setIsExternalLink(String isExternalLink) {
        this.isExternalLink = isExternalLink;
    }
    
    @Column(name = "params")
    public String getParams() {
        return this.params;
    }

    public void setParams(String params) {
        this.params = params;
    }
    
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsmenu")
	public Set<AmMenuofrole> getAmMenuofroles() {
		return this.amMenuofroles;
	}

	public void setAmMenuofroles(Set<AmMenuofrole> amMenuofroles) {
		this.amMenuofroles = amMenuofroles;
	}
}
