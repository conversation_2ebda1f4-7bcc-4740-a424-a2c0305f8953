package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.TenantSettingsDao;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;

@Transactional
@Component
public class TenantSettingsDaoHbn extends BaseDaoHbn implements TenantSettingsDao {

	@Override
	public MsTenantSettings getTenantSettings(MsTenant tenant, String lovTenantSettingCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put(MsLov.LOV_GROUP_HBM, Constants.LOV_GROUP_TENANT_SETTING_TYPE);
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(lovTenantSettingCode));
		
		return managerDAO.selectOne(
				"from MsTenantSettings ts "
				+ "join fetch ts.msTenant t "
				+ "join fetch ts.lovSettingType st "
				+ "where ts.msTenant = :tenant "
				+ "and st.lovGroup = :lovGroup "
				+ "and st.code = :code ", params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsTenantSettings getTenantSettingsNewTrx(MsTenant tenant, String lovTenantSettingCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put(MsLov.LOV_GROUP_HBM, Constants.LOV_GROUP_TENANT_SETTING_TYPE);
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(lovTenantSettingCode));
		
		return managerDAO.selectOne(
				"from MsTenantSettings ts "
				+ "join fetch ts.msTenant t "
				+ "join fetch ts.lovSettingType st "
				+ "where ts.msTenant = :tenant "
				+ "and st.lovGroup = :lovGroup "
				+ "and st.code = :code ", params);
	}

	@Override
	public void insertTenantSettings(MsTenantSettings settings) {
		managerDAO.insert(settings);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertTenantSettingsNewTrx(MsTenantSettings settings) {
		managerDAO.insert(settings);
	}

	@Override
	public void updateTenantSettings(MsTenantSettings settings) {
		managerDAO.update(settings);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateTenantSettingsNewTrx(MsTenantSettings settings) {
		managerDAO.update(settings);
	}

}