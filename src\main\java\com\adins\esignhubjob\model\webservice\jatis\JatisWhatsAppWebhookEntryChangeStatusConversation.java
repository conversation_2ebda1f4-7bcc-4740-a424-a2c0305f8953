package com.adins.esignhubjob.model.webservice.jatis;

import com.google.gson.annotations.SerializedName;

public class JatisWhatsAppWebhookEntryChangeStatusConversation {
    @SerializedName("expiration_timestamp") private String expirationTimestamp;
    private String id;
    private Object origin;

    public String getExpirationTimestamp() {
        return this.expirationTimestamp;
    }

    public void setExpirationTimestamp(String expirationTimestamp) {
        this.expirationTimestamp = expirationTimestamp;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Object getOrigin() {
        return this.origin;
    }

    public void setOrigin(Object origin) {
        this.origin = origin;
    }

}
