package com.adins.util;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Base64;

import org.apache.commons.net.PrintCommandListener;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

public class FtpClient {

    private String server;
    private int port;
    private String user;
    private String password;
    private FTPClient ftp;

    public FtpClient(String server, int port, String user, String password) {
        this.server = server;
        this.port = port;
        this.user = user;
        this.password = password;
    }

    public void open() throws IOException {
        ftp = new FTPClient();

        ftp.addProtocolCommandListener(new PrintCommandListener(new PrintWriter(System.out)));

        ftp.connect(server, port);
        int reply = ftp.getReplyCode();
        if (!FTPReply.isPositiveCompletion(reply)) {
            ftp.disconnect();
            throw new IOException("Exception in connecting to FTP Server");
        }
        ftp.enterLocalPassiveMode();
        ftp.setFileType(FTP.BINARY_FILE_TYPE);
        ftp.login(user, password);
    }

    public void close() throws IOException {
        ftp.disconnect();
    }

    public void putFileToPath(String base64File, String path) throws IOException {
        open();
        byte[] decoded = Base64.getDecoder().decode(base64File);
        ftp.storeFile(path, new ByteArrayInputStream(decoded));
        close();
    }

    public String downloadFile(String source) throws IOException {
        open();
        InputStream is = ftp.retrieveFileStream(source);
        byte[] sourceBytes = IOUtils.toByteArray(is);
        String encodedString = Base64.getEncoder().encodeToString(sourceBytes); 
        close();
        return encodedString;
    }

    public void deleteFile(String source) throws IOException {
        open();
        ftp.deleteFile(source);
        close();
    }

    public boolean isFileExist(String source) throws IOException {
        open();
        FTPFile[] remoteFiles = ftp.listFiles(source);
        close();
        return remoteFiles.length > 0;
    }
}
