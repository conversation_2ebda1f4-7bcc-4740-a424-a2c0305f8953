package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.EmailAttachmentBean;
import com.adins.esignhubjob.model.custom.adins.EmailInformationBean;
import com.adins.esignhubjob.model.table.AmGeneralsetting;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class DocumentHousekeepingJob extends BaseJobHandler {

    private static final String AUDIT = "FC_DOC_HOUSEKEEP";
    private static final String DELETING_DOC_DONE_MSG = "Deleting doc with document ID %1$s done";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {

        context.getLogger().info("fetching scheduler type and job type");
        MsLov schedulerType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.CODE_LOV_SCHED_TYPE_DAILY);
        context.getLogger().info(String.format("Scheduler type %s fetched.", schedulerType.getCode()));
        MsLov jobType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_JOB_TYPE, Constants.CODE_LOV_JOB_TYPE_DOCUMENT_HOUSEKEEPING);
        context.getLogger().info(String.format("Job type %s fetched.", jobType.getCode()));
        Date startDate = new Date();
        Long dataProcessed = 0L;

        try {
            context.getLogger().info("Job Housekeeping file OSS Started");

            context.getLogger().info("fetching list of tenants");
            List<MsTenant> tenants = daoFactory.getTenantDao().getActiveTenants();
            for (MsTenant tenant : tenants) {
                context.getLogger().info(String.format("Deleting docs for tenant %1$s", tenant.getTenantCode()));
                dataProcessed += deleteDocumentSignAndStampComplete(tenant, context);
                dataProcessed += deleteDocumentSignCompleteAndStampNotStarted(tenant, context);
                dataProcessed += deleteDocumentSignCompleteAndNoStamp(tenant, context);
                dataProcessed += deleteNotSignedDocument(tenant, context);
            }

            Date endDate = new Date();
           
            context.getLogger().info("scheduler finished, inserting tr_scheduler_job");
            TrSchedulerJob schedulerJob = new TrSchedulerJob();
            schedulerJob.setSchedulerStart(startDate);
            schedulerJob.setSchedulerEnd(endDate);
            schedulerJob.setMsLovBySchedulerType(schedulerType);
            schedulerJob.setMsLovByJobType(jobType);
            schedulerJob.setDataProcessed(dataProcessed);
            schedulerJob.setUsrCrt(AUDIT);
            schedulerJob.setDtmCrt(new Date());
            schedulerJob.setNotes("Done");
            daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
        } catch (Exception e) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode("PIC_OPERATION_ADINS");
            String[] recipient = gs.getGsValue().split(";");
            EmailAttachmentBean[] attachments = null;

			byte[] stackTraceFile = buildStackTraceTextFile(e);
			String filename = buildStackTraceFileName(jobType.getDescription(), schedulerType.getDescription());
			EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
			attachments = new EmailAttachmentBean[] {attachment};

            EmailInformationBean emailBean = new EmailInformationBean();
		    emailBean.setTo(recipient);
		    emailBean.setBodyMessage("There was an error in Job " + schedulerType.getDescription() + " " + jobType.getDescription() + ". Stack Trace is attached on this email.");
		    emailBean.setSubject("Job " + schedulerType.getDescription() + " " + jobType.getDescription());
            logicFactory.getEmailSenderLogic().sendEmail(emailBean, attachments, context);
            context.getLogger().info("Exception Thrown : " + e.getClass().getName() + " " + e.getMessage());

            TrSchedulerJob schedulerJob = new TrSchedulerJob();
            schedulerJob.setSchedulerStart(startDate);
            schedulerJob.setSchedulerEnd(new Date());
            schedulerJob.setMsLovBySchedulerType(schedulerType);
            schedulerJob.setMsLovByJobType(jobType);
            schedulerJob.setDataProcessed(dataProcessed);
            schedulerJob.setUsrCrt(AUDIT);
            schedulerJob.setDtmCrt(new Date());
            schedulerJob.setNotes("Not Done: Exception found");
            daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
        }
    }

    private Long deleteDocumentSignAndStampComplete(MsTenant tenant, Context context) {
        int keepDuration = 1;
        MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_COMPLETED_STAMP_FILE_DURATION);
        if (null == ts || StringUtils.isBlank(ts.getSettingValue())) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_COMPLETED_STAMP_FILE_DURATION);
            keepDuration = Integer.parseInt(gs.getGsValue());
        } else {
            keepDuration = Integer.parseInt(ts.getSettingValue());
        }

        Date lastKeptDocumentDate = DateUtils.addDays(new Date(), -keepDuration);
        context.getLogger().info(String.format("Deleting Docs with complete sign and stamp for tenant %1$s finished before %2$s", tenant.getTenantCode(), lastKeptDocumentDate));
        List<TrDocumentD> docDsToDelete = daoFactory.getDocumentDao().getListSignedAndStampedDocumentDInTenant(tenant.getIdMsTenant(), lastKeptDocumentDate);
        Long dataProcessed = Long.valueOf(docDsToDelete.size());
        context.getLogger().info(String.format("Deleting %1s Docs with complete sign and stamp for tenant %2s started.", docDsToDelete.size(), tenant.getTenantCode()));
        for (TrDocumentD docD : docDsToDelete) {
            context.getLogger().info(String.format("Deleting Doc with Document Id %1$s,  request date : %2$s, completed date : %3$s", docD.getDocumentId(), docD.getRequestDate(), docD.getCompletedDate()));
            TrDocumentH docH = docD.getTrDocumentH();
            logicFactory.getAliyunOssCloudStorageLogic().deleteStampedDocument(docD, context);
            logicFactory.getAliyunOssCloudStorageLogic().deleteManualStamp(docD, context);
            logicFactory.getAliyunOssCloudStorageLogic().deleteStampingDocument(tenant.getTenantCode(), docH.getRefNumber(), docD.getDocumentId(), context);
            logicFactory.getAliyunOssCloudStorageLogic().deleteSignedDocument(docD, context);
            logicFactory.getAliyunOssCloudStorageLogic().deleteBaseSignDocument(docD, context);

            docD.setDocumentDeletedDate(new Date());
            docD.setUsrUpd(AUDIT);
            docD.setDtmUpd(new Date());
            daoFactory.getDocumentDao().updateDocumentDNewTran(docD);
            context.getLogger().info(String.format(DELETING_DOC_DONE_MSG, docD.getDocumentId()));
        }
        context.getLogger().info(String.format("Deleting Docs with complete sign and stamp for tenant %1$s ended.", tenant.getTenantCode()));
        return dataProcessed;
    }

    private Long deleteDocumentSignCompleteAndStampNotStarted(MsTenant tenant, Context context) {
        int keepDuration = 1;
        MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_NOT_COMPLETE_STAMP_FILE_DURATION);
        if (null == ts || StringUtils.isBlank(ts.getSettingValue())) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_NOT_COMPLETE_STAMP_FILE_DURATION);
            keepDuration = Integer.parseInt(gs.getGsValue());
        } else {
            keepDuration = Integer.parseInt(ts.getSettingValue());
        }
        Date lastKeptDocumentDate = DateUtils.addDays(new Date(), -keepDuration);
        context.getLogger().info(String.format("Deleting Docs with complete sign and not stamped for tenant %1$s finished before %2$s", tenant.getTenantCode(), lastKeptDocumentDate));
        List<TrDocumentD> docDsToDelete = daoFactory.getDocumentDao().getListSignedAndNotstartedStampDocumentDInTenant(tenant.getIdMsTenant(), lastKeptDocumentDate);
        context.getLogger().info(String.format("Deleting %1$s Docs with complete sign and not started stamp for tenant %2$s started.", docDsToDelete.size(), tenant.getTenantCode()));
        Long dataProcessed = Long.valueOf(docDsToDelete.size());
        for (TrDocumentD docD : docDsToDelete) {
            context.getLogger().info(String.format("Deleting Doc with Document Id %1$s,  request date : %2$s, completed date : %3$s", docD.getDocumentId(), docD.getRequestDate(), docD.getCompletedDate()));
            TrDocumentH docH = docD.getTrDocumentH();
            logicFactory.getAliyunOssCloudStorageLogic().deleteManualStamp(docD, context);
            logicFactory.getAliyunOssCloudStorageLogic().deleteStampingDocument(tenant.getTenantCode(), docH.getRefNumber(), docD.getDocumentId(), context);
            logicFactory.getAliyunOssCloudStorageLogic().deleteSignedDocument(docD, context);
            logicFactory.getAliyunOssCloudStorageLogic().deleteBaseSignDocument(docD, context);

            docD.setDocumentDeletedDate(new Date());
            docD.setUsrUpd(AUDIT);
            docD.setDtmUpd(new Date());
            daoFactory.getDocumentDao().updateDocumentDNewTran(docD);
            context.getLogger().info(String.format(DELETING_DOC_DONE_MSG, docD.getDocumentId()));
        }
        context.getLogger().info(String.format("Deleting Docs with complete sign and not started stamp for tenant %1$s ended.", tenant.getTenantCode()));
        return dataProcessed;
    }

    private Long deleteDocumentSignCompleteAndNoStamp(MsTenant tenant, Context context) {
        int keepDuration = 1;
        MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_COMPLETED_SIGN_FILE_DURATION);
        if (null == ts || StringUtils.isBlank(ts.getSettingValue())) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_COMPLETED_SIGN_FILE_DURATION);
            keepDuration = Integer.parseInt(gs.getGsValue());
        } else {
            keepDuration = Integer.parseInt(ts.getSettingValue());
        }
        Date lastKeptDocumentDate = DateUtils.addDays(new Date(), -keepDuration);
        context.getLogger().info(String.format("Deleting Docs with complete sign and no stamp for tenant %1$s finished before %2$s", tenant.getTenantCode(), lastKeptDocumentDate));
        List<TrDocumentD> docDsToDelete = daoFactory.getDocumentDao().getListSignedAndNoStampDocumentDInTenant(tenant.getIdMsTenant(), lastKeptDocumentDate);
        context.getLogger().info(String.format("Deleting %1s Docs with complete sign and no stamp for tenant %2$s started.", docDsToDelete.size(), tenant.getTenantCode()));
        Long dataProcessed = Long.valueOf(docDsToDelete.size());
        for (TrDocumentD docD : docDsToDelete) {
            context.getLogger().info(String.format("Deleting Document with Document Id %1$s, completed date : %2$s ", docD.getDocumentId(), docD.getCompletedDate()));
            logicFactory.getAliyunOssCloudStorageLogic().deleteSignedDocument(docD, context);
            logicFactory.getAliyunOssCloudStorageLogic().deleteBaseSignDocument(docD, context);

            docD.setDocumentDeletedDate(new Date());
            docD.setUsrUpd(AUDIT);
            docD.setDtmUpd(new Date());
            daoFactory.getDocumentDao().updateDocumentDNewTran(docD);
            context.getLogger().info(String.format(DELETING_DOC_DONE_MSG, docD.getDocumentId()));
        }
        context.getLogger().info(String.format("Deleting Docs with complete sign and no stamp for tenant %1$s ended.", tenant.getTenantCode()));
        return dataProcessed;
    }

    private Long deleteNotSignedDocument(MsTenant tenant, Context context) {
        int keepDuration = 1;
        MsTenantSettings ts = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_NOT_COMPLETE_SIGN_FILE_DURATION);
        if (null == ts || StringUtils.isBlank(ts.getSettingValue())) {
            AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(Constants.LOV_CODE_TENANT_SETTING_DOCUMENT_NOT_COMPLETE_SIGN_FILE_DURATION);
            keepDuration = Integer.parseInt(gs.getGsValue());
        } else {
            keepDuration = Integer.parseInt(ts.getSettingValue());
        }
        Date lastKeptDocumentDate = DateUtils.addDays(new Date(), -keepDuration);
        context.getLogger().info(String.format("Deleting Docs with incomplete sign and stamp for tenant %1$s finished before %2$s", tenant.getTenantCode(), lastKeptDocumentDate));
        List<TrDocumentD> docDsToDelete = daoFactory.getDocumentDao().getListIncompleteSignAndStampDocumentDInTenant(tenant.getIdMsTenant(), lastKeptDocumentDate);
        context.getLogger().info(String.format("Deleting %1s Docs with no sign started for tenant %2$s started.", docDsToDelete.size(), tenant.getTenantCode()));
        Long dataProcessed = Long.valueOf(docDsToDelete.size());
        for (TrDocumentD docD : docDsToDelete) {
            context.getLogger().info(String.format("Deleting Document with Document Id %1$s, request date : %2$s ", docD.getDocumentId(), docD.getRequestDate()));
            logicFactory.getAliyunOssCloudStorageLogic().deleteBaseSignDocument(docD, context);

            docD.setDocumentDeletedDate(new Date());
            docD.setUsrUpd(AUDIT);
            docD.setDtmUpd(new Date());
            daoFactory.getDocumentDao().updateDocumentDNewTran(docD);
            context.getLogger().info(String.format(DELETING_DOC_DONE_MSG, docD.getDocumentId()));
        }
        context.getLogger().info(String.format("Deleting Docs with no sign started for tenant %1$s ended.", tenant.getTenantCode()));
        return dataProcessed;
    }
    
    private byte[] buildStackTraceTextFile(Exception e) {
		String stackTrace = ExceptionUtils.getStackTrace(e);
		String base64 = Base64.getEncoder().encodeToString(stackTrace.getBytes());
		return Base64.getDecoder().decode(base64);
	}

    private String buildStackTraceFileName(String jobType, String schedulerType) {
		String currentTime = Tools.formatDateToStringIn(new Date(), Constants.DATE_TIME_FORMAT_SEQ);
		StringBuilder filename = new StringBuilder()
			.append(StringUtils.upperCase(jobType)).append("_")
			.append(StringUtils.upperCase(schedulerType)).append("_")
			.append(currentTime)
			.append(".txt");
		return filename.toString();
	}
}
