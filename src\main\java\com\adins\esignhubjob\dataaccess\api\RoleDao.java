package com.adins.esignhubjob.dataaccess.api;

import com.adins.esignhubjob.model.table.AmMemberofrole;
import com.adins.esignhubjob.model.table.AmMsrole;
import com.adins.esignhubjob.model.table.AmMsuser;

public interface RoleDao {
    // am_msrole

    AmMsrole getRoleByCodeAndTenantCode(String roleCode, String tenantCode);
    AmMsrole getRoleByCodeAndTenantCodeNewTran(String roleCode, String tenantCode);

    // am_memberofrole
    void insertMemberOfRole(AmMemberofrole memberOfRole);
	void insertMemberOfRoleNewTran(AmMemberofrole memberOfRole);
    
    AmMemberofrole getMemberofrole(AmMsuser user, AmMsrole role);
	AmMemberofrole getMemberofroleNewTran(AmMsuser user, AmMsrole role);

}
