package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import org.springframework.stereotype.Component;

import com.adins.esignhubjob.BaseJobHandler;
import com.adins.util.IOUtils;
import com.aliyun.fc.runtime.Context;

@Component
public class Dummy<PERSON>ob extends BaseJobHandler {

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String input = IOUtils.toString(inputStream);
        context.getLogger().info(input);
    }
    
}