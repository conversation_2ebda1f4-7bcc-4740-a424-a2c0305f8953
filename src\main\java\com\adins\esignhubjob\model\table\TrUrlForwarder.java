package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_url_forwarder")
public class TrUrlForwarder extends CreatableAndUpdatableEntity implements Serializable  {
    private static final long serialVersionUID = 1L;

    private long idUrlForwarder;
    private String urlCode;
    private String urlLink;
    private MsEmailPattern msEmailPattern;
    private AmMsuser amMsUser;
    
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_url_forwarder", unique = true, nullable = false)
	public long getIdUrlForwarder() {
		return this.idUrlForwarder;
	}

	public void setIdUrlForwarder(long idUrlForwarder) {
		this.idUrlForwarder = idUrlForwarder;
	}

    @Column(name = "url_code", length = 10)
	public String getUrlCode() {
		return this.urlCode;
	}

	public void setUrlCode(String urlCode) {
		this.urlCode = urlCode;
	}

    @Column(name = "url_link", length = 100)
	public String getUrlLink() {
		return this.urlLink;
	}

	public void setUrlLink(String urlLink) {
		this.urlLink = urlLink;
	}
    
    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_email_pattern", nullable = false)
	public MsEmailPattern getMsEmailPattern() {
		return this.msEmailPattern;
	}

	public void setMsEmailPattern(MsEmailPattern msEmailPattern) {
		this.msEmailPattern = msEmailPattern;
	}

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsUser() {
		return this.amMsUser;
	}

	public void setAmMsUser(AmMsuser amMsUser) {
		this.amMsUser = amMsUser;
	}
}
