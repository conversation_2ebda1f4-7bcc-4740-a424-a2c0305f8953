package com.adins.esignhubjob.dataaccess.api;

import java.util.List;

import com.adins.esignhubjob.model.table.MsBalancevendoroftenant;

public interface BalancevendoroftenantDao {
    MsBalancevendoroftenant getBalancevendoroftenant(String tenantCode, String vendorCode, String lovBalanceTypeCode);
    List<MsBalancevendoroftenant> getListBalancevendoroftenant(String vendorCode, String lovBalanceTypeCode);
    List<MsBalancevendoroftenant> getListMsBalancevendoroftenant();
}
