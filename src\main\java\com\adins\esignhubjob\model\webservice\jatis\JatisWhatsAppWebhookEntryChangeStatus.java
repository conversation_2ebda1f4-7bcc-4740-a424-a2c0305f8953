package com.adins.esignhubjob.model.webservice.jatis;

import com.google.gson.annotations.SerializedName;

public class JatisWhatsAppWebhookEntryChangeStatus {
    private JatisWhatsAppWebhookEntryChangeStatusConversation conversation;
    private Object errors;
    private String id;
    private Object pricing;
    @SerializedName("recipient_id") private String recipientId;
    private String status;
    private String timestamp;

    public JatisWhatsAppWebhookEntryChangeStatusConversation getConversation() {
        return this.conversation;
    }

    public void setConversation(JatisWhatsAppWebhookEntryChangeStatusConversation conversation) {
        this.conversation = conversation;
    }

    public Object getErrors() {
        return this.errors;
    }

    public void setErrors(Object errors) {
        this.errors = errors;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Object getPricing() {
        return this.pricing;
    }

    public void setPricing(Object pricing) {
        this.pricing = pricing;
    }

    public String getRecipientId() {
        return this.recipientId;
    }

    public void setRecipientId(String recipientId) {
        this.recipientId = recipientId;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTimestamp() {
        return this.timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

}
