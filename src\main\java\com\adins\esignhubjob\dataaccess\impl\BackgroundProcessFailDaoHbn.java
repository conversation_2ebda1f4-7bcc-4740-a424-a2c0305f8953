package com.adins.esignhubjob.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.BackgroundProcessFailDao;
import com.adins.esignhubjob.model.table.TrBackgroundProcessFail;
import com.adins.util.Tools;

@Transactional
@Component
public class BackgroundProcessFailDaoHbn extends BaseDaoHbn implements BackgroundProcessFailDao {
	@Override
	public void insertTrBackgroundProcessFail(TrBackgroundProcessFail backgroundProcessFail) {
		backgroundProcessFail.setUsrCrt(Tools.maskData(backgroundProcessFail.getUsrCrt()));
		this.managerDAO.insert(backgroundProcessFail);
	}
}
