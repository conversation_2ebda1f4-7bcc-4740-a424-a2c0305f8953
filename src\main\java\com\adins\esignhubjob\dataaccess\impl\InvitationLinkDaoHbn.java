package com.adins.esignhubjob.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.InvitationLinkDao;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.TrInvitationLink;

@Component
@Transactional
public class InvitationLinkDaoHbn extends BaseDaoHbn implements InvitationLinkDao {

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TrInvitationLink getInvitationLinkNewTrx(String receiverDetail, MsVendor vendor, MsTenant tenant) {
        Map<String, Object> params = new HashMap<>();
        params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(receiverDetail));
        params.put("vendor", vendor);
        params.put("tenant", tenant);

        return managerDAO.selectOne(
            "from TrInvitationLink il "
            + "where il.receiverDetail = :receiverDetail "
            + "and il.msVendor = :vendor "
            + "and il.msTenant = :tenant ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateInvitationLinkNewTrx(TrInvitationLink invLink) {
        managerDAO.update(invLink);
    }
    
}
