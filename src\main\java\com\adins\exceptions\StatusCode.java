package com.adins.exceptions;

public class StatusCode {

    private StatusCode() {}
    
    public static final int UNKNOWN = 9999;

    public static final int GENERIC_SYSTEM		= 9_000;
    public static final int INVALID_API_KEY		= GENERIC_SYSTEM + 8;

    public static final int RECON_ERROR         = 1000;
    
    public static final int GENERIC_COMMON_ERROR    = 2_000;
    public static final int MANDATORY_PARAMETER     = GENERIC_COMMON_ERROR + 18;

    public static final int STAMP_DUTY_ERROR            = 5_000;
    public static final int BALANCE_MUTATION_NOT_FOUND	= STAMP_DUTY_ERROR + 8;
    
    public static final int EMETERAI_ERROR		= 5_050;
    
    public static final int INVITATION_LINK_ERROR = 7_000;

    public static final int USER_MANAGEMENT_ERROR       = 10_000;
    public static final int NO_PHONE_IS_NOT_VALID 		= USER_MANAGEMENT_ERROR + 3;
}
