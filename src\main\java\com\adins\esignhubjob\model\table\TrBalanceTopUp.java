package com.adins.esignhubjob.model.table;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "tr_balance_top_up")

public class TrBalanceTopUp extends CreatableAndUpdatableEntity  implements Serializable{
	private static final long serialVersionUID = 1L;
	
	private long idBalanceTopUp;
	private TrBalanceMutation trBalanceMutation;
	private Date expiredDate;
	private String isUsed;
	private Date lastExpiredDate;
	private Integer extendExpiredAttempt;
	private BigDecimal balancePricePerQty;
	
	public TrBalanceTopUp() {
	
	}
	
	public TrBalanceTopUp(long idBalanceTopUp, TrBalanceMutation trBalanceMutation,Date expiredDate,String isUser
			,Date lastExpiredDate,Integer extendExpiredAttempt,BigDecimal balancePricePerQty
			,String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd) {
		this.idBalanceTopUp = idBalanceTopUp;
		this.trBalanceMutation = trBalanceMutation;
		this.expiredDate = expiredDate;
		this.isUsed = isUser;
		this.lastExpiredDate = lastExpiredDate;
		this.extendExpiredAttempt = extendExpiredAttempt;
		this.balancePricePerQty = balancePricePerQty;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
		
	}
	
	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_balance_top_up", unique = true, nullable = false)
	public long getIdBalanceTopUp() {
		return idBalanceTopUp;
	}

	public void setIdBalanceTopUp(long idBalanceTopUp) {
		this.idBalanceTopUp = idBalanceTopUp;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_balance_mutation")
	public TrBalanceMutation getTrBalanceMutation() {
		return trBalanceMutation;
	}


	public void setTrBalanceMutation(TrBalanceMutation trBalanceMutation) {
		this.trBalanceMutation = trBalanceMutation;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "expired_date", length = 29)
	public Date getExpiredDate() {
		return expiredDate;
	}

	public void setExpiredDate(Date expiredDate) {
		this.expiredDate = expiredDate;
	}

	@Column(name = "is_used", length = 1)
	public String getIsUsed() {
		return isUsed;
	}

	public void setIsUsed(String isUsed) {
		this.isUsed = isUsed;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_expired_date", length = 29)
	public Date getLastExpiredDate() {
		return lastExpiredDate;
	}

	public void setLastExpiredDate(Date lastExpiredDate) {
		this.lastExpiredDate = lastExpiredDate;
	}

	@Column(name = "extend_expired_attempt")
	public Integer getExtendExpiredAttempt() {
		return extendExpiredAttempt;
	}

	public void setExtendExpiredAttempt(Integer extendExpiredAttempt) {
		this.extendExpiredAttempt = extendExpiredAttempt;
	}

	
	@Column(name = "balance_price_per_qty")
	public BigDecimal getBalancePricePerQty() {
		return balancePricePerQty;
	}

	public void setBalancePricePerQty(BigDecimal balancePricePerQty) {
		this.balancePricePerQty = balancePricePerQty;
	}
}
