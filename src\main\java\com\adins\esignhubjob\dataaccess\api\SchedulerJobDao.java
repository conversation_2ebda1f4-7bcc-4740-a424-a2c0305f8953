package com.adins.esignhubjob.dataaccess.api;

import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrSchedulerJob;

public interface SchedulerJobDao {
    void insertSchedulerJob(TrSchedulerJob schedulerJob);
    void insertSchedulerJobNewTrx(TrSchedulerJob schedulerJob);
    void updateSchedulerJobNewTrx(TrSchedulerJob schedulerJob);

    TrSchedulerJob getSchedulerJobNewTrx(MsTenant tenant, String dateRecap, String jobType, String balanceType);

}
