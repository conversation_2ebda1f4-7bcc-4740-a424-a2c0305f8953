package com.adins.esignhubjob.model.table.custom;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class ActivatableAndDeletableEntity extends CreatableAndUpdatableEntity {
    public static final String IS_ACTIVE_HBM = "isActive";
    public static final String IS_DELETED_HBM = "isDeleted";

    protected String isActive;
    protected String isDeleted;

    @Column(name = "is_active", length = 1)
	public String getIsActive() {
		return this.isActive;
	}

	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}

    @Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return this.isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
}
