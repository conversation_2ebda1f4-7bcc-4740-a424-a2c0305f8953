package com.adins.esignhubjob.model.table;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.esignhubjob.model.table.custom.CreatableAndUpdatableEntity;

@Entity
@Table(name = "ms_paymentsigntypeoftenant")
public class MsPaymentsigntypeoftenant extends CreatableAndUpdatableEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	private long idMsPaymentsigntypeoftenant;
	private MsLov msLov;
	private MsTenant msTenant;
	private MsVendor msVendor;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_paymentsigntypeoftenant", unique = true, nullable = false)
	public long getIdMsPaymentsigntypeoftenant() {
		return this.idMsPaymentsigntypeoftenant;
	}

	public void setIdMsPaymentsigntypeoftenant(long idMsPaymentsigntypeoftenant) {
		this.idMsPaymentsigntypeoftenant = idMsPaymentsigntypeoftenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_payment_sign_type")
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor")
	public MsVendor getMsVendor() {
		return msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}
}
