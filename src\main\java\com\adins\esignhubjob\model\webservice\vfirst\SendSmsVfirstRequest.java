package com.adins.esignhubjob.model.webservice.vfirst;

import java.util.List;

import com.adins.esignhubjob.model.custom.vfirst.SmsVfirstBean;
import com.adins.esignhubjob.model.custom.vfirst.UserVfirstBean;
import com.google.gson.annotations.SerializedName;

public class SendSmsVfirstRequest {
    
    @SerializedName("@VER") private String ver;
    @SerializedName("SMS") private List<SmsVfirstBean> sms;
    @SerializedName("USER") private UserVfirstBean user;

    public String getVer() {
		return ver;
	}
	public void setVer(String ver) {
		this.ver = ver;
	}
	public List<SmsVfirstBean> getSms() {
		return sms;
	}
	public void setSms(List<SmsVfirstBean> sms) {
		this.sms = sms;
	}
	public UserVfirstBean getUser() {
		return user;
	}
	public void setUser(UserVfirstBean user) {
		this.user = user;
	}
}
