package com.adins.esignhubjob.dataaccess.api;

import java.util.Date;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigi;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigiDetailSign;
import com.adins.esignhubjob.model.table.TrVerifyDocumentKomdigiDetailStamp;

public interface VerifyDocumentKomdigiDao {
    void insertVerifyDocumentKomdigiNewTrx(TrVerifyDocumentKomdigi verifyDocumentKomdigi);
    void insertVerifyDocumentKomdigiDetailSignNewTrx(TrVerifyDocumentKomdigiDetailSign verifyDocumentKomdigiDetailSign);
    void insertVerifyDocumentKomdigiDetailStampNewTrx(TrVerifyDocumentKomdigiDetailStamp verifyDocumentKomdigiDetailStamp);

    int countVerifyDocumentKomdigiByStatusAndDate(String status, Date date);
    int countTotalVerifyDocumentKomdigiByDate(Date date);
}