package com.adins.esignhubjob.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralDocumentHistoryResponseContainer;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralRegisterStatusResponseContainer;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.table.TrJobCheckRegisterStatus;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralDocumentStatusResponse;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralRegisterStatusResponse;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralUploadDocumentResponse;
import com.aliyun.fc.runtime.Context;

public interface PrivyGeneralLogic {
    // Registration
    PrivyGeneralRegisterStatusResponseContainer checkRegisterStatus(TrJobCheckRegisterStatus jobCheckRegisterStatus, MsVendoroftenant vendoroftenant, Context context);
    String getRegisterStatusRejectMessage(PrivyGeneralRegisterStatusResponse response, Context context);
    String getRegisterStatusShortRejectMessage(PrivyGeneralRegisterStatusResponse response, Context context);

    // Sign
    PrivyGeneralDocumentHistoryResponseContainer checkDocumentHistory(TrDocumentSigningRequest signingRequest, MsVendoroftenant vendoroftenant, TrDocumentD document, Context context) throws IOException;
    PrivyGeneralDocumentStatusResponse checkDocumentStatus(TrDocumentSigningRequest signingRequest, MsVendoroftenant vendoroftenant, TrDocumentD document, Context context) throws IOException;

    // Stamping
    PrivyGeneralUploadDocumentResponse uploadDocumentStamping(TrDocumentD document, MsVendoroftenant vendoroftenant, String reservedTrxNo, Context context) throws IOException;
    PrivyGeneralDocumentStatusResponse checkStampingStatus(TrDocumentD document, MsVendoroftenant vendoroftenant, String reservedTrxNo, Context context) throws IOException;

}
